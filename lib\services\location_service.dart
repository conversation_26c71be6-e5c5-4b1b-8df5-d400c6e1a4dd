// lib/services/location_service.dart
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../models/location.dart' as app_location;

class LocationService {
  // 获取当前位置
  Future<app_location.Location> getCurrentLocation() async {
    // 检查位置权限
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        throw Exception('位置权限被拒绝');
      }
    }
    
    if (permission == LocationPermission.deniedForever) {
      throw Exception('位置权限被永久拒绝，请在设置中启用');
    }
    
    // 获取当前位置
    final position = await Geolocator.getCurrentPosition();
    
    // 反向地理编码，获取地址信息
    final placemarks = await placemarkFromCoordinates(
      position.latitude,
      position.longitude,
    );
    
    if (placemarks.isEmpty) {
      throw Exception('无法获取地址信息');
    }
    
    final placemark = placemarks.first;
    
    // 提取国家、省份和城市信息
    String country = placemark.country ?? '中国';
    String province = placemark.administrativeArea ?? '北京';
    String city = placemark.locality ?? placemark.subAdministrativeArea ?? '北京市';
    
    // 创建位置对象
    return app_location.Location(
      country: country,
      province: province,
      city: city,
      latitude: position.latitude,
      longitude: position.longitude,
    );
  }
  
  // 根据地址获取坐标
  Future<app_location.Location> getCoordinatesFromAddress(String country, String province, String city) async {
    try {
      final addresses = await locationFromAddress('$country $province $city');
      
      if (addresses.isEmpty) {
        throw Exception('无法获取坐标信息');
      }
      
      final address = addresses.first;
      
      return app_location.Location(
        country: country,
        province: province,
        city: city,
        latitude: address.latitude,
        longitude: address.longitude,
      );
    } catch (e) {
      // 如果地理编码失败，返回估计坐标
      // 这里使用一个简单的中国城市坐标映射表
      final defaultCoordinates = _getDefaultCoordinates(province, city);
      
      return app_location.Location(
        country: country,
        province: province,
        city: city,
        latitude: defaultCoordinates['latitude'] ?? 39.9042,
        longitude: defaultCoordinates['longitude'] ?? 116.4074,
      );
    }
  }
  
  // 获取默认坐标
  Map<String, double> _getDefaultCoordinates(String province, String city) {
    // 简化版中国主要城市坐标
    final coordinates = {
      '北京': {'latitude': 39.9042, 'longitude': 116.4074},
      '上海': {'latitude': 31.2304, 'longitude': 121.4737},
      '广州': {'latitude': 23.1291, 'longitude': 113.2644},
      '深圳': {'latitude': 22.5431, 'longitude': 114.0579},
      '成都': {'latitude': 30.5728, 'longitude': 104.0668},
      // 其他城市...
    };
    
    return coordinates[city] ?? {'latitude': 35.8617, 'longitude': 104.1954}; // 默认中国中心点
  }
}