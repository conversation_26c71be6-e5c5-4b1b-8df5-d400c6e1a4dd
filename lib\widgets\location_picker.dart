// lib/widgets/location_picker.dart
import 'package:flutter/material.dart';
import '../models/location.dart';

class LocationPicker extends StatefulWidget {
  final Location? initialLocation;
  final ValueChanged<Location> onLocationSelected;
  
  LocationPicker({
    this.initialLocation,
    required this.onLocationSelected,
  });
  
  @override
  _LocationPickerState createState() => _LocationPickerState();
}

class _LocationPickerState extends State<LocationPicker> {
  late String _country;
  late String _province;
  late String _city;
  
  // 中国省份列表
  final List<String> _provinces = [
    '北京', '上海', '天津', '重庆',
    '河北', '山西', '辽宁', '吉林', '黑龙江',
    '江苏', '浙江', '安徽', '福建', '江西', '山东',
    '河南', '湖北', '湖南', '广东', '海南',
    '四川', '贵州', '云南', '陕西', '甘肃',
    '青海', '台湾', '内蒙古', '广西', '西藏',
    '宁夏', '新疆', '香港', '澳门',
  ];
  
  // 城市映射
  final Map<String, List<String>> _cities = {
    '北京': ['北京市'],
    '上海': ['上海市'],
    '天津': ['天津市'],
    '重庆': ['重庆市'],
    '河北': ['石家庄', '唐山', '秦皇岛', '邯郸', '邢台', '保定', '张家口', '承德', '沧州', '廊坊', '衡水'],
    '山西': ['太原', '大同', '阳泉', '长治', '晋城', '朔州', '晋中', '运城', '忻州', '临汾', '吕梁'],
    // 其他省份的城市...
  };
  
  @override
  void initState() {
    super.initState();
    
    if (widget.initialLocation != null) {
      _country = widget.initialLocation!.country;
      _province = widget.initialLocation!.province;
      _city = widget.initialLocation!.city;
    } else {
      _country = '中国';
      _province = '北京';
      _city = '北京市';
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 国家选择（简化版只支持中国）
        TextFormField(
          readOnly: true,
          initialValue: _country,
          decoration: InputDecoration(
            labelText: '国家',
            border: OutlineInputBorder(),
            suffixIcon: Icon(Icons.arrow_drop_down),
          ),
          onTap: () {
            // 暂时只支持中国
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('目前仅支持中国地区')),
            );
          },
        ),
        SizedBox(height: 16),
        
        // 省份选择
        DropdownButtonFormField<String>(
          value: _province,
          decoration: InputDecoration(
            labelText: '省份/直辖市',
            border: OutlineInputBorder(),
          ),
          items: _provinces.map((province) {
            return DropdownMenuItem<String>(
              value: province,
              child: Text(province),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _province = value;
                // 重置城市为该省的第一个城市
                _city = _cities[value]?.first ?? '';
              });
              
              _updateLocation();
            }
          },
        ),
        SizedBox(height: 16),
        
        // 城市选择
        DropdownButtonFormField<String>(
          value: _city,
          decoration: InputDecoration(
            labelText: '城市',
            border: OutlineInputBorder(),
          ),
          items: _cities[_province]?.map((city) {
            return DropdownMenuItem<String>(
              value: city,
              child: Text(city),
            );
          }).toList() ?? [],
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _city = value;
              });
              
              _updateLocation();
            }
          },
        ),
        SizedBox(height: 16),
        
        // 使用当前位置按钮
        ElevatedButton.icon(
          icon: Icon(Icons.my_location),
          label: Text('使用当前位置'),
          onPressed: () {
            _getCurrentLocation();
          },
        ),
      ],
    );
  }
  
  void _updateLocation() {
    final location = Location(
      country: _country,
      province: _province,
      city: _city,
      latitude: 0, // 这里应该通过地理编码服务获取实际坐标
      longitude: 0,
    );
    
    widget.onLocationSelected(location);
  }
  
  void _getCurrentLocation() {
    // 这里应该使用定位服务获取当前位置
    // 简化版本，直接使用北京
    setState(() {
      _province = '北京';
      _city = '北京市';
    });
    
    _updateLocation();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('已设置为北京市（实际应用中将获取真实位置）')),
    );
  }
}



