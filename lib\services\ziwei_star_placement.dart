// lib/services/ziwei_star_placement.dart
// 紫微斗数星曜安星算法

import '../models/ziwei_star.dart';
import '../models/ziwei_palace.dart';
import '../data/ziwei_star_database.dart';

/// 紫微斗数星曜安星服务
class ZiweiStarPlacement {
  
  /// 安主星（十四主星）
  static void placeMainStars(List<ZiweiPalace> palaces, DateTime birthTime, int lifePalaceIndex) {
    // 计算紫微星位置
    int ziweiPosition = _calculateZiweiPosition(birthTime);
    
    // 安紫微星系
    _placeZiweiGroup(palaces, ziweiPosition);
    
    // 安天府星系
    _placeTianfuGroup(palaces, ziweiPosition);
  }
  
  /// 计算紫微星位置
  static int _calculateZiweiPosition(DateTime birthTime) {
    // 根据出生日期计算紫微星位置
    // 这里使用简化算法，实际应该根据农历日期和时辰计算
    
    int day = birthTime.day;
    int hour = birthTime.hour;
    
    // 将时辰转换为地支索引
    int hourBranch = _getHourBranch(hour);
    
    // 紫微星位置计算公式（简化版）
    // 实际算法需要根据农历日期和时辰的复杂公式
    int position = (day + hourBranch) % 12;
    
    return position;
  }
  
  /// 将小时转换为地支索引
  static int _getHourBranch(int hour) {
    // 子时(23-1) = 0, 丑时(1-3) = 1, 寅时(3-5) = 2, ...
    if (hour >= 23 || hour < 1) return 0;  // 子
    if (hour >= 1 && hour < 3) return 1;   // 丑
    if (hour >= 3 && hour < 5) return 2;   // 寅
    if (hour >= 5 && hour < 7) return 3;   // 卯
    if (hour >= 7 && hour < 9) return 4;   // 辰
    if (hour >= 9 && hour < 11) return 5;  // 巳
    if (hour >= 11 && hour < 13) return 6; // 午
    if (hour >= 13 && hour < 15) return 7; // 未
    if (hour >= 15 && hour < 17) return 8; // 申
    if (hour >= 17 && hour < 19) return 9; // 酉
    if (hour >= 19 && hour < 21) return 10; // 戌
    return 11; // 亥
  }
  
  /// 安紫微星系
  static void _placeZiweiGroup(List<ZiweiPalace> palaces, int ziweiPosition) {
    // 紫微星系的相对位置
    Map<String, int> ziweiGroupPositions = {
      '紫微': 0,
      '天机': 1,
      '太阳': 3,
      '武曲': 4,
      '天同': 5,
      '廉贞': 8,
    };
    
    // 安紫微星系各星
    ziweiGroupPositions.forEach((starName, relativePos) {
      int actualPosition = (ziweiPosition + relativePos) % 12;
      ZiweiStar? star = ZiweiStarDatabase.findStarByName(starName);
      if (star != null) {
        StarBrightness brightness = ZiweiStarDatabase.getStarBrightness(starName, actualPosition);
        palaces[actualPosition].addStar(star.copyWithPalace(actualPosition, brightness: brightness));
      }
    });
  }
  
  /// 安天府星系
  static void _placeTianfuGroup(List<ZiweiPalace> palaces, int ziweiPosition) {
    // 天府星位置与紫微星相对
    int tianfuPosition = (ziweiPosition + 6) % 12; // 天府在紫微对宫
    
    // 天府星系的相对位置
    Map<String, int> tianfuGroupPositions = {
      '天府': 0,
      '太阴': 1,
      '贪狼': 2,
      '巨门': 3,
      '天相': 4,
      '天梁': 5,
      '七杀': 6,
      '破军': 8,
    };
    
    // 安天府星系各星
    tianfuGroupPositions.forEach((starName, relativePos) {
      int actualPosition = (tianfuPosition + relativePos) % 12;
      ZiweiStar? star = ZiweiStarDatabase.findStarByName(starName);
      if (star != null) {
        StarBrightness brightness = ZiweiStarDatabase.getStarBrightness(starName, actualPosition);
        palaces[actualPosition].addStar(star.copyWithPalace(actualPosition, brightness: brightness));
      }
    });
  }
  
  /// 安辅星
  static void placeAuxiliaryStars(List<ZiweiPalace> palaces, DateTime birthTime, int lifePalaceIndex) {
    // 安左辅右弼
    _placeZuofuYoubi(palaces, birthTime);
    
    // 安天魁天钺
    _placeTiankuiTianyue(palaces, birthTime);
    
    // 安禄存
    _placeLucun(palaces, birthTime);
    
    // 安天马
    _placeTianma(palaces, birthTime);
    
    // 安擎羊陀罗
    _placeQingyangTuoluo(palaces, birthTime);
    
    // 安火星铃星
    _placeHuoxingLingxing(palaces, birthTime);
    
    // 安地空地劫
    _placeDikongDijie(palaces, birthTime);
  }
  
  /// 安左辅右弼
  static void _placeZuofuYoubi(List<ZiweiPalace> palaces, DateTime birthTime) {
    int month = birthTime.month;
    
    // 左辅位置计算
    int zuofuPos = (month - 1 + 4) % 12; // 简化算法
    ZiweiStar? zuofu = ZiweiStarDatabase.findStarByName('左辅');
    if (zuofu != null) {
      palaces[zuofuPos].addStar(zuofu.copyWithPalace(zuofuPos));
    }
    
    // 右弼位置计算
    int youbiPos = (zuofuPos + 6) % 12; // 右弼在左辅对宫
    ZiweiStar? youbi = ZiweiStarDatabase.findStarByName('右弼');
    if (youbi != null) {
      palaces[youbiPos].addStar(youbi.copyWithPalace(youbiPos));
    }
  }
  
  /// 安天魁天钺
  static void _placeTiankuiTianyue(List<ZiweiPalace> palaces, DateTime birthTime) {
    // 根据年干安天魁天钺
    String yearStem = _getYearStem(birthTime.year);
    
    Map<String, List<int>> kuiyuePositions = {
      '甲': [1, 7],   // 天魁在丑，天钺在未
      '乙': [0, 8],   // 天魁在子，天钺在申
      '丙': [11, 9],  // 天魁在亥，天钺在酉
      '丁': [10, 9],  // 天魁在戌，天钺在酉
      '戊': [1, 7],   // 天魁在丑，天钺在未
      '己': [0, 8],   // 天魁在子，天钺在申
      '庚': [11, 9],  // 天魁在亥，天钺在酉
      '辛': [6, 3],   // 天魁在午，天钺在卯
      '壬': [2, 5],   // 天魁在寅，天钺在巳
      '癸': [4, 1],   // 天魁在辰，天钺在丑
    };
    
    List<int>? positions = kuiyuePositions[yearStem];
    if (positions != null) {
      // 安天魁
      ZiweiStar? tiankui = ZiweiStarDatabase.findStarByName('天魁');
      if (tiankui != null) {
        palaces[positions[0]].addStar(tiankui.copyWithPalace(positions[0]));
      }
      
      // 安天钺
      ZiweiStar? tianyue = ZiweiStarDatabase.findStarByName('天钺');
      if (tianyue != null) {
        palaces[positions[1]].addStar(tianyue.copyWithPalace(positions[1]));
      }
    }
  }
  
  /// 安禄存
  static void _placeLucun(List<ZiweiPalace> palaces, DateTime birthTime) {
    String yearStem = _getYearStem(birthTime.year);
    
    Map<String, int> lucunPositions = {
      '甲': 2,  // 寅
      '乙': 3,  // 卯
      '丙': 5,  // 巳
      '丁': 6,  // 午
      '戊': 5,  // 巳
      '己': 6,  // 午
      '庚': 8,  // 申
      '辛': 9,  // 酉
      '壬': 11, // 亥
      '癸': 0,  // 子
    };
    
    int? position = lucunPositions[yearStem];
    if (position != null) {
      ZiweiStar? lucun = ZiweiStarDatabase.findStarByName('禄存');
      if (lucun != null) {
        palaces[position].addStar(lucun.copyWithPalace(position));
      }
    }
  }
  
  /// 安天马
  static void _placeTianma(List<ZiweiPalace> palaces, DateTime birthTime) {
    // 天马根据年支安星
    int yearBranch = (birthTime.year - 4) % 12;
    
    Map<int, int> tianmaPositions = {
      0: 2,   // 子年天马在寅
      1: 11,  // 丑年天马在亥
      2: 8,   // 寅年天马在申
      3: 7,   // 卯年天马在未
      4: 2,   // 辰年天马在寅
      5: 11,  // 巳年天马在亥
      6: 8,   // 午年天马在申
      7: 7,   // 未年天马在未
      8: 2,   // 申年天马在寅
      9: 11,  // 酉年天马在亥
      10: 8,  // 戌年天马在申
      11: 7,  // 亥年天马在未
    };
    
    int? position = tianmaPositions[yearBranch];
    if (position != null) {
      ZiweiStar? tianma = ZiweiStarDatabase.findStarByName('天马');
      if (tianma != null) {
        palaces[position].addStar(tianma.copyWithPalace(position));
      }
    }
  }
  
  /// 安擎羊陀罗
  static void _placeQingyangTuoluo(List<ZiweiPalace> palaces, DateTime birthTime) {
    // 根据禄存位置安擎羊陀罗
    String yearStem = _getYearStem(birthTime.year);
    
    Map<String, int> lucunPositions = {
      '甲': 2, '乙': 3, '丙': 5, '丁': 6, '戊': 5,
      '己': 6, '庚': 8, '辛': 9, '壬': 11, '癸': 0,
    };
    
    int? lucunPos = lucunPositions[yearStem];
    if (lucunPos != null) {
      // 擎羊在禄存前一位
      int qingyangPos = (lucunPos + 1) % 12;
      ZiweiStar? qingyang = ZiweiStarDatabase.findStarByName('擎羊');
      if (qingyang != null) {
        palaces[qingyangPos].addStar(qingyang.copyWithPalace(qingyangPos));
      }
      
      // 陀罗在禄存后一位
      int tuoluoPos = (lucunPos - 1 + 12) % 12;
      ZiweiStar? tuoluo = ZiweiStarDatabase.findStarByName('陀罗');
      if (tuoluo != null) {
        palaces[tuoluoPos].addStar(tuoluo.copyWithPalace(tuoluoPos));
      }
    }
  }
  
  /// 安火星铃星
  static void _placeHuoxingLingxing(List<ZiweiPalace> palaces, DateTime birthTime) {
    // 根据年支和时支安火星铃星
    int yearBranch = (birthTime.year - 4) % 12;
    int hourBranch = _getHourBranch(birthTime.hour);
    
    // 火星位置计算（简化）
    int huoxingPos = (yearBranch + hourBranch) % 12;
    ZiweiStar? huoxing = ZiweiStarDatabase.findStarByName('火星');
    if (huoxing != null) {
      palaces[huoxingPos].addStar(huoxing.copyWithPalace(huoxingPos));
    }
    
    // 铃星位置计算（简化）
    int lingxingPos = (yearBranch - hourBranch + 12) % 12;
    ZiweiStar? lingxing = ZiweiStarDatabase.findStarByName('铃星');
    if (lingxing != null) {
      palaces[lingxingPos].addStar(lingxing.copyWithPalace(lingxingPos));
    }
  }
  
  /// 安地空地劫
  static void _placeDikongDijie(List<ZiweiPalace> palaces, DateTime birthTime) {
    // 根据年支安地空地劫
    int yearBranch = (birthTime.year - 4) % 12;
    
    // 地空位置
    int dikongPos = (yearBranch + 1) % 12;
    ZiweiStar? dikong = ZiweiStarDatabase.findStarByName('地空');
    if (dikong != null) {
      palaces[dikongPos].addStar(dikong.copyWithPalace(dikongPos));
    }
    
    // 地劫位置
    int dijiePos = (yearBranch - 1 + 12) % 12;
    ZiweiStar? dijie = ZiweiStarDatabase.findStarByName('地劫');
    if (dijie != null) {
      palaces[dijiePos].addStar(dijie.copyWithPalace(dijiePos));
    }
  }
  
  /// 获取年干
  static String _getYearStem(int year) {
    const stems = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    return stems[(year - 4) % 10];
  }
}
