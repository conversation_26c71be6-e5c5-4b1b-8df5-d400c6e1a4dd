// lib/utils/stem_branch_calculator.dart
import '../models/advanced_settings.dart';
import 'solar_terms_calculator.dart';
import '../services/paipan_service.dart';

/// 天干地支计算工具类
/// 实现精确的年月日时天干地支计算，支持高级设置选项
///
/// 版本更新：
/// - 统一日柱计算算法，与paipan_service.dart保持一致
/// - 提高儒略日计算精度，支持时分秒级别的精确计算
/// - 优化子时处理逻辑，确保与高级设置完全兼容
class StemBranchCalculator {
  
  // 十天干
  static const List<String> heavenlyStems = [
    '甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'
  ];
  
  // 十二地支
  static const List<String> earthlyBranches = [
    '子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'
  ];
  
  // 月份地支对应表（农历）
  static const List<String> monthBranches = [
    '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'
  ];
  
  /// 计算年干支
  /// [year] 公历年份
  /// [birthTime] 出生时间（可选，用于立春判断）
  /// [settings] 高级设置（可选）
  /// 返回年干支字符串
  static String getYearStemBranch(int year, {DateTime? birthTime, AdvancedSettings? settings}) {
    settings ??= AdvancedSettings.defaultSettings();

    // 如果设置为以立春为新年起点，且提供了出生时间，使用PaipanService的精确计算
    if (settings.yearPillarStartPoint == YearPillarStartPoint.lichun && birthTime != null) {
      try {
        PaipanService paipanService = PaipanService();
        Map<String, dynamic> ganZhiResult = paipanService.getGanZhi(
          birthTime.year,
          birthTime.month,
          birthTime.day,
          birthTime.hour,
          birthTime.minute,
          birthTime.second
        );

        if (ganZhiResult.isNotEmpty) {
          List<int> tg = ganZhiResult['tg'];
          List<int> dz = ganZhiResult['dz'];
          String result = paipanService.ctg[tg[0]] + paipanService.cdz[dz[0]];
          return result;
        }
      } catch (e) {
        // 如果PaipanService计算失败，使用简化算法
      }
    }

    // 使用简化算法（不考虑立春）
    int actualYear = year;

    // 以公元4年为甲子年起点
    int stemIndex = (actualYear - 4) % 10;
    int branchIndex = (actualYear - 4) % 12;

    // 确保索引为正数
    if (stemIndex < 0) stemIndex += 10;
    if (branchIndex < 0) branchIndex += 12;

    String result = heavenlyStems[stemIndex] + earthlyBranches[branchIndex];

    return result;
  }
  
  /// 计算月干支
  /// [year] 公历年份
  /// [month] 农历月份 (1-12)
  /// [lunarDay] 农历日期（可选，用于闰月处理）
  /// [isLeapMonth] 是否闰月（可选）
  /// [settings] 高级设置（可选）
  /// 返回月干支字符串
  static String getMonthStemBranch(
    int year,
    int month, {
    int? lunarDay,
    bool? isLeapMonth,
    AdvancedSettings? settings,
  }) {
    settings ??= AdvancedSettings.defaultSettings();
    isLeapMonth ??= false;

    int actualMonth = month;

    // 处理闰月分界
    if (isLeapMonth && lunarDay != null) {
      switch (settings.leapMonthBoundary) {
        case LeapMonthBoundary.middle:
          // 闰月月中分界：前半月算上个月，后半月算下个月
          if (lunarDay <= 15) {
            actualMonth = month; // 算作当月
          } else {
            actualMonth = month + 1; // 算作下月
            if (actualMonth > 12) actualMonth = 1;
          }
          break;
        case LeapMonthBoundary.previous:
          // 整个闰月都算作上个月
          actualMonth = month;
          break;
        case LeapMonthBoundary.next:
          // 整个闰月都算作下个月
          actualMonth = month + 1;
          if (actualMonth > 12) actualMonth = 1;
          break;
      }
    }

    // 月支固定：正月寅，二月卯，...，十二月丑
    String monthBranch = monthBranches[actualMonth - 1];

    // 月干根据年干推算
    // 注意：这里需要使用与年干支计算相同的逻辑来获取年干索引
    // 为了保持一致性，我们需要传递一个虚拟的出生时间来获取正确的年干
    DateTime virtualBirthTime = DateTime(year, month, 15); // 使用月中作为虚拟出生时间
    String yearStemBranch = getYearStemBranch(year, birthTime: virtualBirthTime, settings: settings);
    int yearStemIndex = heavenlyStems.indexOf(yearStemBranch[0]);
    int monthStemIndex;

    switch (yearStemIndex) {
      case 0: case 5: // 甲己年
        monthStemIndex = (2 + actualMonth - 1) % 10; // 丙作首
        break;
      case 1: case 6: // 乙庚年
        monthStemIndex = (4 + actualMonth - 1) % 10; // 戊为头
        break;
      case 2: case 7: // 丙辛年
        monthStemIndex = (6 + actualMonth - 1) % 10; // 庚上起
        break;
      case 3: case 8: // 丁壬年
        monthStemIndex = (8 + actualMonth - 1) % 10; // 壬位顺行流
        break;
      case 4: case 9: // 戊癸年
        monthStemIndex = (0 + actualMonth - 1) % 10; // 甲子是何方
        break;
      default:
        monthStemIndex = 0;
    }

    return heavenlyStems[monthStemIndex] + monthBranch;
  }
  
  /// 计算日干支（高精度版本，与paipan_service.dart保持一致）
  /// [date] 日期
  /// 返回日干支字符串
  static String getDayStemBranch(DateTime date) {
    // 直接使用paipan_service.dart的精确算法
    PaipanService paipanService = PaipanService();

    Map<String, dynamic> ganZhiResult = paipanService.getGanZhi(
      date.year,
      date.month,
      date.day,
      12, // 使用正午时间避免子时问题
      0,
      0
    );

    if (ganZhiResult.isNotEmpty) {
      List<int> tg = ganZhiResult['tg'];
      List<int> dz = ganZhiResult['dz'];

      // 返回日柱（索引2）
      return paipanService.ctg[tg[2]] + paipanService.cdz[dz[2]];
    }

    // 如果paipan_service计算失败，使用备用算法
    double jd = _getJulianDayDouble(date);
    double jda = jd + 0.5;
    double thes = ((jda - jda.floor()) * 86400.0) + 3600.0;
    double dayjd = jda.floor() + (thes / 86400.0);

    int dgz = ((dayjd + 49.0).floor() % 60 + 60) % 60;
    int stemIndex = dgz % 10;
    int branchIndex = dgz % 12;

    return heavenlyStems[stemIndex] + earthlyBranches[branchIndex];
  }
  
  /// 计算时干支
  /// [date] 包含时间的日期
  /// [hourIndex] 时辰索引 (0-11)
  /// [settings] 高级设置（可选）
  /// 返回时干支字符串
  static String getHourStemBranch(DateTime date, int hourIndex, {AdvancedSettings? settings}) {
    // 时支固定
    String hourBranch = earthlyBranches[hourIndex];

    // 时干根据日干推算
    // 甲己还加甲，乙庚丙作初
    // 丙辛从戊起，丁壬庚子居
    // 戊癸何方发，壬子是真途
    String dayStemBranch = settings != null
        ? getDayStemBranchWithSettings(date, settings: settings)
        : getDayStemBranch(date);
    int dayStemIndex = heavenlyStems.indexOf(dayStemBranch[0]);

    int hourStemIndex;
    switch (dayStemIndex) {
      case 0: // 甲日
      case 5: // 己日
        hourStemIndex = (0 + hourIndex) % 10; // 甲作初
        break;
      case 1: // 乙日
      case 6: // 庚日
        hourStemIndex = (2 + hourIndex) % 10; // 丙作初
        break;
      case 2: // 丙日
      case 7: // 辛日
        hourStemIndex = (4 + hourIndex) % 10; // 戊起
        break;
      case 3: // 丁日
      case 8: // 壬日
        hourStemIndex = (6 + hourIndex) % 10; // 庚子居
        break;
      case 4: // 戊日
      case 9: // 癸日
        hourStemIndex = (8 + hourIndex) % 10; // 壬子是真途
        break;
      default:
        hourStemIndex = 0;
    }

    return heavenlyStems[hourStemIndex] + hourBranch;
  }
  
  /// 获取完整的四柱干支
  /// [solarDate] 公历日期时间
  /// [lunarMonth] 农历月份
  /// [hourIndex] 时辰索引
  /// 返回包含年月日时干支的Map
  static Map<String, String> getFourPillars(DateTime solarDate, int lunarMonth, int hourIndex) {
    return {
      'year': getYearStemBranch(solarDate.year),
      'month': getMonthStemBranch(solarDate.year, lunarMonth),
      'day': getDayStemBranch(solarDate),
      'hour': getHourStemBranch(solarDate, hourIndex),
    };
  }
  
  /// 计算儒略日数（整数版本，保留兼容性）
  /// [date] 日期
  /// 返回儒略日数
  static int _getJulianDay(DateTime date) {
    return _getJulianDayDouble(date).floor();
  }

  /// 计算高精度儒略日数（浮点版本）
  /// [date] 日期时间
  /// 返回高精度儒略日数
  static double _getJulianDayDouble(DateTime date) {
    int year = date.year;
    int month = date.month;
    int day = date.day;
    int hour = date.hour;
    int minute = date.minute;
    int second = date.second;

    if (month <= 2) {
      year -= 1;
      month += 12;
    }

    int a = year ~/ 100;
    int b = 2 - a + (a ~/ 4);

    // 计算日期部分
    double jd = (365.25 * (year + 4716)).floor() +
                (30.6001 * (month + 1)).floor() +
                day + b - 1524;

    // 添加时间部分，提高精度
    double timeFraction = (hour + minute / 60.0 + second / 3600.0) / 24.0;

    return jd + timeFraction;
  }
  
  /// 获取天干属性
  /// [stem] 天干字符
  /// 返回天干属性Map
  static Map<String, dynamic> getStemProperties(String stem) {
    final properties = {
      '甲': {'element': '木', 'nature': '阳', 'direction': '东'},
      '乙': {'element': '木', 'nature': '阴', 'direction': '东'},
      '丙': {'element': '火', 'nature': '阳', 'direction': '南'},
      '丁': {'element': '火', 'nature': '阴', 'direction': '南'},
      '戊': {'element': '土', 'nature': '阳', 'direction': '中'},
      '己': {'element': '土', 'nature': '阴', 'direction': '中'},
      '庚': {'element': '金', 'nature': '阳', 'direction': '西'},
      '辛': {'element': '金', 'nature': '阴', 'direction': '西'},
      '壬': {'element': '水', 'nature': '阳', 'direction': '北'},
      '癸': {'element': '水', 'nature': '阴', 'direction': '北'},
    };
    return properties[stem] ?? {};
  }
  
  /// 获取地支属性
  /// [branch] 地支字符
  /// 返回地支属性Map
  static Map<String, dynamic> getBranchProperties(String branch) {
    final properties = {
      '子': {'element': '水', 'direction': '北', 'time': '23-01时', 'season': '冬'},
      '丑': {'element': '土', 'direction': '东北', 'time': '01-03时', 'season': '冬'},
      '寅': {'element': '木', 'direction': '东北', 'time': '03-05时', 'season': '春'},
      '卯': {'element': '木', 'direction': '东', 'time': '05-07时', 'season': '春'},
      '辰': {'element': '土', 'direction': '东南', 'time': '07-09时', 'season': '春'},
      '巳': {'element': '火', 'direction': '东南', 'time': '09-11时', 'season': '夏'},
      '午': {'element': '火', 'direction': '南', 'time': '11-13时', 'season': '夏'},
      '未': {'element': '土', 'direction': '西南', 'time': '13-15时', 'season': '夏'},
      '申': {'element': '金', 'direction': '西南', 'time': '15-17时', 'season': '秋'},
      '酉': {'element': '金', 'direction': '西', 'time': '17-19时', 'season': '秋'},
      '戌': {'element': '土', 'direction': '西北', 'time': '19-21时', 'season': '秋'},
      '亥': {'element': '水', 'direction': '西北', 'time': '21-23时', 'season': '冬'},
    };
    return properties[branch] ?? {};
  }

  /// 计算日干支（支持子时处理方式，高精度版本）
  /// [date] 日期时间
  /// [settings] 高级设置（可选）
  /// 返回日干支字符串
  static String getDayStemBranchWithSettings(DateTime date, {AdvancedSettings? settings}) {
    settings ??= AdvancedSettings.defaultSettings();

    // 直接使用paipan_service.dart的精确算法
    PaipanService paipanService = PaipanService();

    // 设置子时处理模式
    paipanService.zwz = (settings.ziHourMode == ZiHourMode.early);

    // 调用paipan_service的getGanZhi方法获取准确的四柱
    Map<String, dynamic> ganZhiResult = paipanService.getGanZhi(
      date.year,
      date.month,
      date.day,
      date.hour,
      date.minute,
      date.second
    );

    if (ganZhiResult.isNotEmpty) {
      List<int> tg = ganZhiResult['tg'];
      List<int> dz = ganZhiResult['dz'];

      // 返回日柱（索引2）
      return paipanService.ctg[tg[2]] + paipanService.cdz[dz[2]];
    }

    // 如果paipan_service计算失败，使用原有算法作为备用
    double jd = _getJulianDayDouble(date);
    double jda = jd + 0.5;
    double thes = ((jda - jda.floor()) * 86400.0) + 3600.0;
    double dayjd = jda.floor() + (thes / 86400.0);

    int dgz = ((dayjd + 49.0).floor() % 60 + 60) % 60;
    int stemIndex = dgz % 10;
    int branchIndex = dgz % 12;

    // 处理子时
    if (date.hour >= 23 && settings.ziHourMode == ZiHourMode.early) {
      stemIndex = (stemIndex + 10 - 1) % 10;
      branchIndex = (branchIndex + 12 - 1) % 12;
    }

    return heavenlyStems[stemIndex] + earthlyBranches[branchIndex];
  }

  /// 获取完整的四柱干支（支持高级设置）
  /// [solarDate] 公历日期时间
  /// [lunarMonth] 农历月份
  /// [lunarDay] 农历日期（可选）
  /// [isLeapMonth] 是否闰月（可选）
  /// [hourIndex] 时辰索引
  /// [settings] 高级设置（可选）
  /// 返回包含年月日时干支的Map
  static Map<String, String> getFourPillarsWithSettings(
    DateTime solarDate,
    int lunarMonth,
    int hourIndex, {
    int? lunarDay,
    bool? isLeapMonth,
    AdvancedSettings? settings,
  }) {
    settings ??= AdvancedSettings.defaultSettings();

    return {
      'year': getYearStemBranch(solarDate.year, birthTime: solarDate, settings: settings),
      'month': getMonthStemBranch(
        solarDate.year,
        lunarMonth,
        lunarDay: lunarDay,
        isLeapMonth: isLeapMonth,
        settings: settings,
      ),
      'day': getDayStemBranchWithSettings(solarDate, settings: settings),
      'hour': getHourStemBranch(solarDate, hourIndex, settings: settings),
    };
  }

  /// 直接使用 PaipanService 获取完整四柱信息
  /// [solarDate] 公历日期时间
  /// [settings] 高级设置（可选）
  /// 返回包含完整干支信息的Map
  static Map<String, dynamic> getFullGanZhiInfo(DateTime solarDate, {AdvancedSettings? settings}) {
    settings ??= AdvancedSettings.defaultSettings();

    PaipanService paipanService = PaipanService();
    paipanService.zwz = (settings.ziHourMode == ZiHourMode.early);

    return paipanService.getGanZhi(
      solarDate.year,
      solarDate.month,
      solarDate.day,
      solarDate.hour,
      solarDate.minute,
      solarDate.second
    );
  }

  /// 获取生肖信息
  /// [year] 年份
  /// [settings] 高级设置（可选）
  /// 返回生肖字符串
  static String getZodiac(int year, {AdvancedSettings? settings}) {
    PaipanService paipanService = PaipanService();
    Map<String, dynamic> ganZhiResult = paipanService.getGanZhi(year, 1, 1, 12, 0, 0);

    if (ganZhiResult.isNotEmpty) {
      List<int> dz = ganZhiResult['dz'];
      return paipanService.csa[dz[0]];
    }

    return '';
  }

  /// 获取纳音信息
  /// [stemBranch] 干支字符串
  /// 返回纳音字符串
  static String getNaYin(String stemBranch) {
    if (stemBranch.length < 2) return '';

    PaipanService paipanService = PaipanService();

    int stemIndex = paipanService.ctg.indexOf(stemBranch[0]);
    int branchIndex = paipanService.cdz.indexOf(stemBranch[1]);

    if (stemIndex >= 0 && branchIndex >= 0) {
      List<dynamic> naYinInfo = paipanService.naYin(stemIndex, branchIndex);
      return naYinInfo.isNotEmpty ? naYinInfo[0].toString() : '';
    }

    return '';
  }
}
