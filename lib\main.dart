// lib/main.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'screens/auth/login_screen.dart';
import 'screens/auth/register_screen.dart';
import 'screens/home/<USER>';
import 'screens/input/birth_info_screen.dart';
import 'screens/chart/chart_display_screen.dart';
import 'screens/chart/ziwei_chart_screen.dart';
import 'screens/interpretation/interpretation_screen.dart';
import 'screens/calendar/calendar_screen.dart';
import 'screens/bazi/bazi_screen.dart';
import 'services/auth_service.dart';
import 'services/chart_service.dart';
import 'services/storage_service.dart';
import 'services/ai_service.dart';
import 'services/ziwei_chart_calculator.dart';
import 'services/settings_service.dart';
import 'models/birth_data.dart';
import 'models/chart_data.dart';
import 'models/ziwei_chart.dart';
import 'models/advanced_settings.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthService()),
        Provider(create: (_) => StorageService()),
        Provider(create: (_) => ChartService()),
        Provider(create: (_) => AIService()),
      ],
      child: MaterialApp(
        title: '紫微命盘',
        theme: ThemeData(
          primarySwatch: Colors.purple,
          visualDensity: VisualDensity.adaptivePlatformDensity,
          brightness: Brightness.light,
          appBarTheme: AppBarTheme(
            backgroundColor: Colors.purple,
            foregroundColor: Colors.white,
          ),
        ),
        darkTheme: ThemeData(
          primarySwatch: Colors.purple,
          visualDensity: VisualDensity.adaptivePlatformDensity,
          brightness: Brightness.dark,
        ),
        themeMode: ThemeMode.system,
        initialRoute: '/',
        routes: {
          '/': (context) => HomeScreen(),
          '/login': (context) => LoginScreen(),
          '/register': (context) => RegisterScreen(),
          '/input': (context) => BirthInfoScreen(),
          '/calendar': (context) => CalendarScreen(),
          '/bazi': (context) => BaziScreen(),
        },
        onGenerateRoute: (settings) {
          if (settings.name == '/chart') {
            final arguments = settings.arguments;
            if (arguments is ZiweiChart) {
              return MaterialPageRoute(
                builder: (context) => ZiweiChartScreen(chart: arguments),
              );
            } else if (arguments is BirthData) {
              // 如果传递的是BirthData，需要先计算ZiweiChart
              return MaterialPageRoute(
                builder: (context) => FutureBuilder<ZiweiChart>(
                  future: _calculateZiweiChart(arguments),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return Scaffold(
                        appBar: AppBar(title: Text('计算命盘中...')),
                        body: Center(child: CircularProgressIndicator()),
                      );
                    } else if (snapshot.hasError) {
                      return Scaffold(
                        appBar: AppBar(title: Text('计算失败')),
                        body: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.error, size: 64, color: Colors.red),
                              SizedBox(height: 16),
                              Text('命盘计算失败'),
                              SizedBox(height: 8),
                              Text('${snapshot.error}'),
                              SizedBox(height: 16),
                              ElevatedButton(
                                onPressed: () => Navigator.pop(context),
                                child: Text('返回'),
                              ),
                            ],
                          ),
                        ),
                      );
                    } else if (snapshot.hasData) {
                      return ZiweiChartScreen(chart: snapshot.data!);
                    } else {
                      return Scaffold(
                        appBar: AppBar(title: Text('未知错误')),
                        body: Center(child: Text('未知错误')),
                      );
                    }
                  },
                ),
              );
            }
          } else if (settings.name == '/interpretation') {
            final chartData = settings.arguments as ChartData;
            return MaterialPageRoute(
              builder: (context) => InterpretationScreen(chartData: chartData),
            );
          }
          return null;
        },
      ),
    );
  }

  /// 计算紫微斗数命盘的辅助方法
  static Future<ZiweiChart> _calculateZiweiChart(BirthData birthData) async {
    try {
      // 加载高级设置
      AdvancedSettings settings = await SettingsService.instance.loadAdvancedSettings();

      // 计算紫微斗数命盘
      return ZiweiChartCalculator.calculateChart(birthData, settings);
    } catch (e) {
      // 如果加载设置失败，使用默认设置
      AdvancedSettings defaultSettings = AdvancedSettings.defaultSettings();
      return ZiweiChartCalculator.calculateChart(birthData, defaultSettings);
    }
  }
}
