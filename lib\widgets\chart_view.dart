// lib/widgets/chart_view.dart
import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/chart_data.dart';

class ChartView extends StatefulWidget {
  final ChartData chartData;
  final Function(int)? onPalaceSelected;
  
  ChartView({
    required this.chartData,
    this.onPalaceSelected,
  });
  
  @override
  _ChartViewState createState() => _ChartViewState();
}

class _ChartViewState extends State<ChartView> {
  double _scale = 1.0;
  int _selectedPalaceIndex = -1;
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onScaleUpdate: (ScaleUpdateDetails details) {
        setState(() {
          _scale = (_scale * details.scale).clamp(0.8, 2.0);
        });
      },
      child: Center(
        child: Transform.scale(
          scale: _scale,
          child: Container(
            width: 300,
            height: 300,
            child: CustomPaint(
              painter: ChartPainter(
                chartData: widget.chartData,
                selectedPalaceIndex: _selectedPalaceIndex,
              ),
              child: GestureDetector(
                onTapDown: (TapDownDetails details) {
                  _handleTap(details.localPosition);
                },
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  void _handleTap(Offset position) {
    // 计算点击位置对应的宫位
    final center = Offset(150, 150);
    final vector = position - center;
    
    // 计算角度
    double angle = math.atan2(vector.dy, vector.dx);
    if (angle < 0) angle += 2 * math.pi;
    
    // 计算距离
    final distance = vector.distance;
    
    // 只有点击在环形区域内才处理
    if (distance >= 50 && distance <= 150) {
      // 计算宫位索引
      final palaceIndex = (angle / (math.pi / 6)).floor() % 12;
      
      setState(() {
        _selectedPalaceIndex = palaceIndex;
      });
      
      if (widget.onPalaceSelected != null) {
        widget.onPalaceSelected!(palaceIndex);
      }
    }
  }
}

class ChartPainter extends CustomPainter {
  final ChartData chartData;
  final int selectedPalaceIndex;
  
  ChartPainter({
    required this.chartData,
    required this.selectedPalaceIndex,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final outerRadius = math.min(size.width, size.height) / 2;
    final innerRadius = outerRadius * 0.3;
    final middleRadius = (outerRadius + innerRadius) / 2;
    
    // 绘制背景
    _drawBackground(canvas, center, outerRadius);
    
    // 绘制十二宫位
    _drawPalaces(canvas, center, innerRadius, outerRadius);
    
    // 绘制中心区域
    _drawCenterInfo(canvas, center, innerRadius);
  }
  
  void _drawBackground(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, radius, paint);
    
    final borderPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    canvas.drawCircle(center, radius, borderPaint);
  }
  
  void _drawPalaces(Canvas canvas, Offset center, double innerRadius, double outerRadius) {
    final palacePaint = Paint()
      ..style = PaintingStyle.fill;
    
    final borderPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    final textStyle = TextStyle(
      color: Colors.black,
      fontSize: 10,
    );
    
    final middleRadius = (innerRadius + outerRadius) / 2;
    
    for (int i = 0; i < 12; i++) {
      final startAngle = i * math.pi / 6;
      final endAngle = (i + 1) * math.pi / 6;
      
      // 计算宫位在命盘中的实际索引
      final palaceIndex = (chartData.ascendant + i) % 12;
      final palace = chartData.palaces.firstWhere((p) => p.index == palaceIndex);
      
      // 设置宫位颜色
      if (palaceIndex == selectedPalaceIndex) {
        palacePaint.color = Colors.amber.withOpacity(0.3);
      } else if (palaceIndex == chartData.ascendant) {
        palacePaint.color = Colors.red.withOpacity(0.2);
      } else if (palaceIndex == chartData.bodyPalace) {
        palacePaint.color = Colors.blue.withOpacity(0.2);
      } else {
        palacePaint.color = Colors.grey.withOpacity(0.1);
      }
      
      // 绘制宫位扇形
      final path = Path()
        ..moveTo(center.dx, center.dy)
        ..arcTo(
          Rect.fromCircle(center: center, radius: outerRadius),
          startAngle,
          endAngle - startAngle,
          false
        )
        ..lineTo(center.dx, center.dy)
        ..close();
      
      canvas.drawPath(path, palacePaint);
      canvas.drawPath(path, borderPaint);
      
      // 绘制宫位名称
      final nameAngle = (startAngle + endAngle) / 2;
      final nameX = center.dx + middleRadius * 0.8 * math.cos(nameAngle);
      final nameY = center.dy + middleRadius * 0.8 * math.sin(nameAngle);
      
      _drawRotatedText(
        canvas,
        palace.name,
        Offset(nameX, nameY),
        nameAngle,
        textStyle.copyWith(fontWeight: FontWeight.bold),
      );
      
      // 绘制地支
      final branchAngle = (startAngle + endAngle) / 2;
      final branchX = center.dx + middleRadius * 0.5 * math.cos(branchAngle);
      final branchY = center.dy + middleRadius * 0.5 * math.sin(branchAngle);
      
      _drawRotatedText(
        canvas,
        palace.earthBranch,
        Offset(branchX, branchY),
        branchAngle,
        textStyle,
      );
      
      // 绘制主星
      if (palace.mainStars.isNotEmpty) {
        final mainStarsText = palace.mainStars.join('\n');
        final starsAngle = (startAngle + endAngle) / 2;
        final starsX = center.dx + middleRadius * 1.2 * math.cos(starsAngle);
        final starsY = center.dy + middleRadius * 1.2 * math.sin(starsAngle);
        
        _drawRotatedText(
          canvas,
          mainStarsText,
          Offset(starsX, starsY),
          starsAngle,
          textStyle.copyWith(color: Colors.purple),
        );
      }
    }
  }
  
  void _drawCenterInfo(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..color = Colors.grey.withOpacity(0.2)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, radius, paint);
    
    final borderPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    canvas.drawCircle(center, radius, borderPaint);
    
    // 绘制中心文字信息
    final textPainter = TextPainter(
      text: TextSpan(
        text: '${chartData.birthData.year}年\n${chartData.birthData.month}月${chartData.birthData.day}日\n${_getHourText(chartData.birthData.hour)}',
        style: TextStyle(
          color: Colors.black,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout(minWidth: 0, maxWidth: radius * 2);
    
    final offset = Offset(
      center.dx - textPainter.width / 2,
      center.dy - textPainter.height / 2,
    );
    
    textPainter.paint(canvas, offset);
  }
  
  String _getHourText(int hour) {
    final hourNames = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    return '${hourNames[hour]}时';
  }
  
  void _drawRotatedText(
    Canvas canvas,
    String text,
    Offset position,
    double angle,
    TextStyle style,
  ) {
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: style,
      ),
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    
    canvas.save();
    canvas.translate(position.dx, position.dy);
    canvas.rotate(angle + math.pi / 2);
    canvas.translate(-textPainter.width / 2, -textPainter.height / 2);
    textPainter.paint(canvas, Offset.zero);
    canvas.restore();
  }
  
  @override
  bool shouldRepaint(ChartPainter oldDelegate) {
    return oldDelegate.chartData != chartData ||
           oldDelegate.selectedPalaceIndex != selectedPalaceIndex;
  }
}

