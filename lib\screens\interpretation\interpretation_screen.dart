// lib/screens/interpretation/interpretation_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/chart_data.dart';
import '../../services/ai_service.dart';

class InterpretationScreen extends StatefulWidget {
  final ChartData chartData;
  
  const InterpretationScreen({Key? key, required this.chartData}) : super(key: key);
  
  @override
  _InterpretationScreenState createState() => _InterpretationScreenState();
}

class _InterpretationScreenState extends State<InterpretationScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  String _aiInterpretation = '';
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  Future<void> _generateAIInterpretation() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final aiService = Provider.of<AIService>(context, listen: false);
      final interpretation = await aiService.generateInterpretation(widget.chartData);
      setState(() {
        _aiInterpretation = interpretation;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('生成解读失败：$e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('命盘解读'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: '基础解读'),
            Tab(text: 'AI解读'),
            Tab(text: '详细分析'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildBasicInterpretation(),
          _buildAIInterpretation(),
          _buildDetailedAnalysis(),
        ],
      ),
    );
  }
  
  Widget _buildBasicInterpretation() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPersonalityCard(),
          SizedBox(height: 16),
          _buildCareerCard(),
          SizedBox(height: 16),
          _buildRelationshipCard(),
          SizedBox(height: 16),
          _buildWealthCard(),
        ],
      ),
    );
  }
  
  Widget _buildPersonalityCard() {
    final commandPalace = widget.chartData.palaces[0]; // 命宫
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: Colors.purple),
                SizedBox(width: 8),
                Text('性格特质', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            SizedBox(height: 12),
            Text('命宫：${commandPalace.name} (${commandPalace.earthBranch})', 
                 style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            if (commandPalace.mainStars.isNotEmpty) ...[
              Text('主星：${commandPalace.mainStars.join('、')}', 
                   style: TextStyle(color: Colors.red)),
              SizedBox(height: 8),
            ],
            Text(_getPersonalityDescription(commandPalace)),
          ],
        ),
      ),
    );
  }
  
  Widget _buildCareerCard() {
    final careerPalace = widget.chartData.palaces[8]; // 官禄宫
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.work, color: Colors.blue),
                SizedBox(width: 8),
                Text('事业运势', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            SizedBox(height: 12),
            Text('官禄宫：${careerPalace.name} (${careerPalace.earthBranch})', 
                 style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            if (careerPalace.mainStars.isNotEmpty) ...[
              Text('主星：${careerPalace.mainStars.join('、')}', 
                   style: TextStyle(color: Colors.red)),
              SizedBox(height: 8),
            ],
            Text(_getCareerDescription(careerPalace)),
          ],
        ),
      ),
    );
  }
  
  Widget _buildRelationshipCard() {
    final marriagePalace = widget.chartData.palaces[2]; // 夫妻宫
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.favorite, color: Colors.pink),
                SizedBox(width: 8),
                Text('感情婚姻', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            SizedBox(height: 12),
            Text('夫妻宫：${marriagePalace.name} (${marriagePalace.earthBranch})', 
                 style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            if (marriagePalace.mainStars.isNotEmpty) ...[
              Text('主星：${marriagePalace.mainStars.join('、')}', 
                   style: TextStyle(color: Colors.red)),
              SizedBox(height: 8),
            ],
            Text(_getRelationshipDescription(marriagePalace)),
          ],
        ),
      ),
    );
  }
  
  Widget _buildWealthCard() {
    final wealthPalace = widget.chartData.palaces[4]; // 财帛宫
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.monetization_on, color: Colors.green),
                SizedBox(width: 8),
                Text('财富运势', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            SizedBox(height: 12),
            Text('财帛宫：${wealthPalace.name} (${wealthPalace.earthBranch})', 
                 style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            if (wealthPalace.mainStars.isNotEmpty) ...[
              Text('主星：${wealthPalace.mainStars.join('、')}', 
                   style: TextStyle(color: Colors.red)),
              SizedBox(height: 8),
            ],
            Text(_getWealthDescription(wealthPalace)),
          ],
        ),
      ),
    );
  }
  
  Widget _buildAIInterpretation() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          if (_aiInterpretation.isEmpty && !_isLoading)
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.auto_awesome, size: 64, color: Colors.purple),
                    SizedBox(height: 16),
                    Text('AI智能解读', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                    SizedBox(height: 8),
                    Text('点击下方按钮生成个性化的命盘解读', style: TextStyle(color: Colors.grey)),
                    SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: _generateAIInterpretation,
                      icon: Icon(Icons.psychology),
                      label: Text('生成AI解读'),
                    ),
                  ],
                ),
              ),
            ),
          if (_isLoading)
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('AI正在分析您的命盘...'),
                  ],
                ),
              ),
            ),
          if (_aiInterpretation.isNotEmpty)
            Expanded(
              child: SingleChildScrollView(
                child: Card(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.auto_awesome, color: Colors.purple),
                            SizedBox(width: 8),
                            Text('AI智能解读', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                            Spacer(),
                            IconButton(
                              icon: Icon(Icons.refresh),
                              onPressed: _generateAIInterpretation,
                              tooltip: '重新生成',
                            ),
                          ],
                        ),
                        SizedBox(height: 12),
                        Text(_aiInterpretation),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  Widget _buildDetailedAnalysis() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.construction, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text('详细分析功能开发中', style: TextStyle(fontSize: 18, color: Colors.grey)),
          SizedBox(height: 8),
          Text('敬请期待下一版本更新', style: TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }

  String _getPersonalityDescription(palace) {
    if (palace.mainStars.contains('紫微')) {
      return '紫微星坐命，具有领导才能，性格高贵，有王者风范。做事有条理，责任心强，但有时会显得过于严肃。';
    } else if (palace.mainStars.contains('天府')) {
      return '天府星坐命，性格温和稳重，具有很强的包容力。善于理财，做事踏实，是值得信赖的人。';
    } else {
      return '根据命宫星曜配置，您具有独特的性格特质。建议多发挥自身优势，注意克服性格中的不足。';
    }
  }

  String _getCareerDescription(palace) {
    if (palace.mainStars.contains('武曲')) {
      return '武曲星在官禄宫，适合从事金融、工程、技术等需要专业技能的工作。事业发展稳健，但需要耐心积累。';
    } else if (palace.mainStars.contains('天相')) {
      return '天相星在官禄宫，适合从事服务业、公务员、教育等与人打交道的工作。事业运势平稳，贵人运佳。';
    } else {
      return '根据官禄宫星曜配置，建议选择适合自己特质的职业方向，发挥所长，必能在事业上有所成就。';
    }
  }

  String _getRelationshipDescription(palace) {
    return '根据夫妻宫的星曜配置，您在感情方面有自己的特点。建议以诚待人，用心经营感情，必能获得美满的婚姻。';
  }

  String _getWealthDescription(palace) {
    return '根据财帛宫的星曜配置，您的财运有一定的特点。建议合理理财，开源节流，必能积累财富。';
  }
}
