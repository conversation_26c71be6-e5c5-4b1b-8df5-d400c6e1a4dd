// lib/services/storage_service.dart
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/chart_data.dart';

class StorageService {
  // 保存命盘数据
  Future<bool> saveChart(ChartData chart) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 获取已保存的命盘ID列表
      final chartIds = prefs.getStringList('chart_ids') ?? [];
      
      // 如果是新命盘，添加到ID列表
      if (!chartIds.contains(chart.id)) {
        chartIds.add(chart.id);
        await prefs.setStringList('chart_ids', chartIds);
      }
      
      // 保存命盘数据
      await prefs.setString('chart_${chart.id}', jsonEncode(chart.toJson()));
      
      return true;
    } catch (e) {
      print('保存命盘失败: $e');
      return false;
    }
  }
  
  // 获取命盘数据
  Future<ChartData?> getChart(String chartId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final chartJson = prefs.getString('chart_${chartId}');
      if (chartJson == null) return null;
      
      return ChartData.fromJson(jsonDecode(chartJson));
    } catch (e) {
      print('获取命盘失败: $e');
      return null;
    }
  }
  
  // 获取所有命盘
  Future<List<ChartData>> getAllCharts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      final chartIds = prefs.getStringList('chart_ids') ?? [];
      final charts = <ChartData>[];
      
      for (final id in chartIds) {
        final chartJson = prefs.getString('chart_$id');
        if (chartJson != null) {
          charts.add(ChartData.fromJson(jsonDecode(chartJson)));
        }
      }
      
      // 按创建时间排序，最新的在前
      charts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      return charts;
    } catch (e) {
      print('获取所有命盘失败: $e');
      return [];
    }
  }
  
  // 删除命盘
  Future<bool> deleteChart(String chartId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 从ID列表中移除
      final chartIds = prefs.getStringList('chart_ids') ?? [];
      chartIds.remove(chartId);
      await prefs.setStringList('chart_ids', chartIds);
      
      // 删除命盘数据
      await prefs.remove('chart_$chartId');
      
      // 删除相关的AI解读数据
      await prefs.remove('ai_interpretation_$chartId');
      
      return true;
    } catch (e) {
      print('删除命盘失败: $e');
      return false;
    }
  }
  
  // 保存AI解读数据
  Future<bool> saveAIInterpretation(String chartId, String interpretation) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('ai_interpretation_$chartId', interpretation);
      return true;
    } catch (e) {
      print('保存AI解读失败: $e');
      return false;
    }
  }

  // 获取AI解读数据
  Future<String?> getAIInterpretation(String chartId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('ai_interpretation_$chartId');
    } catch (e) {
      print('获取AI解读失败: $e');
      return null;
    }
  }
}