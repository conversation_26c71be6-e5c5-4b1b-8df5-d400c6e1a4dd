// lib/models/location.dart
class Location {
  final String country;
  final String province;
  final String city;
  final double latitude;
  final double longitude;

  Location({
    required this.country,
    required this.province,
    required this.city,
    required this.latitude,
    required this.longitude,
  });

  // 获取位置名称的便捷方法
  String get name => '$province$city';

  Map<String, dynamic> toJson() {
    return {
      'country': country,
      'province': province,
      'city': city,
      'latitude': latitude,
      'longitude': longitude,
    };
  }

  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      country: json['country'] as String,
      province: json['province'] as String,
      city: json['city'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
    );
  }
}