// lib/services/ziwei_chart_calculator.dart
// 紫微斗数命盘计算服务

import '../models/birth_data.dart';
import '../models/advanced_settings.dart';
import '../models/ziwei_chart.dart';
import '../models/ziwei_palace.dart';
import '../models/ziwei_star.dart';
import '../utils/stem_branch_calculator.dart';
import '../utils/calendar_converter.dart';
import '../services/ziwei_star_placement.dart';

/// 紫微斗数命盘计算器
class ZiweiChartCalculator {
  
  /// 计算完整的紫微斗数命盘
  static ZiweiChart calculateChart(
    BirthData birthData, 
    AdvancedSettings settings
  ) {
    try {
      // 1. 计算基础时间信息
      DateTime solarTime = _calculateSolarTime(birthData);
      
      // 2. 计算四柱干支
      Map<String, String> fourPillars = _calculateFourPillars(birthData, solarTime, settings);
      
      // 3. 计算命宫和身宫位置
      int lifePalaceIndex = _calculateLifePalaceIndex(solarTime, birthData);
      int bodyPalaceIndex = _calculateBodyPalaceIndex(lifePalaceIndex);
      
      // 4. 创建十二宫位
      List<ZiweiPalace> palaces = _createPalaces(fourPillars, lifePalaceIndex);
      
      // 5. 安星入宫
      _placeAllStars(palaces, solarTime, lifePalaceIndex, settings);
      
      // 6. 计算大运
      List<MajorPeriod> majorPeriods = _calculateMajorPeriods(palaces, lifePalaceIndex, birthData);
      
      // 7. 创建命盘
      return ZiweiChart(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: birthData.name,
        gender: birthData.gender,
        birthData: birthData,
        settings: settings,
        palaces: palaces,
        lifePalaceIndex: lifePalaceIndex,
        bodyPalaceIndex: bodyPalaceIndex,
        fourPillars: fourPillars,
        majorPeriods: majorPeriods,
        createdAt: DateTime.now(),
      );
      
    } catch (e) {
      print('紫微斗数命盘计算错误: $e');
      rethrow;
    }
  }
  
  /// 计算太阳时
  static DateTime _calculateSolarTime(BirthData birthData) {
    DateTime birthTime = DateTime(
      birthData.year,
      birthData.month,
      birthData.day,
      birthData.hour,
      birthData.minute,
    );
    
    // 如果是农历，先转换为公历
    if (birthData.calendar == 'lunar') {
      DateTime solarDate = CalendarConverter.lunarToSolar(
        birthData.year,
        birthData.month,
        birthData.day,
        birthData.isLeapMonth,
      );
      birthTime = DateTime(
        solarDate.year,
        solarDate.month,
        solarDate.day,
        birthData.hour,
        birthData.minute,
      );
    }
    
    // 如果有地理位置信息，计算真太阳时
    if (birthData.location != null) {
      return CalendarConverter.meanToApparentSolarTime(
        birthTime,
        birthData.location!.longitude,
      );
    }
    
    return birthTime;
  }
  
  /// 计算四柱干支
  static Map<String, String> _calculateFourPillars(
    BirthData birthData,
    DateTime solarTime,
    AdvancedSettings settings,
  ) {
    // 获取农历信息
    Map<String, dynamic> lunarInfo = CalendarConverter.solarToLunar(solarTime);
    
    // 计算时辰索引
    int hourIndex = CalendarConverter.getHourIndexByMeanTime(solarTime);
    
    // 使用高级设置计算四柱干支
    return StemBranchCalculator.getFourPillarsWithSettings(
      solarTime,
      lunarInfo['month'],
      hourIndex,
      lunarDay: lunarInfo['day'],
      isLeapMonth: lunarInfo['isLeapMonth'],
      settings: settings,
    );
  }
  
  /// 计算命宫位置
  static int _calculateLifePalaceIndex(DateTime solarTime, BirthData birthData) {
    // 紫微斗数命宫计算公式
    // 命宫 = (月数 + 时辰数) % 12
    
    int month = solarTime.month;
    int hourIndex = CalendarConverter.getHourIndexByMeanTime(solarTime);
    
    // 转换为地支索引（寅=0, 卯=1, ..., 丑=11）
    int lifePalaceIndex = (month - 1 + hourIndex + 2) % 12; // +2是因为正月对应寅宫
    
    return lifePalaceIndex;
  }
  
  /// 计算身宫位置
  static int _calculateBodyPalaceIndex(int lifePalaceIndex) {
    // 身宫计算公式（简化版）
    // 实际计算需要根据月份和时辰的复杂公式
    return (lifePalaceIndex + 6) % 12; // 简化为命宫对宫
  }
  
  /// 创建十二宫位
  static List<ZiweiPalace> _createPalaces(Map<String, String> fourPillars, int lifePalaceIndex) {
    // 创建标准十二宫位
    List<ZiweiPalace> standardPalaces = PalaceFactory.createStandardPalaces();
    
    // 根据命宫位置调整宫位顺序和干支
    List<ZiweiPalace> adjustedPalaces = [];
    
    for (int i = 0; i < 12; i++) {
      int actualIndex = (lifePalaceIndex + i) % 12;
      ZiweiPalace standardPalace = standardPalaces[i];
      
      // 计算宫位干支
      String palaceStemBranch = _calculatePalaceStemBranch(actualIndex, fourPillars);
      
      // 创建调整后的宫位
      ZiweiPalace adjustedPalace = ZiweiPalace(
        type: standardPalace.type,
        index: actualIndex,
        name: standardPalace.name,
        stemBranch: palaceStemBranch,
        element: _getPalaceElement(palaceStemBranch),
        description: standardPalace.description,
        domains: standardPalace.domains,
        isBodyPalace: i == 6, // 身宫通常在命宫对宫
      );
      
      adjustedPalaces.add(adjustedPalace);
    }
    
    return adjustedPalaces;
  }
  
  /// 计算宫位干支
  static String _calculatePalaceStemBranch(int palaceIndex, Map<String, String> fourPillars) {
    // 根据年干和宫位地支计算宫位天干
    const earthlyBranches = ['寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥', '子', '丑'];
    const heavenlyStems = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    
    String earthlyBranch = earthlyBranches[palaceIndex];
    
    // 根据年干推算宫位天干（简化算法）
    String yearStem = fourPillars['year']![0];
    int yearStemIndex = heavenlyStems.indexOf(yearStem);
    int palaceStemIndex = (yearStemIndex + palaceIndex) % 10;
    String heavenlyStem = heavenlyStems[palaceStemIndex];
    
    return heavenlyStem + earthlyBranch;
  }
  
  /// 获取宫位五行
  static StarElement _getPalaceElement(String stemBranch) {
    if (stemBranch.length < 2) return StarElement.earth;
    
    String earthlyBranch = stemBranch[1];
    
    switch (earthlyBranch) {
      case '寅':
      case '卯':
        return StarElement.wood;
      case '巳':
      case '午':
        return StarElement.fire;
      case '申':
      case '酉':
        return StarElement.metal;
      case '亥':
      case '子':
        return StarElement.water;
      default:
        return StarElement.earth;
    }
  }
  
  /// 安所有星曜
  static void _placeAllStars(
    List<ZiweiPalace> palaces,
    DateTime solarTime,
    int lifePalaceIndex,
    AdvancedSettings settings,
  ) {
    // 安主星
    ZiweiStarPlacement.placeMainStars(palaces, solarTime, lifePalaceIndex);
    
    // 安辅星
    ZiweiStarPlacement.placeAuxiliaryStars(palaces, solarTime, lifePalaceIndex);
    
    // 安杂曜（文昌文曲等）
    _placeMiscellaneousStars(palaces, solarTime);
    
    // 安化曜
    _placeTransformStars(palaces, solarTime, settings);
  }
  
  /// 安杂曜
  static void _placeMiscellaneousStars(List<ZiweiPalace> palaces, DateTime solarTime) {
    // 安文昌文曲
    _placeWenchangWenqu(palaces, solarTime);
    
    // 安桃花星
    _placePeachBlossomStars(palaces, solarTime);
    
    // 安其他杂曜
    _placeOtherMiscellaneousStars(palaces, solarTime);
  }
  
  /// 安文昌文曲
  static void _placeWenchangWenqu(List<ZiweiPalace> palaces, DateTime solarTime) {
    // 根据时辰安文昌文曲
    int hourIndex = CalendarConverter.getHourIndexByMeanTime(solarTime);
    
    // 文昌位置（简化算法）
    int wenchangPos = (hourIndex + 3) % 12;
    // 文曲位置（简化算法）
    int wenquPos = (hourIndex + 9) % 12;
    
    // 这里需要从数据库获取星曜并添加到宫位
    // 暂时省略具体实现
  }
  
  /// 安桃花星
  static void _placePeachBlossomStars(List<ZiweiPalace> palaces, DateTime solarTime) {
    // 安红鸾、天喜、天姚等桃花星
    // 根据年支计算位置
    int yearBranch = (solarTime.year - 4) % 12;
    
    // 红鸾位置
    int hongluan = (yearBranch + 3) % 12;
    // 天喜位置
    int tianxi = (hongluan + 6) % 12;
    
    // 这里需要从数据库获取星曜并添加到宫位
    // 暂时省略具体实现
  }
  
  /// 安其他杂曜
  static void _placeOtherMiscellaneousStars(List<ZiweiPalace> palaces, DateTime solarTime) {
    // 安孤辰寡宿等其他杂曜
    // 暂时省略具体实现
  }
  
  /// 安化曜
  static void _placeTransformStars(
    List<ZiweiPalace> palaces,
    DateTime solarTime,
    AdvancedSettings settings,
  ) {
    // 根据年干安四化星
    String yearStem = StemBranchCalculator.getYearStemBranch(
      solarTime.year,
      birthTime: solarTime,
      settings: settings,
    )[0];
    
    // 四化星对照表（简化版）
    Map<String, Map<String, String>> transformTable = {
      '甲': {'禄': '廉贞', '权': '破军', '科': '武曲', '忌': '太阳'},
      '乙': {'禄': '天机', '权': '天梁', '科': '紫微', '忌': '太阴'},
      '丙': {'禄': '天同', '权': '天机', '科': '文昌', '忌': '廉贞'},
      '丁': {'禄': '太阴', '权': '天同', '科': '天机', '忌': '巨门'},
      '戊': {'禄': '贪狼', '权': '太阴', '科': '右弼', '忌': '天机'},
      '己': {'禄': '武曲', '权': '贪狼', '科': '天梁', '忌': '文曲'},
      '庚': {'禄': '太阳', '权': '武曲', '科': '太阴', '忌': '天同'},
      '辛': {'禄': '巨门', '权': '太阳', '科': '文曲', '忌': '文昌'},
      '壬': {'禄': '天梁', '权': '紫微', '科': '左辅', '忌': '武曲'},
      '癸': {'禄': '破军', '权': '巨门', '科': '太阴', '忌': '贪狼'},
    };
    
    Map<String, String>? transforms = transformTable[yearStem];
    if (transforms != null) {
      transforms.forEach((transformType, starName) {
        // 找到原星所在宫位，添加化曜
        for (var palace in palaces) {
          if (palace.hasStar(starName)) {
            // 创建化曜星
            TransformStar transformStar = TransformStar(
              name: '化$transformType',
              transformType: _getTransformType(transformType),
              originalStar: starName,
              description: '$starName化$transformType',
              characteristics: ['化$transformType'],
              meaning: '$starName化$transformType的意义',
              palaceIndex: palace.index,
            );
            
            palace.addStar(transformStar);
            break;
          }
        }
      });
    }
  }
  
  /// 获取化曜类型
  static TransformType _getTransformType(String type) {
    switch (type) {
      case '禄':
        return TransformType.lu;
      case '权':
        return TransformType.quan;
      case '科':
        return TransformType.ke;
      case '忌':
        return TransformType.ji;
      default:
        return TransformType.lu;
    }
  }
  
  /// 计算大运
  static List<MajorPeriod> _calculateMajorPeriods(
    List<ZiweiPalace> palaces,
    int lifePalaceIndex,
    BirthData birthData,
  ) {
    List<MajorPeriod> majorPeriods = [];
    
    // 确定大运起始年龄和方向
    int startAge = _calculateMajorPeriodStartAge(birthData);
    bool clockwise = _isMajorPeriodClockwise(birthData);
    
    // 计算十个大运
    for (int i = 0; i < 10; i++) {
      int periodStartAge = startAge + i * 10;
      int periodEndAge = periodStartAge + 9;
      
      // 计算大运宫位
      int direction = clockwise ? 1 : -1;
      int periodPalaceIndex = (lifePalaceIndex + i * direction + 12) % 12;
      ZiweiPalace periodPalace = palaces[periodPalaceIndex];
      
      // 计算大运干支
      String periodStemBranch = _calculateMajorPeriodStemBranch(birthData.year + periodStartAge);
      
      // 生成大运描述
      String description = '第${i + 1}大运，行至${periodPalace.name}';
      
      majorPeriods.add(MajorPeriod(
        sequence: i + 1,
        startAge: periodStartAge,
        endAge: periodEndAge,
        palace: periodPalace,
        stemBranch: periodStemBranch,
        description: description,
      ));
    }
    
    return majorPeriods;
  }
  
  /// 计算大运起始年龄
  static int _calculateMajorPeriodStartAge(BirthData birthData) {
    // 根据性别和年干确定大运起始年龄
    // 这里使用简化算法，实际需要复杂计算
    return birthData.gender == 'male' ? 6 : 4;
  }
  
  /// 判断大运是否顺行
  static bool _isMajorPeriodClockwise(BirthData birthData) {
    // 根据性别和年干确定大运方向
    // 这里使用简化算法
    return birthData.gender == 'male';
  }
  
  /// 计算大运干支
  static String _calculateMajorPeriodStemBranch(int year) {
    const stems = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    const branches = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    
    int stemIndex = (year - 4) % 10;
    int branchIndex = (year - 4) % 12;
    
    return stems[stemIndex] + branches[branchIndex];
  }
}
