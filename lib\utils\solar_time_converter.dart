// lib/utils/solar_time_converter.dart
import 'dart:math' as math;
import '../services/paipan_service.dart';

/// 太阳时转换工具类
/// 实现真太阳时和平太阳时的相互转换
/// 重构后与 PaipanService 保持一致
class SolarTimeConverter {
  static final PaipanService _paipanService = PaipanService();
  
  /// 计算真太阳时与平太阳时的时差（分钟）
  /// [dayOfYear] 一年中的第几天 (1-365/366)
  /// 返回值：正数表示真太阳时比平太阳时快，负数表示慢
  static double getEquationOfTime(int dayOfYear) {
    // 将一年中的天数转换为弧度
    double n = dayOfYear - 1;
    double L = (280.460 + 0.9856474 * n) * math.pi / 180; // 平均黄经
    double g = (357.528 + 0.9856003 * n) * math.pi / 180; // 平均近点角
    
    // 计算时差方程（单位：分钟）
    double equationOfTime = 4 * (L - 0.0057183 - math.atan2(math.tan(L), math.cos(23.44 * math.pi / 180)));
    equationOfTime += 4 * 0.0167 * math.sin(2 * g);
    
    return equationOfTime;
  }
  
  /// 将北京时间转换为地方时间
  /// [dateTime] 北京时间（平太阳时）
  /// [longitude] 经度（东经为正，西经为负）
  /// 返回地方时间（真太阳时）
  static DateTime meanToApparentSolarTime(DateTime dateTime, double longitude) {
    int dayOfYear = dateTime.difference(DateTime(dateTime.year, 1, 1)).inDays + 1;

    // 计算时差方程（分钟）
    double equationOfTime = getEquationOfTime(dayOfYear);

    // 计算经度时差（每度4分钟，东经比北京时间早，西经比北京时间晚）
    double longitudeCorrection = (longitude - 120) * 4; // 以东经120度为基准

    // 总时差（分钟）
    double totalCorrection = equationOfTime + longitudeCorrection;

    return dateTime.add(Duration(minutes: totalCorrection.round()));
  }
  
  /// 将地方时间转换为北京时间
  /// [dateTime] 地方时间（真太阳时）
  /// [longitude] 经度（东经为正，西经为负）
  /// 返回北京时间（平太阳时）
  static DateTime apparentToMeanSolarTime(DateTime dateTime, double longitude) {
    int dayOfYear = dateTime.difference(DateTime(dateTime.year, 1, 1)).inDays + 1;

    // 计算时差方程（分钟）
    double equationOfTime = getEquationOfTime(dayOfYear);

    // 计算经度时差（每度4分钟）
    double longitudeCorrection = (longitude - 120) * 4; // 以东经120度为基准

    // 总时差（分钟）
    double totalCorrection = equationOfTime + longitudeCorrection;

    return dateTime.subtract(Duration(minutes: totalCorrection.round()));
  }
  
  /// 根据真太阳时计算时辰
  /// [apparentSolarTime] 真太阳时
  /// 返回时辰索引 (0-11，对应子丑寅卯辰巳午未申酉戌亥)
  static int getHourIndex(DateTime apparentSolarTime) {
    int hour = apparentSolarTime.hour;
    int minute = apparentSolarTime.minute;
    
    // 将时间转换为分钟数
    int totalMinutes = hour * 60 + minute;
    
    // 子时从23:00开始，到次日1:00结束
    // 每个时辰2小时 = 120分钟
    int hourIndex;
    
    if (totalMinutes >= 23 * 60) {
      // 23:00-24:00 属于子时
      hourIndex = 0;
    } else {
      // 1:00-23:00 按正常计算
      hourIndex = ((totalMinutes + 60) ~/ 120) % 12;
    }
    
    return hourIndex;
  }
  
  /// 获取时辰名称
  /// [hourIndex] 时辰索引 (0-11)
  static String getHourName(int hourIndex) {
    const hourNames = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    return hourNames[hourIndex % 12];
  }
  
  /// 获取时辰的现代时间范围
  /// [hourIndex] 时辰索引 (0-11)
  static String getHourRange(int hourIndex) {
    const ranges = [
      '23:00-01:00', '01:00-03:00', '03:00-05:00', '05:00-07:00',
      '07:00-09:00', '09:00-11:00', '11:00-13:00', '13:00-15:00',
      '15:00-17:00', '17:00-19:00', '19:00-21:00', '21:00-23:00'
    ];
    return ranges[hourIndex % 12];
  }
  
  /// 计算指定地点的日出时间（真太阳时）
  /// [date] 日期
  /// [latitude] 纬度
  /// [longitude] 经度
  static DateTime? getSunrise(DateTime date, double latitude, double longitude) {
    return _calculateSunTime(date, latitude, longitude, true);
  }
  
  /// 计算指定地点的日落时间（真太阳时）
  /// [date] 日期
  /// [latitude] 纬度
  /// [longitude] 经度
  static DateTime? getSunset(DateTime date, double latitude, double longitude) {
    return _calculateSunTime(date, latitude, longitude, false);
  }
  
  /// 内部方法：计算日出日落时间
  static DateTime? _calculateSunTime(DateTime date, double latitude, double longitude, bool isSunrise) {
    int dayOfYear = date.difference(DateTime(date.year, 1, 1)).inDays + 1;
    
    // 太阳赤纬角
    double declination = 23.45 * math.sin((360 * (284 + dayOfYear) / 365) * math.pi / 180);
    
    // 时角
    double hourAngle = math.acos(-math.tan(latitude * math.pi / 180) * math.tan(declination * math.pi / 180));
    
    if (hourAngle.isNaN) {
      return null; // 极昼或极夜
    }
    
    // 转换为小时
    double timeOffset = hourAngle * 12 / math.pi;
    
    // 计算日出或日落时间
    double solarNoon = 12 - (longitude - 120) / 15; // 真太阳正午时间
    double sunTime = isSunrise ? solarNoon - timeOffset : solarNoon + timeOffset;
    
    // 应用时差修正
    double equationOfTime = getEquationOfTime(dayOfYear);
    sunTime += equationOfTime / 60;
    
    int hour = sunTime.floor();
    int minute = ((sunTime - hour) * 60).round();
    
    return DateTime(date.year, date.month, date.day, hour, minute);
  }

  /// 使用 PaipanService 验证儒略日计算
  /// [dateTime] 日期时间
  /// 返回儒略日数
  static double? getJulianDay(DateTime dateTime) {
    return _paipanService.solar2Julian(
      dateTime.year, dateTime.month, dateTime.day,
      dateTime.hour, dateTime.minute, dateTime.second
    );
  }

  /// 使用 PaipanService 进行儒略日转公历
  /// [julianDay] 儒略日数
  /// 返回公历日期时间列表 [年, 月, 日, 时, 分, 秒]
  static List<int> julianToSolar(double julianDay) {
    return _paipanService.julian2Solar(julianDay);
  }
}
