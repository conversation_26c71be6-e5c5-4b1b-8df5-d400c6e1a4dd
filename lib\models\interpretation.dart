// lib/models/interpretation.dart
class Interpretation {
  final String id;
  final String chartId;
  final String type; // basic, advanced, professional
  final int overallScore; // 1-100
  final Map<String, double> lifeAspects; // 各方面评分
  final List<String> personalityTags; // 性格标签
  final Map<String, String> content; // 解读内容
  final bool aiGenerated; // 是否由AI生成
  
  Interpretation({
    required this.id,
    required this.chartId,
    required this.type,
    required this.overallScore,
    required this.lifeAspects,
    required this.personalityTags,
    required this.content,
    required this.aiGenerated,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'chartId': chartId,
      'type': type,
      'overallScore': overallScore,
      'lifeAspects': lifeAspects,
      'personalityTags': personalityTags,
      'content': content,
      'aiGenerated': aiGenerated,
    };
  }
  
  factory Interpretation.fromJson(Map<String, dynamic> json) {
    return Interpretation(
      id: json['id'] as String,
      chartId: json['chartId'] as String,
      type: json['type'] as String,
      overallScore: json['overallScore'] as int,
      lifeAspects: Map<String, double>.from(json['lifeAspects']),
      personalityTags: List<String>.from(json['personalityTags']),
      content: Map<String, String>.from(json['content']),
      aiGenerated: json['aiGenerated'] as bool,
    );
  }
}
