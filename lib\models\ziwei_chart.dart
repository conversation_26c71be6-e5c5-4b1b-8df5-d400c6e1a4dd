// lib/models/ziwei_chart.dart
// 紫微斗数命盘模型

import 'ziwei_palace.dart';
import 'ziwei_star.dart';
import '../models/birth_data.dart';
import '../models/advanced_settings.dart';

/// 大运信息
class MajorPeriod {
  /// 大运序号
  final int sequence;
  
  /// 起始年龄
  final int startAge;
  
  /// 结束年龄
  final int endAge;
  
  /// 大运宫位
  final ZiweiPalace palace;
  
  /// 大运干支
  final String stemBranch;
  
  /// 大运描述
  final String description;
  
  const MajorPeriod({
    required this.sequence,
    required this.startAge,
    required this.endAge,
    required this.palace,
    required this.stemBranch,
    required this.description,
  });
  
  /// 获取大运年数
  int get duration => endAge - startAge + 1;
  
  /// 检查指定年龄是否在此大运期间
  bool containsAge(int age) {
    return age >= startAge && age <= endAge;
  }
  
  Map<String, dynamic> toMap() {
    return {
      'sequence': sequence,
      'startAge': startAge,
      'endAge': endAge,
      'duration': duration,
      'palace': palace.toMap(),
      'stemBranch': stemBranch,
      'description': description,
    };
  }
}

/// 流年信息
class AnnualLuck {
  /// 流年年份
  final int year;
  
  /// 流年年龄
  final int age;
  
  /// 流年宫位
  final ZiweiPalace palace;
  
  /// 流年干支
  final String stemBranch;
  
  /// 流年飞星
  final List<ZiweiStar> flyingStars;
  
  /// 流年描述
  final String description;
  
  const AnnualLuck({
    required this.year,
    required this.age,
    required this.palace,
    required this.stemBranch,
    required this.flyingStars,
    required this.description,
  });
  
  Map<String, dynamic> toMap() {
    return {
      'year': year,
      'age': age,
      'palace': palace.toMap(),
      'stemBranch': stemBranch,
      'flyingStars': flyingStars.map((star) => star.toMap()).toList(),
      'description': description,
    };
  }
}

/// 紫微斗数命盘
class ZiweiChart {
  /// 命盘ID
  final String id;
  
  /// 姓名
  final String name;
  
  /// 性别
  final String gender;
  
  /// 出生信息
  final BirthData birthData;
  
  /// 高级设置
  final AdvancedSettings settings;
  
  /// 十二宫位
  final List<ZiweiPalace> palaces;
  
  /// 命宫索引
  final int lifePalaceIndex;
  
  /// 身宫索引
  final int bodyPalaceIndex;
  
  /// 四柱干支
  final Map<String, String> fourPillars;
  
  /// 大运列表
  final List<MajorPeriod> majorPeriods;
  
  /// 创建时间
  final DateTime createdAt;
  
  ZiweiChart({
    required this.id,
    required this.name,
    required this.gender,
    required this.birthData,
    required this.settings,
    required this.palaces,
    required this.lifePalaceIndex,
    required this.bodyPalaceIndex,
    required this.fourPillars,
    required this.majorPeriods,
    required this.createdAt,
  });
  
  /// 获取命宫
  ZiweiPalace get lifePalace => palaces[lifePalaceIndex];
  
  /// 获取身宫
  ZiweiPalace get bodyPalace => palaces[bodyPalaceIndex];
  
  /// 获取指定类型的宫位
  ZiweiPalace? getPalaceByType(PalaceType type) {
    try {
      return palaces.firstWhere((palace) => palace.type == type);
    } catch (e) {
      return null;
    }
  }
  
  /// 获取指定索引的宫位
  ZiweiPalace? getPalaceByIndex(int index) {
    if (index >= 0 && index < palaces.length) {
      return palaces[index];
    }
    return null;
  }
  
  /// 查找包含指定星曜的宫位
  List<ZiweiPalace> findPalacesWithStar(String starName) {
    return palaces.where((palace) => palace.hasStar(starName)).toList();
  }
  
  /// 获取所有主星
  List<ZiweiStar> getAllMainStars() {
    List<ZiweiStar> allMainStars = [];
    for (var palace in palaces) {
      allMainStars.addAll(palace.mainStars);
    }
    return allMainStars;
  }
  
  /// 获取所有辅星
  List<ZiweiStar> getAllAuxiliaryStars() {
    List<ZiweiStar> allAuxiliaryStars = [];
    for (var palace in palaces) {
      allAuxiliaryStars.addAll(palace.auxiliaryStars);
    }
    return allAuxiliaryStars;
  }
  
  /// 获取所有化曜
  List<ZiweiStar> getAllTransformStars() {
    List<ZiweiStar> allTransformStars = [];
    for (var palace in palaces) {
      allTransformStars.addAll(palace.transformStars);
    }
    return allTransformStars;
  }
  
  /// 计算指定年龄的流年
  AnnualLuck calculateAnnualLuck(int age) {
    int year = birthData.year + age;
    
    // 计算流年宫位（从命宫开始，按年龄推算）
    int annualPalaceIndex = (lifePalaceIndex + age) % 12;
    ZiweiPalace annualPalace = palaces[annualPalaceIndex];
    
    // 计算流年干支
    String annualStemBranch = _calculateAnnualStemBranch(year);
    
    // 计算流年飞星（简化版）
    List<ZiweiStar> flyingStars = _calculateFlyingStars(year, age);
    
    // 生成流年描述
    String description = _generateAnnualDescription(annualPalace, flyingStars);
    
    return AnnualLuck(
      year: year,
      age: age,
      palace: annualPalace,
      stemBranch: annualStemBranch,
      flyingStars: flyingStars,
      description: description,
    );
  }
  
  /// 获取当前大运
  MajorPeriod? getCurrentMajorPeriod(int currentAge) {
    try {
      return majorPeriods.firstWhere((period) => period.containsAge(currentAge));
    } catch (e) {
      return null;
    }
  }
  
  /// 获取命盘强度分析
  Map<String, dynamic> getStrengthAnalysis() {
    Map<String, double> palaceStrengths = {};
    double totalStrength = 0.0;
    
    for (var palace in palaces) {
      double strength = palace.strength;
      palaceStrengths[palace.name] = strength;
      totalStrength += strength;
    }
    
    return {
      'palaceStrengths': palaceStrengths,
      'totalStrength': totalStrength,
      'averageStrength': totalStrength / 12,
      'strongestPalace': palaceStrengths.entries
          .reduce((a, b) => a.value > b.value ? a : b)
          .key,
      'weakestPalace': palaceStrengths.entries
          .reduce((a, b) => a.value < b.value ? a : b)
          .key,
    };
  }
  
  /// 获取五行分析
  Map<String, dynamic> getElementAnalysis() {
    Map<StarElement, int> elementCounts = {};
    
    for (var palace in palaces) {
      for (var star in palace.stars) {
        elementCounts[star.element] = (elementCounts[star.element] ?? 0) + 1;
      }
    }
    
    return {
      'elementCounts': elementCounts.map((k, v) => MapEntry(k.toString(), v)),
      'dominantElement': elementCounts.entries
          .reduce((a, b) => a.value > b.value ? a : b)
          .key.toString(),
      'weakestElement': elementCounts.entries
          .reduce((a, b) => a.value < b.value ? a : b)
          .key.toString(),
    };
  }
  
  /// 计算流年干支
  String _calculateAnnualStemBranch(int year) {
    const stems = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    const branches = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    
    int stemIndex = (year - 4) % 10;
    int branchIndex = (year - 4) % 12;
    
    return stems[stemIndex] + branches[branchIndex];
  }
  
  /// 计算流年飞星
  List<ZiweiStar> _calculateFlyingStars(int year, int age) {
    // 简化的流年飞星计算
    // 实际应该根据复杂的飞星规则计算
    return [];
  }
  
  /// 生成流年描述
  String _generateAnnualDescription(ZiweiPalace palace, List<ZiweiStar> flyingStars) {
    String description = '流年行至${palace.name}，';
    
    if (palace.mainStars.isNotEmpty) {
      description += '主星有${palace.mainStars.map((s) => s.name).join('、')}，';
    }
    
    description += '主管${palace.domains.join('、')}等事项。';
    
    return description;
  }
  
  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'gender': gender,
      'birthData': birthData.toMap(),
      'settings': settings.toJson(),
      'palaces': palaces.map((palace) => palace.toMap()).toList(),
      'lifePalaceIndex': lifePalaceIndex,
      'bodyPalaceIndex': bodyPalaceIndex,
      'fourPillars': fourPillars,
      'majorPeriods': majorPeriods.map((period) => period.toMap()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'strengthAnalysis': getStrengthAnalysis(),
      'elementAnalysis': getElementAnalysis(),
    };
  }
  
  /// 从Map创建
  static ZiweiChart fromMap(Map<String, dynamic> map) {
    // 实现从Map创建ZiweiChart的逻辑
    // 这里需要复杂的反序列化逻辑
    throw UnimplementedError('fromMap method needs to be implemented');
  }
}
