// lib/data/china_cities.dart
// 中国城市经纬度数据库

import 'dart:math' as math;
import '../models/location.dart';

/// 中国城市数据库
class ChinaCitiesDatabase {
  
  // 中国主要城市经纬度数据
  static const List<Map<String, dynamic>> _citiesData = [
    // 直辖市
    {'name': '北京市', 'province': '北京市', 'latitude': 39.9042, 'longitude': 116.4074},
    {'name': '上海市', 'province': '上海市', 'latitude': 31.2304, 'longitude': 121.4737},
    {'name': '天津市', 'province': '天津市', 'latitude': 39.3434, 'longitude': 117.3616},
    {'name': '重庆市', 'province': '重庆市', 'latitude': 29.5647, 'longitude': 106.5507},
    
    // 省会城市
    {'name': '广州市', 'province': '广东省', 'latitude': 23.1291, 'longitude': 113.2644},
    {'name': '深圳市', 'province': '广东省', 'latitude': 22.5431, 'longitude': 114.0579},
    {'name': '杭州市', 'province': '浙江省', 'latitude': 30.2741, 'longitude': 120.1551},
    {'name': '南京市', 'province': '江苏省', 'latitude': 32.0603, 'longitude': 118.7969},
    {'name': '武汉市', 'province': '湖北省', 'latitude': 30.5928, 'longitude': 114.3055},
    {'name': '成都市', 'province': '四川省', 'latitude': 30.5728, 'longitude': 104.0668},
    {'name': '西安市', 'province': '陕西省', 'latitude': 34.3416, 'longitude': 108.9398},
    {'name': '郑州市', 'province': '河南省', 'latitude': 34.7466, 'longitude': 113.6254},
    {'name': '济南市', 'province': '山东省', 'latitude': 36.6512, 'longitude': 117.1201},
    {'name': '青岛市', 'province': '山东省', 'latitude': 36.0986, 'longitude': 120.3719},
    {'name': '大连市', 'province': '辽宁省', 'latitude': 38.9140, 'longitude': 121.6147},
    {'name': '沈阳市', 'province': '辽宁省', 'latitude': 41.8057, 'longitude': 123.4315},
    {'name': '长春市', 'province': '吉林省', 'latitude': 43.8171, 'longitude': 125.3235},
    {'name': '哈尔滨市', 'province': '黑龙江省', 'latitude': 45.8038, 'longitude': 126.5349},
    {'name': '昆明市', 'province': '云南省', 'latitude': 25.0389, 'longitude': 102.7183},
    {'name': '贵阳市', 'province': '贵州省', 'latitude': 26.5783, 'longitude': 106.7135},
    {'name': '南宁市', 'province': '广西壮族自治区', 'latitude': 22.8170, 'longitude': 108.3669},
    {'name': '海口市', 'province': '海南省', 'latitude': 20.0444, 'longitude': 110.1999},
    {'name': '三亚市', 'province': '海南省', 'latitude': 18.2479, 'longitude': 109.5146},
    {'name': '长沙市', 'province': '湖南省', 'latitude': 28.2282, 'longitude': 112.9388},
    {'name': '南昌市', 'province': '江西省', 'latitude': 28.6820, 'longitude': 115.8579},
    {'name': '合肥市', 'province': '安徽省', 'latitude': 31.8206, 'longitude': 117.2272},
    {'name': '福州市', 'province': '福建省', 'latitude': 26.0745, 'longitude': 119.2965},
    {'name': '厦门市', 'province': '福建省', 'latitude': 24.4798, 'longitude': 118.0894},
    {'name': '太原市', 'province': '山西省', 'latitude': 37.8706, 'longitude': 112.5489},
    {'name': '石家庄市', 'province': '河北省', 'latitude': 38.0428, 'longitude': 114.5149},
    {'name': '呼和浩特市', 'province': '内蒙古自治区', 'latitude': 40.8414, 'longitude': 111.7519},
    {'name': '银川市', 'province': '宁夏回族自治区', 'latitude': 38.4872, 'longitude': 106.2309},
    {'name': '西宁市', 'province': '青海省', 'latitude': 36.6171, 'longitude': 101.7782},
    {'name': '兰州市', 'province': '甘肃省', 'latitude': 36.0611, 'longitude': 103.8343},
    {'name': '乌鲁木齐市', 'province': '新疆维吾尔自治区', 'latitude': 43.8256, 'longitude': 87.6168},
    {'name': '拉萨市', 'province': '西藏自治区', 'latitude': 29.6520, 'longitude': 91.1721},
    
    // 其他重要城市
    {'name': '苏州市', 'province': '江苏省', 'latitude': 31.2989, 'longitude': 120.5853},
    {'name': '无锡市', 'province': '江苏省', 'latitude': 31.4912, 'longitude': 120.3119},
    {'name': '常州市', 'province': '江苏省', 'latitude': 31.7976, 'longitude': 119.9462},
    {'name': '宁波市', 'province': '浙江省', 'latitude': 29.8683, 'longitude': 121.5440},
    {'name': '温州市', 'province': '浙江省', 'latitude': 28.0006, 'longitude': 120.6994},
    {'name': '嘉兴市', 'province': '浙江省', 'latitude': 30.7463, 'longitude': 120.7550},
    {'name': '佛山市', 'province': '广东省', 'latitude': 23.0218, 'longitude': 113.1219},
    {'name': '东莞市', 'province': '广东省', 'latitude': 23.0205, 'longitude': 113.7518},
    {'name': '中山市', 'province': '广东省', 'latitude': 22.5158, 'longitude': 113.3927},
    {'name': '珠海市', 'province': '广东省', 'latitude': 22.2711, 'longitude': 113.5767},
    {'name': '汕头市', 'province': '广东省', 'latitude': 23.3540, 'longitude': 116.6816},
    {'name': '惠州市', 'province': '广东省', 'latitude': 23.1115, 'longitude': 114.4152},
    {'name': '烟台市', 'province': '山东省', 'latitude': 37.4638, 'longitude': 121.4478},
    {'name': '潍坊市', 'province': '山东省', 'latitude': 36.7069, 'longitude': 119.1019},
    {'name': '临沂市', 'province': '山东省', 'latitude': 35.1041, 'longitude': 118.3563},
    {'name': '洛阳市', 'province': '河南省', 'latitude': 34.6197, 'longitude': 112.4540},
    {'name': '开封市', 'province': '河南省', 'latitude': 34.7972, 'longitude': 114.3074},
    {'name': '绵阳市', 'province': '四川省', 'latitude': 31.4678, 'longitude': 104.6794},
    {'name': '德阳市', 'province': '四川省', 'latitude': 31.1270, 'longitude': 104.3982},
    {'name': '宜宾市', 'province': '四川省', 'latitude': 28.7602, 'longitude': 104.6308},
    {'name': '包头市', 'province': '内蒙古自治区', 'latitude': 40.6562, 'longitude': 109.8403},
    {'name': '鄂尔多斯市', 'province': '内蒙古自治区', 'latitude': 39.6086, 'longitude': 109.9896},
    {'name': '大庆市', 'province': '黑龙江省', 'latitude': 46.5907, 'longitude': 125.1031},
    {'name': '齐齐哈尔市', 'province': '黑龙江省', 'latitude': 47.3543, 'longitude': 123.9180},
    {'name': '吉林市', 'province': '吉林省', 'latitude': 43.8436, 'longitude': 126.5449},
    {'name': '延边朝鲜族自治州', 'province': '吉林省', 'latitude': 42.9048, 'longitude': 129.5089},
    {'name': '唐山市', 'province': '河北省', 'latitude': 39.6243, 'longitude': 118.1944},
    {'name': '保定市', 'province': '河北省', 'latitude': 38.8738, 'longitude': 115.4648},
    {'name': '邯郸市', 'province': '河北省', 'latitude': 36.6253, 'longitude': 114.5389},
    {'name': '秦皇岛市', 'province': '河北省', 'latitude': 39.9398, 'longitude': 119.6006},
    {'name': '廊坊市', 'province': '河北省', 'latitude': 39.5038, 'longitude': 116.7030},
    {'name': '承德市', 'province': '河北省', 'latitude': 40.9739, 'longitude': 117.9616},
    {'name': '张家口市', 'province': '河北省', 'latitude': 40.8111, 'longitude': 114.8794},
    {'name': '衡水市', 'province': '河北省', 'latitude': 37.7161, 'longitude': 115.6656},
    {'name': '沧州市', 'province': '河北省', 'latitude': 38.3037, 'longitude': 116.8575},
    {'name': '邢台市', 'province': '河北省', 'latitude': 37.0682, 'longitude': 114.5086},
  ];
  
  /// 获取所有城市列表
  static List<Location> getAllCities() {
    return _citiesData.map((data) => Location(
      country: '中国',
      province: data['province'],
      city: data['name'],
      latitude: data['latitude'],
      longitude: data['longitude'],
    )).toList();
  }
  
  /// 根据经度查找附近的城市（±2度范围内）
  static List<Location> getCitiesByLongitude(double longitude, {double tolerance = 2.0}) {
    return _citiesData
        .where((data) => (data['longitude'] - longitude).abs() <= tolerance)
        .map((data) => Location(
          country: '中国',
          province: data['province'],
          city: data['name'],
          latitude: data['latitude'],
          longitude: data['longitude'],
        ))
        .toList();
  }
  
  /// 根据城市名称查找城市
  static Location? getCityByName(String cityName) {
    try {
      var data = _citiesData.firstWhere(
        (city) => city['name'] == cityName || city['name'].contains(cityName),
      );
      return Location(
        country: '中国',
        province: data['province'],
        city: data['name'],
        latitude: data['latitude'],
        longitude: data['longitude'],
      );
    } catch (e) {
      return null;
    }
  }
  
  /// 根据省份获取城市列表
  static List<Location> getCitiesByProvince(String province) {
    return _citiesData
        .where((data) => data['province'] == province)
        .map((data) => Location(
          country: '中国',
          province: data['province'],
          city: data['name'],
          latitude: data['latitude'],
          longitude: data['longitude'],
        ))
        .toList();
  }
  
  /// 获取所有省份列表
  static List<String> getAllProvinces() {
    Set<String> provinces = _citiesData.map((data) => data['province'] as String).toSet();
    return provinces.toList()..sort();
  }
  
  /// 搜索城市（支持模糊搜索）
  static List<Location> searchCities(String keyword) {
    if (keyword.isEmpty) return [];
    
    return _citiesData
        .where((data) => 
          data['name'].toString().contains(keyword) || 
          data['province'].toString().contains(keyword))
        .map((data) => Location(
          country: '中国',
          province: data['province'],
          city: data['name'],
          latitude: data['latitude'],
          longitude: data['longitude'],
        ))
        .toList();
  }
  
  /// 计算两个城市之间的距离（公里）
  static double calculateDistance(Location city1, Location city2) {
    const double earthRadius = 6371; // 地球半径（公里）
    
    double lat1Rad = city1.latitude * (3.14159 / 180);
    double lat2Rad = city2.latitude * (3.14159 / 180);
    double deltaLatRad = (city2.latitude - city1.latitude) * (3.14159 / 180);
    double deltaLonRad = (city2.longitude - city1.longitude) * (3.14159 / 180);
    
    double a = math.sin(deltaLatRad / 2) * math.sin(deltaLatRad / 2) +
               math.cos(lat1Rad) * math.cos(lat2Rad) *
               math.sin(deltaLonRad / 2) * math.sin(deltaLonRad / 2);
    double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    
    return earthRadius * c;
  }
  
  /// 查找最近的城市
  static Location? findNearestCity(double latitude, double longitude) {
    if (_citiesData.isEmpty) return null;
    
    Location targetLocation = Location(
      country: '中国',
      province: '',
      city: '',
      latitude: latitude,
      longitude: longitude,
    );
    
    Location? nearestCity;
    double minDistance = double.infinity;
    
    for (var data in _citiesData) {
      Location city = Location(
        country: '中国',
        province: data['province'],
        city: data['name'],
        latitude: data['latitude'],
        longitude: data['longitude'],
      );
      
      double distance = calculateDistance(targetLocation, city);
      if (distance < minDistance) {
        minDistance = distance;
        nearestCity = city;
      }
    }
    
    return nearestCity;
  }
}
