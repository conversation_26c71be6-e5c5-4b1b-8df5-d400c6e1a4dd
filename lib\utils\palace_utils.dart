// lib/utils/palace_utils.dart
class PalaceUtils {
  // 十二宫位名称
  static const List<String> palaceNames = [
    '命宫', '兄弟宫', '夫妻宫', '子女宫', '财帛宫', '疾厄宫',
    '迁移宫', '交友宫', '官禄宫', '田宅宫', '福德宫', '父母宫'
  ];
  
  // 十二地支
  static const List<String> earthBranches = [
    '子', '丑', '寅', '卯', '辰', '巳',
    '午', '未', '申', '酉', '戌', '亥'
  ];
  
  // 十天干
  static const List<String> heavenlyStems = [
    '甲', '乙', '丙', '丁', '戊', '己',
    '庚', '辛', '壬', '癸'
  ];
  
  // 获取宫位含义
  static String getPalaceMeaning(String palaceName) {
    final meanings = {
      '命宫': '代表个人的先天运势、性格特质、人生格局',
      '兄弟宫': '代表兄弟姐妹关系、朋友交往、合作伙伴',
      '夫妻宫': '代表婚姻感情、配偶状况、异性缘分',
      '子女宫': '代表子女运势、创作能力、部属关系',
      '财帛宫': '代表财运状况、理财能力、赚钱方式',
      '疾厄宫': '代表健康状况、意外灾厄、抗压能力',
      '迁移宫': '代表外出运势、环境变化、人际关系',
      '交友宫': '代表朋友关系、社交能力、人脉资源',
      '官禄宫': '代表事业运势、工作状况、社会地位',
      '田宅宫': '代表不动产、居住环境、祖业根基',
      '福德宫': '代表精神享受、兴趣爱好、福分厚薄',
      '父母宫': '代表父母关系、长辈缘分、学习能力'
    };
    return meanings[palaceName] ?? '未知宫位';
  }
  
  // 获取地支属性
  static Map<String, dynamic> getEarthBranchProperties(String earthBranch) {
    final properties = {
      '子': {'element': '水', 'direction': '北', 'time': '23-01时', 'season': '冬'},
      '丑': {'element': '土', 'direction': '东北', 'time': '01-03时', 'season': '冬'},
      '寅': {'element': '木', 'direction': '东北', 'time': '03-05时', 'season': '春'},
      '卯': {'element': '木', 'direction': '东', 'time': '05-07时', 'season': '春'},
      '辰': {'element': '土', 'direction': '东南', 'time': '07-09时', 'season': '春'},
      '巳': {'element': '火', 'direction': '东南', 'time': '09-11时', 'season': '夏'},
      '午': {'element': '火', 'direction': '南', 'time': '11-13时', 'season': '夏'},
      '未': {'element': '土', 'direction': '西南', 'time': '13-15时', 'season': '夏'},
      '申': {'element': '金', 'direction': '西南', 'time': '15-17时', 'season': '秋'},
      '酉': {'element': '金', 'direction': '西', 'time': '17-19时', 'season': '秋'},
      '戌': {'element': '土', 'direction': '西北', 'time': '19-21时', 'season': '秋'},
      '亥': {'element': '水', 'direction': '西北', 'time': '21-23时', 'season': '冬'},
    };
    return properties[earthBranch] ?? {};
  }
  
  // 获取天干属性
  static Map<String, dynamic> getHeavenlyStemProperties(String heavenlyStem) {
    final properties = {
      '甲': {'element': '木', 'nature': '阳', 'direction': '东'},
      '乙': {'element': '木', 'nature': '阴', 'direction': '东'},
      '丙': {'element': '火', 'nature': '阳', 'direction': '南'},
      '丁': {'element': '火', 'nature': '阴', 'direction': '南'},
      '戊': {'element': '土', 'nature': '阳', 'direction': '中'},
      '己': {'element': '土', 'nature': '阴', 'direction': '中'},
      '庚': {'element': '金', 'nature': '阳', 'direction': '西'},
      '辛': {'element': '金', 'nature': '阴', 'direction': '西'},
      '壬': {'element': '水', 'nature': '阳', 'direction': '北'},
      '癸': {'element': '水', 'nature': '阴', 'direction': '北'},
    };
    return properties[heavenlyStem] ?? {};
  }
  
  // 计算宫位强弱
  static int calculatePalaceStrength(List<String> mainStars, List<String> minorStars, List<String> transformStars) {
    int strength = 0;
    
    // 主星加分
    for (String star in mainStars) {
      switch (star) {
        case '紫微':
        case '天府':
          strength += 5;
          break;
        case '太阳':
        case '太阴':
          strength += 4;
          break;
        default:
          strength += 3;
      }
    }
    
    // 辅星加分
    for (String star in minorStars) {
      switch (star) {
        case '左辅':
        case '右弼':
        case '文昌':
        case '文曲':
          strength += 2;
          break;
        case '擎羊':
        case '陀罗':
        case '火星':
        case '铃星':
          strength -= 1;
          break;
        case '地空':
        case '地劫':
          strength -= 2;
          break;
        default:
          strength += 1;
      }
    }
    
    // 化星加分
    for (String star in transformStars) {
      switch (star) {
        case '化禄':
          strength += 3;
          break;
        case '化权':
          strength += 2;
          break;
        case '化科':
          strength += 2;
          break;
        case '化忌':
          strength -= 2;
          break;
      }
    }
    
    return strength.clamp(0, 20);
  }
  
  // 获取宫位吉凶
  static String getPalaceFortune(int strength) {
    if (strength >= 15) return '大吉';
    if (strength >= 12) return '中吉';
    if (strength >= 8) return '小吉';
    if (strength >= 5) return '平常';
    if (strength >= 2) return '小凶';
    return '大凶';
  }
  
  // 获取宫位建议
  static String getPalaceAdvice(String palaceName, int strength) {
    final advice = {
      '命宫': strength >= 10 ? '命格不错，宜积极进取' : '需要努力提升自我修养',
      '兄弟宫': strength >= 10 ? '兄弟朋友助力大，宜多交往' : '需要改善人际关系',
      '夫妻宫': strength >= 10 ? '感情运佳，宜珍惜缘分' : '感情需要用心经营',
      '子女宫': strength >= 10 ? '子女运佳，教育有方' : '需要多关心子女教育',
      '财帛宫': strength >= 10 ? '财运不错，宜理财投资' : '需要谨慎理财，开源节流',
      '疾厄宫': strength >= 10 ? '身体健康，注意保养' : '需要注意身体健康',
      '迁移宫': strength >= 10 ? '外出运佳，宜多发展' : '外出需要谨慎小心',
      '交友宫': strength >= 10 ? '人缘佳，贵人多助' : '需要改善社交关系',
      '官禄宫': strength >= 10 ? '事业运佳，宜积极发展' : '事业需要努力突破',
      '田宅宫': strength >= 10 ? '不动产运佳，宜投资' : '购置不动产需谨慎',
      '福德宫': strength >= 10 ? '精神愉快，享受生活' : '需要调整心态，培养兴趣',
      '父母宫': strength >= 10 ? '与长辈关系和睦' : '需要孝顺长辈，改善关系'
    };
    return advice[palaceName] ?? '需要综合分析';
  }
}
