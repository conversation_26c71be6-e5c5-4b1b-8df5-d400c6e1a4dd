// lib/screens/bazi/bazi_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../../models/birth_data.dart';
import '../../models/location.dart';
import '../../services/paipan_service.dart';
import '../../models/advanced_settings.dart';
import '../../services/settings_service.dart';

class BaziScreen extends StatefulWidget {
  @override
  _BaziScreenState createState() => _BaziScreenState();
}

class _BaziScreenState extends State<BaziScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  
  // 输入数据
  DateTime _birthDate = DateTime.now();
  TimeOfDay _birthTime = TimeOfDay.now();
  bool _isLunar = false;
  bool _isLeapMonth = false;
  String _gender = 'male';
  Location _birthLocation = Location(
    country: '中国',
    province: '北京市',
    city: '北京市',
    latitude: 39.9042,
    longitude: 116.4074,
  );
  
  // 计算结果
  Map<String, dynamic>? _lunarInfo;
  Map<String, String>? _fourPillars;
  Map<String, dynamic>? _solarTermInfo;
  Map<String, dynamic>? _zodiacInfo;
  Map<String, dynamic>? _tenGodsInfo;
  Map<String, dynamic>? _naYinInfo;
  Map<String, dynamic>? _lifeStageInfo;
  List<Map<String, dynamic>>? _majorPeriods;
  
  // 服务
  final PaipanService _paipanService = PaipanService();
  AdvancedSettings _settings = AdvancedSettings.defaultSettings();
  
  // 状态
  bool _isCalculating = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadSettings();
    _performCalculations();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 加载设置
  Future<void> _loadSettings() async {
    try {
      _settings = await SettingsService.instance.loadAdvancedSettings();
      setState(() {});
    } catch (e) {
      print('加载设置失败: $e');
    }
  }

  /// 执行所有计算
  Future<void> _performCalculations() async {
    setState(() {
      _isCalculating = true;
    });

    try {
      // 创建完整的出生时间
      DateTime fullBirthTime = DateTime(
        _birthDate.year,
        _birthDate.month,
        _birthDate.day,
        _birthTime.hour,
        _birthTime.minute,
      );

      // 如果是农历，先转换为公历
      DateTime solarDate = fullBirthTime;
      if (_isLunar) {
        List<int>? solarResult = _paipanService.lunar2Solar(_birthDate.year, _birthDate.month, _birthDate.day, _isLeapMonth);
        if (solarResult != null && solarResult.length >= 3) {
          solarDate = DateTime(solarResult[0], solarResult[1], solarResult[2], _birthTime.hour, _birthTime.minute);
        }
      }

      DateTime fullSolarTime = DateTime(
        solarDate.year,
        solarDate.month,
        solarDate.day,
        _birthTime.hour,
        _birthTime.minute,
      );

      // 1. 使用统一的排盘服务进行计算
      Map<String, dynamic> fullInfo = _paipanService.getFullInfo(
        _gender == 'male' ? 0 : 1, // 性别：0男1女
        fullSolarTime.year,
        fullSolarTime.month,
        fullSolarTime.day,
        fullSolarTime.hour,
        fullSolarTime.minute,
        0 // 秒数
      );

      if (fullInfo.isNotEmpty) {
        // 2. 提取四柱信息
        _fourPillars = _extractFourPillars(fullInfo);

        // 3. 提取农历信息
        _lunarInfo = _extractLunarInfo(fullSolarTime);

        // 4. 星座生肖计算
        _zodiacInfo = _calculateZodiacInfo(fullSolarTime, _lunarInfo!);

        // 5. 节气信息
        _solarTermInfo = _getSolarTermInfo(fullSolarTime);

        // 6. 十神计算
        _tenGodsInfo = _calculateTenGods(_fourPillars!);

        // 7. 纳音计算
        _naYinInfo = _calculateNaYin(_fourPillars!);

        // 8. 长生十二宫计算
        _lifeStageInfo = _calculateLifeStage(_fourPillars!);

        // 9. 大运流年计算 - 使用PaipanService的正确计算结果
        _majorPeriods = _extractMajorPeriods(fullInfo);
      }

    } catch (e) {
      print('计算失败: $e');
    } finally {
      setState(() {
        _isCalculating = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('八字排盘'),
        backgroundColor: Colors.purple[700],
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(icon: Icon(Icons.input), text: '输入'),
            Tab(icon: Icon(Icons.auto_awesome), text: '八字'),
            Tab(icon: Icon(Icons.timeline), text: '大运'),
            Tab(icon: Icon(Icons.info), text: '详情'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildInputView(),
          _buildBaziView(),
          _buildMajorPeriodsView(),
          _buildDetailsView(),
        ],
      ),
    );
  }

  /// 构建输入视图
  Widget _buildInputView() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          _buildDateTimeInput(),
          SizedBox(height: 16),
          _buildLocationInput(),
          SizedBox(height: 16),
          _buildGenderInput(),
          SizedBox(height: 24),
          ElevatedButton.icon(
            icon: _isCalculating ? SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
            ) : Icon(Icons.calculate),
            label: Text(_isCalculating ? '计算中...' : '重新计算'),
            onPressed: _isCalculating ? null : _performCalculations,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple[700],
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建八字视图
  Widget _buildBaziView() {
    if (_isCalculating) {
      return Center(child: CircularProgressIndicator());
    }

    if (_fourPillars == null) {
      return Center(child: Text('请先输入出生信息并计算'));
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          _buildFourPillarsCard(),
          SizedBox(height: 16),
          _buildTenGodsCard(),
          SizedBox(height: 16),
          _buildNaYinCard(),
          SizedBox(height: 16),
          _buildLifeStageCard(),
        ],
      ),
    );
  }

  /// 构建大运视图
  Widget _buildMajorPeriodsView() {
    if (_isCalculating) {
      return Center(child: CircularProgressIndicator());
    }

    if (_majorPeriods == null) {
      return Center(child: Text('请先输入出生信息并计算'));
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: _majorPeriods!.length,
      itemBuilder: (context, index) {
        Map<String, dynamic> period = _majorPeriods![index];
        return Card(
          child: ListTile(
            title: Text('${period['name']} ${period['ganZhi']} (${period['startAge']}-${period['endAge']}岁)'),
            subtitle: Text('${period['startYear']}-${period['endYear']}年'),
            trailing: Icon(Icons.chevron_right),
            onTap: () {
              // 显示详细信息
            },
          ),
        );
      },
    );
  }

  /// 构建详情视图
  Widget _buildDetailsView() {
    if (_isCalculating) {
      return Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          if (_lunarInfo != null) _buildLunarInfoCard(),
          SizedBox(height: 16),
          if (_zodiacInfo != null) _buildZodiacInfoCard(),
          SizedBox(height: 16),
          if (_solarTermInfo != null) _buildSolarTermCard(),
        ],
      ),
    );
  }

  /// 构建日期时间输入
  Widget _buildDateTimeInput() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('出生日期时间', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                Switch(
                  value: _isLunar,
                  onChanged: (value) {
                    setState(() {
                      _isLunar = value;
                    });
                  },
                ),
              ],
            ),
            Text(_isLunar ? '农历' : '公历', style: TextStyle(color: Colors.grey[600])),
            SizedBox(height: 16),

            // 日期选择
            InkWell(
              onTap: () => _selectDate(context),
              child: Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.calendar_today),
                    SizedBox(width: 12),
                    Text('${_birthDate.year}年${_birthDate.month}月${_birthDate.day}日'),
                    Spacer(),
                    Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            ),

            SizedBox(height: 12),

            // 时间选择
            InkWell(
              onTap: () => _selectTime(context),
              child: Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.access_time),
                    SizedBox(width: 12),
                    Text('${_birthTime.hour.toString().padLeft(2, '0')}:${_birthTime.minute.toString().padLeft(2, '0')}'),
                    Spacer(),
                    Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建地点输入
  Widget _buildLocationInput() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('出生地点', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            SizedBox(height: 12),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.location_on),
                  SizedBox(width: 12),
                  Text(_birthLocation.name),
                  Spacer(),
                  Text('经度: ${_birthLocation.longitude.toStringAsFixed(2)}°',
                       style: TextStyle(color: Colors.grey[600], fontSize: 12)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建性别输入
  Widget _buildGenderInput() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('性别', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: Text('男'),
                    value: 'male',
                    groupValue: _gender,
                    onChanged: (value) {
                      setState(() {
                        _gender = value!;
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: Text('女'),
                    value: 'female',
                    groupValue: _gender,
                    onChanged: (value) {
                      setState(() {
                        _gender = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 选择日期
  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _birthDate,
      firstDate: DateTime(1900),
      lastDate: DateTime(2100),
    );
    if (picked != null && picked != _birthDate) {
      setState(() {
        _birthDate = picked;
      });
    }
  }

  /// 选择时间
  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: _birthTime,
    );
    if (picked != null && picked != _birthTime) {
      setState(() {
        _birthTime = picked;
      });
    }
  }

  /// 构建四柱八字卡片
  Widget _buildFourPillarsCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.auto_awesome, color: Colors.purple[600]),
                SizedBox(width: 8),
                Text('四柱八字', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            SizedBox(height: 16),

            // 四柱表格
            Table(
              border: TableBorder.all(color: Colors.grey[300]!),
              children: [
                TableRow(
                  decoration: BoxDecoration(color: Colors.grey[100]),
                  children: [
                    _buildTableCell('年柱', isHeader: true),
                    _buildTableCell('月柱', isHeader: true),
                    _buildTableCell('日柱', isHeader: true),
                    _buildTableCell('时柱', isHeader: true),
                  ],
                ),
                TableRow(
                  children: [
                    _buildTableCell(_fourPillars!['year']!),
                    _buildTableCell(_fourPillars!['month']!),
                    _buildTableCell(_fourPillars!['day']!),
                    _buildTableCell(_fourPillars!['hour']!),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建十神卡片
  Widget _buildTenGodsCard() {
    if (_tenGodsInfo == null) return Container();

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.psychology, color: Colors.blue[600]),
                SizedBox(width: 8),
                Text('十神分析', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            SizedBox(height: 16),
            ..._tenGodsInfo!.entries.map((entry) =>
              _buildInfoRow(entry.key, entry.value.toString())
            ).toList(),
          ],
        ),
      ),
    );
  }

  /// 构建纳音卡片
  Widget _buildNaYinCard() {
    if (_naYinInfo == null) return Container();

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.music_note, color: Colors.green[600]),
                SizedBox(width: 8),
                Text('纳音五行', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            SizedBox(height: 16),
            ..._naYinInfo!.entries.map((entry) =>
              _buildInfoRow(entry.key, entry.value.toString())
            ).toList(),
          ],
        ),
      ),
    );
  }

  /// 构建长生十二宫卡片
  Widget _buildLifeStageCard() {
    if (_lifeStageInfo == null) return Container();

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.timeline, color: Colors.orange[600]),
                SizedBox(width: 8),
                Text('长生十二宫', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            SizedBox(height: 16),
            ..._lifeStageInfo!.entries.map((entry) =>
              _buildInfoRow(entry.key, entry.value.toString())
            ).toList(),
          ],
        ),
      ),
    );
  }

  /// 构建农历信息卡片
  Widget _buildLunarInfoCard() {
    if (_lunarInfo == null) return Container();

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.brightness_2, color: Colors.orange[600]),
                SizedBox(width: 8),
                Text('农历信息', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            SizedBox(height: 16),
            _buildInfoRow('农历日期', '${_lunarInfo!['year'] ?? '未知'}年${_lunarInfo!['monthName'] ?? '未知月'}${_lunarInfo!['dayName'] ?? '未知日'}'),
            _buildInfoRow('生肖', _lunarInfo!['zodiac'] ?? '未知'),
            _buildInfoRow('闰月', (_lunarInfo!['isLeapMonth'] ?? false) ? '是' : '否'),
          ],
        ),
      ),
    );
  }

  /// 构建星座生肖卡片
  Widget _buildZodiacInfoCard() {
    if (_zodiacInfo == null) return Container();

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.star, color: Colors.amber[600]),
                SizedBox(width: 8),
                Text('星座生肖', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            SizedBox(height: 16),
            ..._zodiacInfo!.entries.map((entry) =>
              _buildInfoRow(entry.key, entry.value?.toString() ?? '未知')
            ).toList(),
          ],
        ),
      ),
    );
  }

  /// 构建节气卡片
  Widget _buildSolarTermCard() {
    if (_solarTermInfo == null) return Container();

    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.wb_sunny, color: Colors.amber[600]),
                SizedBox(width: 8),
                Text('节气信息', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ],
            ),
            SizedBox(height: 16),
            ..._solarTermInfo!.entries.map((entry) =>
              _buildInfoRow(entry.key, entry.value?.toString() ?? '未知')
            ).toList(),
          ],
        ),
      ),
    );
  }

  /// 构建表格单元格
  Widget _buildTableCell(String text, {bool isHeader = false}) {
    return Container(
      padding: EdgeInsets.all(12),
      child: Text(
        text,
        textAlign: TextAlign.center,
        style: TextStyle(
          fontWeight: isHeader ? FontWeight.bold : FontWeight.normal,
          fontSize: isHeader ? 14 : 16,
        ),
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label：',
              style: TextStyle(fontWeight: FontWeight.w500, color: Colors.grey[700]),
            ),
          ),
          Expanded(
            child: Text(value, style: TextStyle(color: Colors.black87)),
          ),
        ],
      ),
    );
  }

  /// 计算星座生肖信息
  Map<String, dynamic> _calculateZodiacInfo(DateTime solarDate, Map<String, dynamic> lunarInfo) {
    // 星座计算
    String constellation = _getConstellation(solarDate.month, solarDate.day);

    return {
      '星座': constellation,
      '生肖': lunarInfo['zodiac'] ?? '未知',
      '星座特征': _getConstellationTraits(constellation),
      '生肖特征': _getZodiacTraits(lunarInfo['zodiac'] ?? '未知'),
    };
  }

  /// 获取星座
  String _getConstellation(int month, int day) {
    const List<Map<String, dynamic>> constellations = [
      {'name': '水瓶座', 'start': [1, 20], 'end': [2, 18]},
      {'name': '双鱼座', 'start': [2, 19], 'end': [3, 20]},
      {'name': '白羊座', 'start': [3, 21], 'end': [4, 19]},
      {'name': '金牛座', 'start': [4, 20], 'end': [5, 20]},
      {'name': '双子座', 'start': [5, 21], 'end': [6, 21]},
      {'name': '巨蟹座', 'start': [6, 22], 'end': [7, 22]},
      {'name': '狮子座', 'start': [7, 23], 'end': [8, 22]},
      {'name': '处女座', 'start': [8, 23], 'end': [9, 22]},
      {'name': '天秤座', 'start': [9, 23], 'end': [10, 23]},
      {'name': '天蝎座', 'start': [10, 24], 'end': [11, 22]},
      {'name': '射手座', 'start': [11, 23], 'end': [12, 21]},
      {'name': '摩羯座', 'start': [12, 22], 'end': [1, 19]},
    ];

    for (var constellation in constellations) {
      List<int> start = constellation['start'];
      List<int> end = constellation['end'];

      if (start[0] <= end[0]) {
        // 同一年内
        if (month == start[0] && day >= start[1] ||
            month == end[0] && day <= end[1] ||
            month > start[0] && month < end[0]) {
          return constellation['name'];
        }
      } else {
        // 跨年
        if (month == start[0] && day >= start[1] ||
            month == end[0] && day <= end[1] ||
            month > start[0] || month < end[0]) {
          return constellation['name'];
        }
      }
    }
    return '未知';
  }

  /// 获取星座特征
  String _getConstellationTraits(String constellation) {
    const Map<String, String> traits = {
      '白羊座': '热情、冲动、勇敢',
      '金牛座': '稳重、固执、务实',
      '双子座': '聪明、善变、好奇',
      '巨蟹座': '温柔、敏感、顾家',
      '狮子座': '自信、大方、领导力强',
      '处女座': '细心、完美主义、分析力强',
      '天秤座': '优雅、和谐、犹豫不决',
      '天蝎座': '神秘、专注、占有欲强',
      '射手座': '自由、乐观、爱冒险',
      '摩羯座': '务实、有责任心、保守',
      '水瓶座': '独立、创新、理想主义',
      '双鱼座': '浪漫、直觉、富有同情心',
    };
    return traits[constellation] ?? '未知特征';
  }

  /// 获取生肖特征
  String _getZodiacTraits(String zodiac) {
    const Map<String, String> traits = {
      '鼠': '机智、灵活、适应力强',
      '牛': '勤劳、踏实、有耐心',
      '虎': '勇敢、自信、有领导力',
      '兔': '温和、谨慎、有艺术天赋',
      '龙': '自信、有魅力、富有想象力',
      '蛇': '智慧、神秘、直觉敏锐',
      '马': '热情、自由、精力充沛',
      '羊': '温柔、善良、有创造力',
      '猴': '聪明、机智、好奇心强',
      '鸡': '勤奋、准时、注重细节',
      '狗': '忠诚、可靠、有正义感',
      '猪': '善良、慷慨、享受生活',
    };
    return traits[zodiac] ?? '未知特征';
  }

  /// 从排盘服务结果中提取四柱信息
  Map<String, String> _extractFourPillars(Map<String, dynamic> fullInfo) {
    if (fullInfo['bazi'] == null) return {};

    List<List<String>> bazi = List<List<String>>.from(fullInfo['bazi']);
    if (bazi.length < 4) return {};

    return {
      'year': bazi[0][0] + bazi[0][1],
      'month': bazi[1][0] + bazi[1][1],
      'day': bazi[2][0] + bazi[2][1],
      'hour': bazi[3][0] + bazi[3][1],
    };
  }

  /// 提取农历信息
  Map<String, dynamic> _extractLunarInfo(DateTime solarDate) {
    List<int>? lunarResult = _paipanService.solar2Lunar(solarDate.year, solarDate.month, solarDate.day);

    if (lunarResult != null && lunarResult.length >= 4) {
      return {
        'year': lunarResult[0],
        'month': lunarResult[1],
        'day': lunarResult[2],
        'isLeapMonth': lunarResult[3] == 1,
      };
    }

    // 如果转换失败，返回默认值
    return {
      'year': solarDate.year,
      'month': solarDate.month,
      'day': solarDate.day,
      'isLeapMonth': false,
    };
  }

  /// 获取节气信息
  Map<String, dynamic> _getSolarTermInfo(DateTime dateTime) {
    try {
      // 使用PaipanService获取节气信息
      List<List<int>> jieQiList = _paipanService.get24JieQi(dateTime.year);

      const List<String> jieQiNames = [
        '立春', '雨水', '惊蛰', '春分', '清明', '谷雨',
        '立夏', '小满', '芒种', '夏至', '小暑', '大暑',
        '立秋', '处暑', '白露', '秋分', '寒露', '霜降',
        '立冬', '小雪', '大雪', '冬至', '小寒', '大寒'
      ];

      String nearestTerm = '';
      DateTime? nearestTime;

      for (int i = 0; i < jieQiList.length && i < jieQiNames.length; i++) {
        List<int> jieQiTime = jieQiList[i];
        if (jieQiTime.length >= 6) {
          DateTime time = DateTime(
            jieQiTime[0], jieQiTime[1], jieQiTime[2],
            jieQiTime[3], jieQiTime[4], jieQiTime[5],
          );

          if (time.isBefore(dateTime) || time.isAtSameMomentAs(dateTime)) {
            nearestTerm = jieQiNames[i];
            nearestTime = time;
          } else {
            break;
          }
        }
      }

      return {
        '最近节气': nearestTerm,
        '节气时间': nearestTime?.toString() ?? '',
        '节气季节': _getTermSeason(nearestTerm),
      };
    } catch (e) {
      return {'错误': '节气计算失败'};
    }
  }

  /// 获取节气季节
  String _getTermSeason(String term) {
    const Map<String, String> seasons = {
      '立春': '春季', '雨水': '春季', '惊蛰': '春季', '春分': '春季', '清明': '春季', '谷雨': '春季',
      '立夏': '夏季', '小满': '夏季', '芒种': '夏季', '夏至': '夏季', '小暑': '夏季', '大暑': '夏季',
      '立秋': '秋季', '处暑': '秋季', '白露': '秋季', '秋分': '秋季', '寒露': '秋季', '霜降': '秋季',
      '立冬': '冬季', '小雪': '冬季', '大雪': '冬季', '冬至': '冬季', '小寒': '冬季', '大寒': '冬季',
    };
    return seasons[term] ?? '未知';
  }

  /// 计算十神
  Map<String, dynamic> _calculateTenGods(Map<String, String> fourPillars) {
    // 简化的十神计算
    String dayHeavenlyStem = fourPillars['day']![0]; // 日干

    return {
      '日干': dayHeavenlyStem,
      '十神分析': '基于日干${dayHeavenlyStem}的十神分析',
      '性格特征': _getDayHeavenlyStemTraits(dayHeavenlyStem),
    };
  }

  /// 获取日干特征
  String _getDayHeavenlyStemTraits(String stem) {
    const Map<String, String> traits = {
      '甲': '正直、有领导力、积极向上',
      '乙': '温和、灵活、有艺术天赋',
      '丙': '热情、开朗、有感染力',
      '丁': '细腻、敏感、有创造力',
      '戊': '稳重、可靠、有责任心',
      '己': '温和、包容、善于协调',
      '庚': '刚强、果断、有正义感',
      '辛': '精致、敏锐、追求完美',
      '壬': '智慧、灵活、适应力强',
      '癸': '温柔、直觉、富有同情心',
    };
    return traits[stem] ?? '未知特征';
  }

  /// 计算纳音
  Map<String, dynamic> _calculateNaYin(Map<String, String> fourPillars) {
    Map<String, dynamic> naYinResult = {};

    // 使用排盘服务计算纳音
    List<String> pillars = ['year', 'month', 'day', 'hour'];
    List<String> pillarNames = ['年柱纳音', '月柱纳音', '日柱纳音', '时柱纳音'];

    for (int i = 0; i < pillars.length; i++) {
      String ganZhi = fourPillars[pillars[i]] ?? '';
      if (ganZhi.length >= 2) {
        // 从干支字符串中提取天干地支索引
        int tgIndex = _paipanService.ctg.indexOf(ganZhi[0]);
        int dzIndex = _paipanService.cdz.indexOf(ganZhi[1]);

        if (tgIndex >= 0 && dzIndex >= 0) {
          List<dynamic> naYinInfo = _paipanService.naYin(tgIndex, dzIndex);
          naYinResult[pillarNames[i]] = naYinInfo[0];
        } else {
          naYinResult[pillarNames[i]] = '未知纳音';
        }
      } else {
        naYinResult[pillarNames[i]] = '未知纳音';
      }
    }

    return naYinResult;
  }

  /// 计算长生十二宫
  Map<String, dynamic> _calculateLifeStage(Map<String, String> fourPillars) {
    String dayGanZhi = fourPillars['day'] ?? '';
    if (dayGanZhi.length < 2) {
      return {
        '日干': '未知',
        '长生宫位': '无法计算',
        '生命阶段': '无法确定',
      };
    }

    String dayHeavenlyStem = dayGanZhi[0];

    // 从干支字符串中提取天干地支索引
    int tgIndex = _paipanService.ctg.indexOf(dayHeavenlyStem);

    Map<String, dynamic> lifeStageResult = {};

    // 计算四柱的长生十二宫
    List<String> pillars = ['year', 'month', 'day', 'hour'];
    List<String> pillarNames = ['年支', '月支', '日支', '时支'];

    for (int i = 0; i < pillars.length; i++) {
      String ganZhi = fourPillars[pillars[i]] ?? '';
      if (ganZhi.length >= 2) {
        int dzIndex = _paipanService.cdz.indexOf(ganZhi[1]);
        if (tgIndex >= 0 && dzIndex >= 0) {
          Map<String, dynamic> csInfo = _paipanService.getCs(tgIndex, dzIndex);
          lifeStageResult[pillarNames[i]] = csInfo['char'];
        } else {
          lifeStageResult[pillarNames[i]] = '未知';
        }
      } else {
        lifeStageResult[pillarNames[i]] = '未知';
      }
    }

    lifeStageResult['日干'] = dayHeavenlyStem;

    return lifeStageResult;
  }

  /// 从PaipanService结果中提取大运信息
  List<Map<String, dynamic>> _extractMajorPeriods(Map<String, dynamic> fullInfo) {
    List<Map<String, dynamic>> periods = [];

    try {
      // 从PaipanService结果中提取大运信息
      List<String> bigGanZhi = List<String>.from(fullInfo['big'] ?? []);
      List<List<int>> bigStartTimes = List<List<int>>.from(fullInfo['big_start_time'] ?? []);
      String startDesc = fullInfo['start_desc'] ?? '';

      // 解析起运年龄
      RegExp ageRegex = RegExp(r'(\d+)年');
      Match? match = ageRegex.firstMatch(startDesc);
      int baseStartAge = match != null ? int.parse(match.group(1)!) : 8;

      for (int i = 0; i < bigGanZhi.length && i < 8; i++) {
        int periodStartAge = baseStartAge + i * 10;
        int periodEndAge = periodStartAge + 9;

        // 计算起始年份
        int startYear = bigStartTimes.length > i && bigStartTimes[i].isNotEmpty
            ? bigStartTimes[i][0]
            : DateTime.now().year + periodStartAge;
        int endYear = startYear + 9;

        periods.add({
          'name': '第${i + 1}步大运',
          'startAge': periodStartAge,
          'endAge': periodEndAge,
          'startYear': startYear,
          'endYear': endYear,
          'ganZhi': bigGanZhi[i], // 正确的大运干支
        });
      }
    } catch (e) {
      print('提取大运信息失败: $e');
      // 如果提取失败，返回空列表
    }

    return periods;
  }
}
