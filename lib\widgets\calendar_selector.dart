// lib/widgets/calendar_selector.dart
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../utils/calendar_converter.dart';

class CalendarSelector extends StatefulWidget {
  final DateTime initialDate;
  final bool isLunar;
  final Function(DateTime) onDateChanged;
  final Function(bool) onCalendarTypeChanged;
  
  CalendarSelector({
    required this.initialDate,
    required this.isLunar,
    required this.onDateChanged,
    required this.onCalendarTypeChanged,
  });
  
  @override
  _CalendarSelectorState createState() => _CalendarSelectorState();
}

class _CalendarSelectorState extends State<CalendarSelector> {
  late DateTime _selectedDate;
  late bool _isLunar;
  late Map<String, dynamic> _lunarDate;
  
  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate;
    _isLunar = widget.isLunar;
    _lunarDate = CalendarConverter.solarToLunar(_selectedDate);
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '公历',
              style: TextStyle(
                color: !_isLunar ? Theme.of(context).primaryColor : Colors.grey,
                fontWeight: !_isLunar ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            Switch(
              value: _isLunar,
              onChanged: (value) {
                setState(() {
                  _isLunar = value;
                });
                widget.onCalendarTypeChanged(value);
              },
              activeColor: Theme.of(context).primaryColor,
            ),
            Text(
              '农历',
              style: TextStyle(
                color: _isLunar ? Theme.of(context).primaryColor : Colors.grey,
                fontWeight: _isLunar ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
        SizedBox(height: 16),
        _isLunar ? _buildLunarPicker() : _buildSolarPicker(),
      ],
    );
  }
  
  Widget _buildSolarPicker() {
    return Column(
      children: [
        Container(
          height: 150,
          child: CupertinoDatePicker(
            mode: CupertinoDatePickerMode.date,
            initialDateTime: _selectedDate,
            minimumYear: 1900,
            maximumYear: DateTime.now().year,
            onDateTimeChanged: (DateTime newDate) {
              setState(() {
                _selectedDate = newDate;
                _lunarDate = CalendarConverter.solarToLunar(newDate);
              });
              widget.onDateChanged(newDate);
            },
          ),
        ),
        SizedBox(height: 8),
        Text(
          '对应农历: ${_lunarDate['year']}年${_lunarDate['month']}月${_lunarDate['day']}日${_lunarDate['isLeapMonth'] ? '(闰)' : ''}',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
        SizedBox(height: 4),
        Text(
          '干支纪年: ${CalendarConverter.getStemBranchYear(_lunarDate['year'])}年',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }
  
  Widget _buildLunarPicker() {
    // 农历年份范围
    final years = List.generate(150, (index) => 1900 + index);
    // 农历月份
    final months = List.generate(12, (index) => index + 1);
    // 农历日期
    final days = List.generate(30, (index) => index + 1);
    
    return Column(
      children: [
        Container(
          height: 150,
          child: Row(
            children: [
              // 年份选择器
              Expanded(
                child: CupertinoPicker(
                  itemExtent: 32,
                  scrollController: FixedExtentScrollController(
                    initialItem: _lunarDate['year'] - 1900,
                  ),
                  onSelectedItemChanged: (int index) {
                    setState(() {
                      _lunarDate['year'] = 1900 + index;
                      _updateSolarDate();
                    });
                  },
                  children: years.map((year) {
                    return Center(
                      child: Text(
                        '$year年',
                        style: TextStyle(fontSize: 16),
                      ),
                    );
                  }).toList(),
                ),
              ),
              
              // 月份选择器
              Expanded(
                child: CupertinoPicker(
                  itemExtent: 32,
                  scrollController: FixedExtentScrollController(
                    initialItem: _lunarDate['month'] - 1,
                  ),
                  onSelectedItemChanged: (int index) {
                    setState(() {
                      _lunarDate['month'] = index + 1;
                      _updateSolarDate();
                    });
                  },
                  children: months.map((month) {
                    return Center(
                      child: Text(
                        '$month月',
                        style: TextStyle(fontSize: 16),
                      ),
                    );
                  }).toList(),
                ),
              ),
              
              // 日期选择器
              Expanded(
                child: CupertinoPicker(
                  itemExtent: 32,
                  scrollController: FixedExtentScrollController(
                    initialItem: _lunarDate['day'] - 1,
                  ),
                  onSelectedItemChanged: (int index) {
                    setState(() {
                      _lunarDate['day'] = index + 1;
                      _updateSolarDate();
                    });
                  },
                  children: days.map((day) {
                    return Center(
                      child: Text(
                        '$day日',
                        style: TextStyle(fontSize: 16),
                      ),
                    );
                  }).toList(),
                ),
              ),
              
              // 闰月选择器
              Container(
                width: 60,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('闰月'),
                    Switch(
                      value: _lunarDate['isLeapMonth'],
                      onChanged: (value) {
                        setState(() {
                          _lunarDate['isLeapMonth'] = value;
                          _updateSolarDate();
                        });
                      },
                      activeColor: Theme.of(context).primaryColor,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 8),
        Text(
          '对应公历: ${_selectedDate.year}年${_selectedDate.month}月${_selectedDate.day}日',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
        SizedBox(height: 4),
        Text(
          '干支纪年: ${CalendarConverter.getStemBranchYear(_lunarDate['year'])}年',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }
  
  void _updateSolarDate() {
    final newSolarDate = CalendarConverter.lunarToSolar(
      _lunarDate['year'],
      _lunarDate['month'],
      _lunarDate['day'],
      _lunarDate['isLeapMonth'],
    );
    
    setState(() {
      _selectedDate = newSolarDate;
    });
    
    widget.onDateChanged(newSolarDate);
  }
}

