// lib/widgets/date_picker.dart
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../utils/calendar_converter.dart';

class DatePicker extends StatefulWidget {
  final bool isLunar;
  final DateTime initialDate;
  final ValueChanged<DateTime> onDateChanged;
  
  DatePicker({
    required this.isLunar,
    required this.initialDate,
    required this.onDateChanged,
  });
  
  @override
  _DatePickerState createState() => _DatePickerState();
}

class _DatePickerState extends State<DatePicker> {
  late DateTime _selectedDate;
  late Map<String, dynamic> _lunarDate;
  bool _isLeapMonth = false;
  
  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate;
    _lunarDate = CalendarConverter.solarToLunar(_selectedDate);
  }
  
  @override
  void didUpdateWidget(DatePicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isLunar != oldWidget.isLunar) {
      // 历法切换时更新日期
      if (widget.isLunar) {
        _lunarDate = CalendarConverter.solarToLunar(_selectedDate);
      } else {
        _selectedDate = CalendarConverter.lunarToSolar(
          _lunarDate['year'],
          _lunarDate['month'],
          _lunarDate['day'],
          _isLeapMonth,
        );
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return widget.isLunar
        ? _buildLunarDatePicker()
        : _buildSolarDatePicker();
  }
  
  Widget _buildSolarDatePicker() {
    return Column(
      children: [
        Container(
          height: 150,
          child: CupertinoDatePicker(
            mode: CupertinoDatePickerMode.date,
            initialDateTime: _selectedDate,
            minimumYear: 1900,
            maximumYear: DateTime.now().year,
            onDateTimeChanged: (DateTime newDate) {
              setState(() {
                _selectedDate = newDate;
                _lunarDate = CalendarConverter.solarToLunar(newDate);
              });
              widget.onDateChanged(newDate);
            },
          ),
        ),
        SizedBox(height: 8),
        Text(
          '对应农历: ${_lunarDate['year']}年${_lunarDate['month']}月${_lunarDate['day']}日',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }
  
  Widget _buildLunarDatePicker() {
    // 构建年月日选择器
    // 这里使用三列滚轮选择器
    return Column(
      children: [
        Container(
          height: 150,
          child: Row(
            children: [
              // 年份选择器
              Expanded(
                child: _buildYearPicker(),
              ),
              // 月份选择器
              Expanded(
                child: _buildMonthPicker(),
              ),
              // 日期选择器
              Expanded(
                child: _buildDayPicker(),
              ),
            ],
          ),
        ),
        if (_canBeLeapMonth())
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Checkbox(
                value: _isLeapMonth,
                onChanged: (value) {
                  setState(() {
                    _isLeapMonth = value ?? false;
                    _updateSolarDate();
                  });
                },
              ),
              Text('闰月'),
            ],
          ),
        SizedBox(height: 8),
        Text(
          '对应公历: ${_selectedDate.year}年${_selectedDate.month}月${_selectedDate.day}日',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }
  
  Widget _buildYearPicker() {
    return CupertinoPicker(
      itemExtent: 32,
      scrollController: FixedExtentScrollController(
        initialItem: _lunarDate['year'] - 1900,
      ),
      onSelectedItemChanged: (int index) {
        setState(() {
          _lunarDate['year'] = index + 1900;
          _updateSolarDate();
        });
      },
      children: List<Widget>.generate(
        DateTime.now().year - 1899,
        (int index) {
          final year = index + 1900;
          final chineseYear = CalendarConverter.getChineseYear(year);
          return Center(
            child: Text('$year ($chineseYear)'),
          );
        },
      ),
    );
  }
  
  Widget _buildMonthPicker() {
    return CupertinoPicker(
      itemExtent: 32,
      scrollController: FixedExtentScrollController(
        initialItem: _lunarDate['month'] - 1,
      ),
      onSelectedItemChanged: (int index) {
        setState(() {
          _lunarDate['month'] = index + 1;
          _updateSolarDate();
        });
      },
      children: List<Widget>.generate(
        12,
        (int index) {
          return Center(
            child: Text('${index + 1}月'),
          );
        },
      ),
    );
  }
  
  Widget _buildDayPicker() {
    // 获取当前选择的农历月份的天数
    final daysInMonth = _getDaysInLunarMonth(
      _lunarDate['year'],
      _lunarDate['month'],
      _isLeapMonth,
    );
    
    return CupertinoPicker(
      itemExtent: 32,
      scrollController: FixedExtentScrollController(
        initialItem: _lunarDate['day'] - 1,
      ),
      onSelectedItemChanged: (int index) {
        setState(() {
          _lunarDate['day'] = index + 1;
          _updateSolarDate();
        });
      },
      children: List<Widget>.generate(
        daysInMonth,
        (int index) {
          return Center(
            child: Text('${index + 1}日'),
          );
        },
      ),
    );
  }
  
  void _updateSolarDate() {
    final newSolarDate = CalendarConverter.lunarToSolar(
      _lunarDate['year'],
      _lunarDate['month'],
      _lunarDate['day'],
      _isLeapMonth,
    );
    
    if (_selectedDate != newSolarDate) {
      _selectedDate = newSolarDate;
      widget.onDateChanged(_selectedDate);
    }
  }
  
  bool _canBeLeapMonth() {
    // 检查当前选择的年月是否可能有闰月
    // 简化版，实际需要查询农历数据
    return false;
  }
  
  int _getDaysInLunarMonth(int year, int month, bool isLeapMonth) {
    // 获取农历月份的天数
    // 简化版，实际需要查询农历数据
    return 30; // 假设所有月份都是30天
  }
}