// lib/widgets/palace_detail.dart
import 'package:flutter/material.dart';
import '../models/palace.dart';

class PalaceDetail extends StatelessWidget {
  final Palace palace;
  
  PalaceDetail({required this.palace});
  
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.all(8),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${palace.name}宫 (${palace.earthBranch})',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.help_outline),
                  onPressed: () => _showPalaceInfo(context),
                  tooltip: '宫位说明',
                ),
              ],
            ),
            Divider(),
            Expanded(
              child: SingleChildScrollView(
                child: <PERSON>umn(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildStarSection('主星', palace.mainStars),
                    SizedBox(height: 8),
                    _buildStarSection('辅星', palace.minorStars),
                    SizedBox(height: 8),
                    _buildStarSection('化星', palace.transformStars),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildStarSection(String title, List<String> stars) {
    if (stars.isEmpty) {
      return SizedBox.shrink();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.purple,
          ),
        ),
        SizedBox(height: 4),
        Wrap(
          spacing: 8,
          children: stars.map((star) {
            return Chip(
              label: Text(star),
              backgroundColor: _getStarColor(star),
              labelStyle: TextStyle(
                color: Colors.white,
                fontSize: 12,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
  
  Color _getStarColor(String star) {
    // 根据星曜类型返回不同颜色
    if (['紫微', '天机', '太阳', '武曲', '天同', '廉贞', '天府', '太阴', '贪狼', '巨门', '天相', '天梁', '七杀', '破军'].contains(star)) {
      return Colors.indigo; // 主星
    } else if (['文昌', '文曲', '左辅', '右弼', '天魁', '天钺'].contains(star)) {
      return Colors.teal; // 吉星
    } else if (['擎羊', '陀罗', '火星', '铃星', '地空', '地劫'].contains(star)) {
      return Colors.deepOrange; // 煞星
    } else if (['化科', '化禄', '化权', '化忌'].contains(star)) {
      return Colors.purple; // 化星
    } else {
      return Colors.blueGrey; // 其他辅星
    }
  }
  
  void _showPalaceInfo(BuildContext context) {
    final palaceInfo = _getPalaceInfo();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${palace.name}宫说明'),
        content: SingleChildScrollView(
          child: Text(palaceInfo),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('关闭'),
          ),
        ],
      ),
    );
  }
  
  String _getPalaceInfo() {
    // 返回宫位的详细说明
    switch (palace.name) {
      case '命':
        return '命宫代表一个人的先天体质、性格特质和人生格局。命宫的强弱决定了一个人的精神状态和生命力。';
      case '兄弟':
        return '兄弟宫主管兄弟姐妹关系，也代表朋友、同事等平辈关系，以及个人的社交能力和人际关系。';
      case '夫妻':
        return '夫妻宫主管婚姻、配偶，反映婚姻状况、配偶特质以及与配偶的关系。';
      case '子女':
        return '子女宫主管子女、后代，也代表个人的创造力和事业成果。';
      case '财帛':
        return '财帛宫主管财富、收入、理财能力，反映个人的财运和经济状况。';
      case '疾厄':
        return '疾厄宫主管健康、疾病，反映个人的健康状况和潜在疾病。';
      case '迁移':
        return '迁移宫主管旅行、搬迁、出国等行动，也代表人生旅途中的变动。';
      case '奴仆':
        return '奴仆宫在古代主管仆人，现代则代表下属、助手，也反映个人的管理能力和与下属的关系。';
      case '官禄':
        return '官禄宫主管事业、职位、成就，反映个人的事业发展和社会地位。';
      case '田宅':
        return '田宅宫主管房产、土地等不动产，反映个人的居住环境和不动产拥有情况。';
      case '福德':
        return '福德宫主管福气、德行、内心世界，反映个人的精神生活和幸福感。';
      case '父母':
        return '父母宫主管父母、长辈，反映与父母的关系以及受到的教育和影响。';
      default:
        return '此宫位信息暂无详细说明。';
    }
  }
}





