# 真假太阳时转换和天干地支计算功能说明

## 功能概述

本项目已成功实现了真假太阳时转换和精确到时辰的天干地支计算功能，包括：

1. **真假太阳时转换**：支持平太阳时与真太阳时的相互转换
2. **精确的阳历阴历转换**：基于天文算法的准确历法转换
3. **完整的天干地支计算**：支持年月日时四柱干支计算
4. **时辰精确计算**：支持真太阳时的时辰划分
5. **地理位置考虑**：根据经纬度计算地方时差

## 核心功能模块

### 1. 太阳时转换工具 (`SolarTimeConverter`)

**主要功能：**
- 计算真太阳时与平太阳时的时差
- 平太阳时转真太阳时
- 真太阳时转平太阳时
- 计算日出日落时间

**使用示例：**
```dart
import 'lib/utils/solar_time_converter.dart';

DateTime meanTime = DateTime(2024, 6, 21, 12, 0, 0);
double longitude = 116.4074; // 北京经度

// 转换为真太阳时
DateTime apparentTime = SolarTimeConverter.meanToApparentSolarTime(meanTime, longitude);

// 转换回平太阳时
DateTime backToMean = SolarTimeConverter.apparentToMeanSolarTime(apparentTime, longitude);
```

### 2. 天干地支计算器 (`StemBranchCalculator`)

**主要功能：**
- 年干支计算
- 月干支计算（基于农历月份）
- 日干支计算（基于儒略日数）
- 时干支计算（基于时辰索引）
- 完整四柱干支计算

**使用示例：**
```dart
import 'lib/utils/stem_branch_calculator.dart';

DateTime date = DateTime(2024, 1, 1, 14, 30, 0);
int lunarMonth = 12; // 农历十二月
int hourIndex = 7; // 未时

// 获取完整四柱
Map<String, String> fourPillars = StemBranchCalculator.getFourPillars(date, lunarMonth, hourIndex);
print('年柱：${fourPillars['year']}');
print('月柱：${fourPillars['month']}');
print('日柱：${fourPillars['day']}');
print('时柱：${fourPillars['hour']}');
```

### 3. 阳历阴历转换器 (`LunarSolarConverter`)

**主要功能：**
- 公历转农历（1900-2100年）
- 农历转公历
- 闰月处理
- 农历月日中文名称

**使用示例：**
```dart
import 'lib/utils/lunar_solar_converter.dart';

DateTime solarDate = DateTime(2024, 2, 10);

// 转换为农历
Map<String, dynamic> lunarInfo = LunarSolarConverter.solarToLunar(solarDate);
print('农历：${lunarInfo['year']}年${lunarInfo['month']}月${lunarInfo['day']}日');

// 转换回公历
DateTime backToSolar = LunarSolarConverter.lunarToSolar(
  lunarInfo['year'], 
  lunarInfo['month'], 
  lunarInfo['day'], 
  lunarInfo['isLeapMonth']
);
```

### 4. 时辰计算器 (`HourCalculator`)

**主要功能：**
- 平太阳时时辰计算
- 真太阳时时辰计算
- 时辰详细信息（五行、方位、养生等）
- 时辰时间范围

**使用示例：**
```dart
import 'lib/utils/hour_calculator.dart';

DateTime time = DateTime(2024, 1, 1, 14, 30, 0);
double longitude = 116.4074;

// 计算时辰索引
int hourIndex = HourCalculator.getHourIndexByApparentTime(time, longitude);

// 获取时辰详细信息
Map<String, dynamic> details = HourCalculator.getHourDetails(hourIndex);
print('时辰：${details['name']}');
print('五行：${details['element']}');
print('方位：${details['direction']}');
```

### 5. 统一接口 (`CalendarConverter`)

**主要功能：**
- 整合所有功能的统一接口
- 简化的API调用
- 向后兼容

**使用示例：**
```dart
import 'lib/utils/calendar_converter.dart';

DateTime birthTime = DateTime(2024, 1, 1, 14, 30, 0);
double longitude = 116.4074;

// 获取完整四柱（考虑真太阳时）
Map<String, String> fourPillars = CalendarConverter.getFourPillars(
  birthTime, 
  longitude: longitude
);

// 真假太阳时转换
DateTime apparentTime = CalendarConverter.meanToApparentSolarTime(birthTime, longitude);

// 阳历阴历转换
Map<String, dynamic> lunarInfo = CalendarConverter.solarToLunar(birthTime);
```

## 技术特点

### 1. 精确的天文算法
- 基于儒略日数的日干支计算
- 考虑地球轨道偏心率的时差方程
- 精确的农历数据表（1900-2100年）

### 2. 地理位置支持
- 支持任意经纬度的地方时计算
- 真太阳时的地理修正
- 日出日落时间计算

### 3. 完整的时间体系
- 年月日时四柱完整计算
- 真假太阳时的时辰划分
- 农历闰月的正确处理

### 4. 丰富的附加信息
- 时辰的五行属性
- 方位和季节信息
- 中医养生建议
- 天干地支属性

## 应用场景

1. **紫微斗数排盘**：精确的时辰计算确保排盘准确性
2. **八字命理**：完整的四柱干支计算
3. **择日择时**：基于真太阳时的时辰选择
4. **传统历法**：阳历阴历的准确转换
5. **天文计算**：日出日落等天文现象

## 使用注意事项

1. **时间精度**：建议使用真太阳时进行时辰计算，特别是在边界时间
2. **地理位置**：提供准确的经纬度信息以获得最佳计算结果
3. **年份范围**：农历转换支持1900-2100年，超出范围会抛出异常
4. **闰月处理**：注意农历闰月的正确标识和处理

## 性能优化

- 所有计算都是纯数学运算，性能优异
- 农历数据采用预计算表，查询速度快
- 支持批量计算和缓存优化

## 扩展性

代码采用模块化设计，易于扩展：
- 可添加更多地理位置数据
- 可扩展更多天文计算功能
- 可集成更多传统历法系统

## 测试验证

所有功能都经过严格测试：
- 真假太阳时转换的往返精度验证
- 阳历阴历转换的准确性验证
- 天干地支计算的传统算法验证
- 边界条件和异常情况处理

这套功能为紫微斗数等传统命理应用提供了坚实的技术基础，确保了计算的准确性和可靠性。
