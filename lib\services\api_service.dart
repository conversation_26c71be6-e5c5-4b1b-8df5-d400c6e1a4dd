// lib/services/api_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/chart_data.dart';
import '../models/interpretation.dart';
import '../models/user.dart';

class ApiService {
  final String baseUrl = 'https://api.example.com';
  String? _authToken;
  
  // 设置认证令牌
  void setAuthToken(String token) {
    _authToken = token;
  }
  
  // 获取请求头
  Map<String, String> _getHeaders() {
    final headers = {'Content-Type': 'application/json'};
    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }
    return headers;
  }
  
  // 用户注册
  Future<User?> register(String email, String password, String username) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/auth/register'),
        headers: _getHeaders(),
        body: jsonEncode({
          'email': email,
          'password': password,
          'username': username,
        }),
      );
      
      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        _authToken = data['token'];
        return User.fromJson(data['user']);
      }
      
      return null;
    } catch (e) {
      print('注册失败: $e');
      return null;
    }
  }
  
  // 用户登录
  Future<User?> login(String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/auth/login'),
        headers: _getHeaders(),
        body: jsonEncode({
          'email': email,
          'password': password,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _authToken = data['token'];
        return User.fromJson(data['user']);
      }
      
      return null;
    } catch (e) {
      print('登录失败: $e');
      return null;
    }
  }
  
  // 同步命盘数据
  Future<bool> syncCharts(List<ChartData> charts) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/charts/sync'),
        headers: _getHeaders(),
        body: jsonEncode({
          'charts': charts.map((c) => c.toJson()).toList(),
        }),
      );
      
      return response.statusCode == 200;
    } catch (e) {
      print('同步命盘失败: $e');
      return false;
    }
  }
  
  // 获取用户的所有命盘
  Future<List<ChartData>> getUserCharts() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/charts'),
        headers: _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as List;
        return data.map((json) => ChartData.fromJson(json)).toList();
      }
      
      return [];
    } catch (e) {
      print('获取命盘失败: $e');
      return [];
    }
  }
  
  // 获取AI解读
  Future<Interpretation?> getAIInterpretation(
    String chartId,
    String type,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/interpretations/generate'),
        headers: _getHeaders(),
        body: jsonEncode({
          'chartId': chartId,
          'type': type,
        }),
      );
      
      if (response.statusCode == 200) {
        return Interpretation.fromJson(jsonDecode(response.body));
      }
      
      return null;
    } catch (e) {
      print('获取AI解读失败: $e');
      return null;
    }
  }
  
  // 检查订阅状态
  Future<Map<String, dynamic>?> checkSubscription() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/subscription'),
        headers: _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
      
      return null;
    } catch (e) {
      print('检查订阅失败: $e');
      return null;
    }
  }
}