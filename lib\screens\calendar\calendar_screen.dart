import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../../utils/perpetual_calendar.dart';
import '../../utils/solar_terms_calculator.dart';
import '../../services/paipan_service.dart';
import '../../utils/calendar_converter.dart';

/// 万年历界面
class CalendarScreen extends StatefulWidget {
  @override
  _CalendarScreenState createState() => _CalendarScreenState();
}

class _CalendarScreenState extends State<CalendarScreen> with TickerProviderStateMixin {
  DateTime _selectedDate = DateTime.now();
  bool _isLunar = false;
  late TabController _tabController;
  
  // 当前显示的年月
  int _currentYear = DateTime.now().year;
  int _currentMonth = DateTime.now().month;
  
  // 万年历数据
  Map<String, dynamic>? _dayInfo;
  Map<String, dynamic>? _monthInfo;
  List<Map<String, dynamic>>? _solarTerms;

  // 节气年份选择
  int _solarTermsYear = DateTime.now().year;
  bool _isLoadingSolarTerms = false;

  // 排盘服务
  final PaipanService _paipanService = PaipanService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadCalendarData();
    // 初始化节气数据
    _loadSolarTermsForYear(_solarTermsYear);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 加载万年历数据
  void _loadCalendarData() {
    setState(() {
      _dayInfo = PerpetualCalendar.getDayInfo(_selectedDate);
      _monthInfo = PerpetualCalendar.getMonthInfo(_currentYear, _currentMonth);
      _solarTerms = _calculateSolarTermsFromPaipan(_currentYear);
    });
  }

  /// 加载指定年份的节气数据
  void _loadSolarTermsForYear(int year) {
    setState(() {
      _isLoadingSolarTerms = true;
      _solarTermsYear = year;
    });

    // 异步加载节气数据
    Future.microtask(() {
      try {
        List<Map<String, dynamic>> terms = _calculateSolarTermsFromPaipan(year);
        if (mounted) {
          setState(() {
            _solarTerms = terms;
            _isLoadingSolarTerms = false;
          });
        }
      } catch (e) {
        print('加载节气数据失败: $e');
        if (mounted) {
          setState(() {
            _isLoadingSolarTerms = false;
          });
        }
      }
    });
  }

  /// 使用排盘服务计算节气
  List<Map<String, dynamic>> _calculateSolarTermsFromPaipan(int year) {
    List<Map<String, dynamic>> solarTerms = [];

    // 获取24节气数据
    List<List<int>> jieQiList = _paipanService.get24JieQi(year);

    // 节气名称
    const List<String> jieQiNames = [
      '立春', '雨水', '惊蛰', '春分', '清明', '谷雨',
      '立夏', '小满', '芒种', '夏至', '小暑', '大暑',
      '立秋', '处暑', '白露', '秋分', '寒露', '霜降',
      '立冬', '小雪', '大雪', '冬至', '小寒', '大寒'
    ];

    // 节气描述
    const List<String> descriptions = [
      '春季开始，万物复苏', '雨量增多，气温回升', '春雷响动，蛰虫苏醒', '昼夜等长，春分时节',
      '清明时节，万物清洁明净', '雨水充沛，谷物生长', '夏季开始，万物繁茂', '麦类作物籽粒饱满',
      '有芒作物成熟收割', '白昼最长，夏至时节', '天气炎热，小暑时节', '一年最热，大暑时节',
      '秋季开始，暑去凉来', '暑气消散，秋高气爽', '露水增多，天气转凉', '昼夜等长，秋分时节',
      '露水更凉，深秋时节', '霜降时节，天气更冷', '冬季开始，万物收藏', '开始降雪，天气寒冷',
      '雪量增大，天寒地冻', '白昼最短，冬至时节', '天气寒冷，小寒时节', '一年最冷，大寒时节'
    ];

    for (int i = 0; i < jieQiList.length && i < jieQiNames.length; i++) {
      List<int> jieQiTime = jieQiList[i];
      if (jieQiTime.length >= 6) {
        DateTime time = DateTime(
          jieQiTime[0], // 年
          jieQiTime[1], // 月
          jieQiTime[2], // 日
          jieQiTime[3], // 时
          jieQiTime[4], // 分
          jieQiTime[5], // 秒
        );

        solarTerms.add({
          'name': jieQiNames[i],
          'time': time,
          'description': descriptions[i],
          'season': _getSeason(i),
          'index': i,
        });
      }
    }

    return solarTerms;
  }

  /// 获取节气所属季节
  String _getSeason(int termIndex) {
    if (termIndex >= 0 && termIndex <= 5) return '春季';
    if (termIndex >= 6 && termIndex <= 11) return '夏季';
    if (termIndex >= 12 && termIndex <= 17) return '秋季';
    return '冬季';
  }

  /// 构建节气年份选择器
  Widget _buildSolarTermsYearSelector() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green[50],
        border: Border(bottom: BorderSide(color: Colors.green[200]!)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: Icon(Icons.chevron_left),
            onPressed: () {
              if (_solarTermsYear > 1900) {
                _loadSolarTermsForYear(_solarTermsYear - 1);
              }
            },
          ),
          GestureDetector(
            onTap: _showSolarTermsYearPicker,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[300]!),
              ),
              child: Text(
                '$_solarTermsYear年节气',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.green[700],
                ),
              ),
            ),
          ),
          IconButton(
            icon: Icon(Icons.chevron_right),
            onPressed: () {
              if (_solarTermsYear < 2100) {
                _loadSolarTermsForYear(_solarTermsYear + 1);
              }
            },
          ),
        ],
      ),
    );
  }

  /// 显示节气年份选择器
  void _showSolarTermsYearPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('选择年份'),
        content: Container(
          height: 300,
          width: 200,
          child: CupertinoPicker(
            itemExtent: 32,
            scrollController: FixedExtentScrollController(
              initialItem: _solarTermsYear - 1900,
            ),
            onSelectedItemChanged: (index) {
              int selectedYear = 1900 + index;
              _loadSolarTermsForYear(selectedYear);
            },
            children: List.generate(201, (index) {
              int year = 1900 + index;
              return Center(
                child: Text(
                  '$year年',
                  style: TextStyle(fontSize: 16),
                ),
              );
            }),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('确定'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('万年历'),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(icon: Icon(Icons.calendar_today), text: '日历'),
            Tab(icon: Icon(Icons.info_outline), text: '详情'),
            Tab(icon: Icon(Icons.wb_sunny), text: '节气'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildCalendarView(),
          _buildDetailView(),
          _buildSolarTermsView(),
        ],
      ),
    );
  }

  /// 构建日历视图
  Widget _buildCalendarView() {
    return Column(
      children: [
        // 年月选择器
        _buildYearMonthSelector(),
        
        // 历法切换
        _buildCalendarTypeSwitch(),
        
        // 日历网格
        Expanded(
          child: _buildCalendarGrid(),
        ),
      ],
    );
  }

  /// 构建年月选择器
  Widget _buildYearMonthSelector() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green[50],
        border: Border(bottom: BorderSide(color: Colors.green[200]!)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            icon: Icon(Icons.chevron_left),
            onPressed: _previousMonth,
          ),
          GestureDetector(
            onTap: _showYearMonthPicker,
            child: Text(
              '$_currentYear年$_currentMonth月',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.green[700],
              ),
            ),
          ),
          IconButton(
            icon: Icon(Icons.chevron_right),
            onPressed: _nextMonth,
          ),
        ],
      ),
    );
  }

  /// 构建历法切换开关
  Widget _buildCalendarTypeSwitch() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('公历', style: TextStyle(fontSize: 14)),
          SizedBox(width: 8),
          Switch(
            value: _isLunar,
            onChanged: (value) {
              setState(() {
                _isLunar = value;
              });
            },
            activeColor: Colors.green,
          ),
          SizedBox(width: 8),
          Text('农历', style: TextStyle(fontSize: 14)),
        ],
      ),
    );
  }

  /// 构建日历网格
  Widget _buildCalendarGrid() {
    if (_monthInfo == null) {
      return Center(child: CircularProgressIndicator());
    }

    List<Map<String, dynamic>> days = _monthInfo!['days'];
    DateTime firstDay = _monthInfo!['firstDay'];
    int weekdayOfFirst = firstDay.weekday % 7; // 0=周日, 1=周一...

    return Container(
      padding: EdgeInsets.all(8),
      child: Column(
        children: [
          // 星期标题
          _buildWeekHeader(),
          
          // 日期网格
          Expanded(
            child: GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 7,
                childAspectRatio: 1.0,
              ),
              itemCount: weekdayOfFirst + days.length,
              itemBuilder: (context, index) {
                if (index < weekdayOfFirst) {
                  return Container(); // 空白格子
                }
                
                int dayIndex = index - weekdayOfFirst;
                Map<String, dynamic> dayData = days[dayIndex];
                DateTime date = dayData['solar']['date'];
                
                return _buildDayCell(dayData, date);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建星期标题
  Widget _buildWeekHeader() {
    List<String> weekdays = ['日', '一', '二', '三', '四', '五', '六'];
    
    return Container(
      height: 40,
      child: Row(
        children: weekdays.map((day) => Expanded(
          child: Center(
            child: Text(
              day,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
          ),
        )).toList(),
      ),
    );
  }

  /// 构建日期单元格
  Widget _buildDayCell(Map<String, dynamic> dayData, DateTime date) {
    bool isSelected = date.year == _selectedDate.year &&
                     date.month == _selectedDate.month &&
                     date.day == _selectedDate.day;
    bool isToday = date.year == DateTime.now().year &&
                   date.month == DateTime.now().month &&
                   date.day == DateTime.now().day;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedDate = date;
          _dayInfo = dayData;
        });
      },
      child: Container(
        margin: EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: isSelected ? Colors.green[600] : 
                 isToday ? Colors.green[100] : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: isToday && !isSelected ? 
                 Border.all(color: Colors.green[300]!, width: 2) : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '${date.day}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isSelected ? Colors.white : Colors.black,
              ),
            ),
            if (!_isLunar) ...[
              Text(
                '${dayData['lunar']['dayName']}',
                style: TextStyle(
                  fontSize: 10,
                  color: isSelected ? Colors.white70 : Colors.grey[600],
                ),
              ),
            ] else ...[
              Text(
                '${date.day}',
                style: TextStyle(
                  fontSize: 10,
                  color: isSelected ? Colors.white70 : Colors.grey[600],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 上一月
  void _previousMonth() {
    setState(() {
      if (_currentMonth == 1) {
        _currentMonth = 12;
        _currentYear--;
      } else {
        _currentMonth--;
      }
      _loadCalendarData();
    });
  }

  /// 下一月
  void _nextMonth() {
    setState(() {
      if (_currentMonth == 12) {
        _currentMonth = 1;
        _currentYear++;
      } else {
        _currentMonth++;
      }
      _loadCalendarData();
    });
  }

  /// 显示年月选择器
  void _showYearMonthPicker() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('选择年月'),
        content: Container(
          height: 200,
          width: 300,
          child: Row(
            children: [
              // 年份选择
              Expanded(
                child: CupertinoPicker(
                  itemExtent: 32,
                  scrollController: FixedExtentScrollController(
                    initialItem: _currentYear - 1900,
                  ),
                  onSelectedItemChanged: (index) {
                    _currentYear = 1900 + index;
                  },
                  children: List.generate(200, (index) {
                    int year = 1900 + index;
                    return Center(child: Text('$year年'));
                  }),
                ),
              ),
              // 月份选择
              Expanded(
                child: CupertinoPicker(
                  itemExtent: 32,
                  scrollController: FixedExtentScrollController(
                    initialItem: _currentMonth - 1,
                  ),
                  onSelectedItemChanged: (index) {
                    _currentMonth = index + 1;
                  },
                  children: List.generate(12, (index) {
                    int month = index + 1;
                    return Center(child: Text('$month月'));
                  }),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _loadCalendarData();
            },
            child: Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 构建详情视图
  Widget _buildDetailView() {
    if (_dayInfo == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.calendar_today, size: 64, color: Colors.grey[400]),
            SizedBox(height: 16),
            Text(
              '请选择日期查看详情',
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDateInfoCard(),
          SizedBox(height: 16),
          _buildLunarInfoCard(),
          SizedBox(height: 16),
          _buildStemBranchCard(),
          SizedBox(height: 16),
          _buildSolarTermCard(),
        ],
      ),
    );
  }

  /// 构建日期信息卡片
  Widget _buildDateInfoCard() {
    Map<String, dynamic> solar = _dayInfo!['solar'];

    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.calendar_today, color: Colors.green[600]),
                SizedBox(width: 8),
                Text(
                  '公历信息',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            _buildInfoRow('日期', '${solar['year']}年${solar['month']}月${solar['day']}日'),
            _buildInfoRow('星期', solar['weekdayName']),
            _buildInfoRow('年份', '${solar['year']}年'),
            _buildInfoRow('季度', '第${((solar['month'] - 1) ~/ 3) + 1}季度'),
            _buildInfoRow('月份', '${solar['month']}月'),
            _buildInfoRow('日期', '第${solar['day']}日'),
          ],
        ),
      ),
    );
  }

  /// 构建农历信息卡片
  Widget _buildLunarInfoCard() {
    Map<String, dynamic> lunar = _dayInfo!['lunar'];

    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.brightness_2, color: Colors.orange[600]),
                SizedBox(width: 8),
                Text(
                  '农历信息',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange[700],
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            _buildInfoRow('农历', '${lunar['year']}年${lunar['monthName']}${lunar['dayName']}'),
            _buildInfoRow('生肖', lunar['zodiac']),
            _buildInfoRow('闰月', lunar['isLeapMonth'] ? '是' : '否'),
            if (lunar['isLeapMonth'])
              _buildInfoRow('闰月说明', '本月为闰${lunar['month']}月'),
          ],
        ),
      ),
    );
  }

  /// 构建干支信息卡片
  Widget _buildStemBranchCard() {
    Map<String, dynamic> stemBranch = _dayInfo!['stemBranch'];

    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.auto_awesome, color: Colors.purple[600]),
                SizedBox(width: 8),
                Text(
                  '干支信息',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple[700],
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            _buildInfoRow('年干支', stemBranch['year']),
            _buildInfoRow('月干支', stemBranch['month']),
            _buildInfoRow('日干支', stemBranch['day']),
          ],
        ),
      ),
    );
  }

  /// 构建节气信息卡片
  Widget _buildSolarTermCard() {
    if (_solarTerms == null) return Container();

    // 找到最近的节气和下一个节气
    DateTime selectedDate = _selectedDate;
    Map<String, dynamic>? nearestTerm;
    Map<String, dynamic>? nextTerm;

    // 获取当前年份和前后年份的节气，以处理跨年情况
    List<Map<String, dynamic>> allTerms = [];

    // 添加前一年的节气
    if (selectedDate.month <= 2) {
      allTerms.addAll(_calculateSolarTermsFromPaipan(selectedDate.year - 1));
    }

    // 添加当前年份的节气
    allTerms.addAll(_solarTerms!);

    // 添加下一年的节气
    if (selectedDate.month >= 11) {
      allTerms.addAll(_calculateSolarTermsFromPaipan(selectedDate.year + 1));
    }

    // 找到最近的过去节气和下一个未来节气
    for (int i = 0; i < allTerms.length; i++) {
      DateTime termTime = allTerms[i]['time'];
      if (termTime.isAfter(selectedDate)) {
        nextTerm = allTerms[i];
        if (i > 0) {
          nearestTerm = allTerms[i - 1];
        }
        break;
      }
    }

    // 如果没有找到下一个节气，说明选择的日期在最后一个节气之后
    if (nextTerm == null && allTerms.isNotEmpty) {
      nearestTerm = allTerms.last;
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.wb_sunny, color: Colors.amber[600]),
                SizedBox(width: 8),
                Text(
                  '节气信息',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.amber[700],
                  ),
                ),
              ],
            ),
            SizedBox(height: 12),
            if (nearestTerm != null) ...[
              _buildInfoRow('最近节气', nearestTerm['name']),
              _buildInfoRow('节气时间', _formatDateTime(nearestTerm['time'])),
              _buildInfoRow('节气描述', nearestTerm['description']),
            ],
            if (nextTerm != null) ...[
              SizedBox(height: 8),
              _buildInfoRow('下个节气', nextTerm['name']),
              _buildInfoRow('节气时间', _formatDateTime(nextTerm['time'])),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label：',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: Colors.black87),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建节气视图
  Widget _buildSolarTermsView() {
    return Column(
      children: [
        // 年份选择器
        _buildSolarTermsYearSelector(),

        // 节气列表
        Expanded(
          child: _isLoadingSolarTerms
              ? Center(child: CircularProgressIndicator())
              : _solarTerms == null
                  ? Center(child: Text('暂无数据'))
                  : ListView.builder(
                      padding: EdgeInsets.all(16),
                      itemCount: _solarTerms!.length,
                      itemBuilder: (context, index) {
                        Map<String, dynamic> term = _solarTerms![index];
                        DateTime termTime = term['time'];
                        bool isPassed = termTime.isBefore(DateTime.now());
                        bool isCurrentYear = termTime.year == DateTime.now().year;

                        // 获取农历日期
                        Map<String, dynamic> lunarInfo = PerpetualCalendar.getDayInfo(termTime)['lunar'];
                        String lunarDate = '农历${lunarInfo['year']}年${lunarInfo['monthName']}${lunarInfo['dayName']}';

                        return Card(
                          elevation: isPassed && isCurrentYear ? 1 : 2,
                          color: isPassed && isCurrentYear ? Colors.grey[100] : Colors.white,
                          child: ListTile(
                            leading: Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: isPassed && isCurrentYear ? Colors.grey[400] : Colors.amber[600],
                                shape: BoxShape.circle,
                              ),
                              child: Center(
                                child: Text(
                                  '${index + 1}',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                            title: Text(
                              term['name'],
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: isPassed && isCurrentYear ? Colors.grey[600] : Colors.black,
                              ),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  _formatDateTime(termTime),
                                  style: TextStyle(
                                    color: isPassed && isCurrentYear ? Colors.grey[500] : Colors.grey[700],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                SizedBox(height: 2),
                                Text(
                                  lunarDate,
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: isPassed && isCurrentYear ? Colors.grey[500] : Colors.blue[600],
                                  ),
                                ),
                                SizedBox(height: 2),
                                Text(
                                  term['description'],
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: isPassed && isCurrentYear ? Colors.grey[500] : Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                            trailing: isPassed && isCurrentYear
                              ? Icon(Icons.check_circle, color: Colors.grey[400])
                              : Icon(Icons.schedule, color: Colors.amber[600]),
                          ),
                        );
                      },
                    ),
        ),
      ],
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}:'
           '${dateTime.second.toString().padLeft(2, '0')}';
  }
}
