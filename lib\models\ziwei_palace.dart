// lib/models/ziwei_palace.dart
// 紫微斗数宫位模型

import 'ziwei_star.dart';

/// 十二宫位枚举
enum PalaceType {
  life,      // 命宫
  siblings,  // 兄弟宫
  spouse,    // 夫妻宫
  children,  // 子女宫
  wealth,    // 财帛宫
  health,    // 疾厄宫
  travel,    // 迁移宫
  friends,   // 奴仆宫（交友宫）
  career,    // 官禄宫（事业宫）
  property,  // 田宅宫
  fortune,   // 福德宫
  parents,   // 父母宫
}

/// 宫位模型
class ZiweiPalace {
  /// 宫位类型
  final PalaceType type;
  
  /// 宫位索引（0-11）
  final int index;
  
  /// 宫位名称
  final String name;
  
  /// 宫位干支
  final String stemBranch;
  
  /// 宫位天干
  String get heavenlyStem => stemBranch.isNotEmpty ? stemBranch[0] : '';
  
  /// 宫位地支
  String get earthlyBranch => stemBranch.length > 1 ? stemBranch[1] : '';
  
  /// 宫位中的星曜
  final List<ZiweiStar> stars;
  
  /// 宫位五行
  final StarElement element;
  
  /// 宫位描述
  final String description;
  
  /// 宫位主管事项
  final List<String> domains;
  
  /// 是否为身宫
  bool isBodyPalace;
  
  /// 是否为命宫
  bool get isLifePalace => type == PalaceType.life;
  
  ZiweiPalace({
    required this.type,
    required this.index,
    required this.name,
    required this.stemBranch,
    required this.element,
    required this.description,
    required this.domains,
    this.isBodyPalace = false,
    List<ZiweiStar>? stars,
  }) : stars = stars ?? [];
  
  /// 添加星曜
  void addStar(ZiweiStar star) {
    stars.add(star.copyWithPalace(index));
  }
  
  /// 添加多个星曜
  void addStars(List<ZiweiStar> newStars) {
    for (var star in newStars) {
      addStar(star);
    }
  }
  
  /// 移除星曜
  void removeStar(String starName) {
    stars.removeWhere((star) => star.name == starName);
  }
  
  /// 检查是否包含指定星曜
  bool hasStar(String starName) {
    return stars.any((star) => star.name == starName);
  }
  
  /// 获取指定类型的星曜
  List<ZiweiStar> getStarsByType(StarType type) {
    return stars.where((star) => star.type == type).toList();
  }
  
  /// 获取主星
  List<ZiweiStar> get mainStars => getStarsByType(StarType.main);
  
  /// 获取辅星
  List<ZiweiStar> get auxiliaryStars => getStarsByType(StarType.auxiliary);
  
  /// 获取杂曜
  List<ZiweiStar> get miscellaneousStars => getStarsByType(StarType.miscellaneous);
  
  /// 获取化曜
  List<ZiweiStar> get transformStars => getStarsByType(StarType.transform);
  
  /// 获取宫位强度（根据星曜数量和亮度）
  double get strength {
    double totalStrength = 0.0;
    for (var star in stars) {
      switch (star.brightness) {
        case StarBrightness.temple:
          totalStrength += 5.0;
          break;
        case StarBrightness.prosperous:
          totalStrength += 4.0;
          break;
        case StarBrightness.favorable:
          totalStrength += 3.0;
          break;
        case StarBrightness.neutral:
          totalStrength += 2.0;
          break;
        case StarBrightness.trapped:
          totalStrength += 1.0;
          break;
        case StarBrightness.fallen:
          totalStrength += 0.5;
          break;
        case null:
          totalStrength += 2.0; // 默认值
          break;
      }
    }
    return totalStrength;
  }
  
  /// 获取宫位吉凶性质
  String get nature {
    int auspiciousCount = 0;
    int inauspiciousCount = 0;
    
    for (var star in stars) {
      // 根据星曜特性判断吉凶（简化版）
      if (star.characteristics.any((c) => c.contains('吉') || c.contains('贵') || c.contains('财'))) {
        auspiciousCount++;
      } else if (star.characteristics.any((c) => c.contains('凶') || c.contains('煞') || c.contains('破'))) {
        inauspiciousCount++;
      }
    }
    
    if (auspiciousCount > inauspiciousCount) {
      return '吉';
    } else if (inauspiciousCount > auspiciousCount) {
      return '凶';
    } else {
      return '平';
    }
  }
  
  /// 获取宫位的详细信息
  Map<String, dynamic> toMap() {
    return {
      'type': type.toString(),
      'index': index,
      'name': name,
      'stemBranch': stemBranch,
      'heavenlyStem': heavenlyStem,
      'earthlyBranch': earthlyBranch,
      'element': element.toString(),
      'description': description,
      'domains': domains,
      'isBodyPalace': isBodyPalace,
      'isLifePalace': isLifePalace,
      'stars': stars.map((star) => star.toMap()).toList(),
      'mainStars': mainStars.map((star) => star.name).toList(),
      'auxiliaryStars': auxiliaryStars.map((star) => star.name).toList(),
      'transformStars': transformStars.map((star) => star.name).toList(),
      'strength': strength,
      'nature': nature,
    };
  }
  
  @override
  String toString() {
    return '$name($stemBranch)';
  }
}

/// 宫位工厂类
class PalaceFactory {
  /// 创建标准的十二宫位
  static List<ZiweiPalace> createStandardPalaces() {
    return [
      // 命宫 (0)
      ZiweiPalace(
        type: PalaceType.life,
        index: 0,
        name: '命宫',
        stemBranch: '',
        element: StarElement.earth,
        description: '主管一生命运、性格、外貌、才能',
        domains: ['命运', '性格', '外貌', '才能', '先天运势'],
      ),
      
      // 兄弟宫 (1)
      ZiweiPalace(
        type: PalaceType.siblings,
        index: 1,
        name: '兄弟宫',
        stemBranch: '',
        element: StarElement.earth,
        description: '主管兄弟姐妹、朋友、同事关系',
        domains: ['兄弟姐妹', '朋友', '同事', '合作伙伴'],
      ),
      
      // 夫妻宫 (2)
      ZiweiPalace(
        type: PalaceType.spouse,
        index: 2,
        name: '夫妻宫',
        stemBranch: '',
        element: StarElement.earth,
        description: '主管婚姻、配偶、感情生活',
        domains: ['婚姻', '配偶', '恋爱', '感情', '异性缘'],
      ),
      
      // 子女宫 (3)
      ZiweiPalace(
        type: PalaceType.children,
        index: 3,
        name: '子女宫',
        stemBranch: '',
        element: StarElement.earth,
        description: '主管子女、晚辈、学生、部属',
        domains: ['子女', '晚辈', '学生', '部属', '创作'],
      ),
      
      // 财帛宫 (4)
      ZiweiPalace(
        type: PalaceType.wealth,
        index: 4,
        name: '财帛宫',
        stemBranch: '',
        element: StarElement.earth,
        description: '主管财运、理财能力、金钱观念',
        domains: ['财运', '理财', '投资', '收入', '金钱观'],
      ),
      
      // 疾厄宫 (5)
      ZiweiPalace(
        type: PalaceType.health,
        index: 5,
        name: '疾厄宫',
        stemBranch: '',
        element: StarElement.earth,
        description: '主管健康、疾病、意外、体质',
        domains: ['健康', '疾病', '意外', '体质', '医疗'],
      ),
      
      // 迁移宫 (6)
      ZiweiPalace(
        type: PalaceType.travel,
        index: 6,
        name: '迁移宫',
        stemBranch: '',
        element: StarElement.earth,
        description: '主管外出、旅行、搬迁、外地发展',
        domains: ['外出', '旅行', '搬迁', '外地发展', '人际关系'],
      ),
      
      // 奴仆宫 (7)
      ZiweiPalace(
        type: PalaceType.friends,
        index: 7,
        name: '奴仆宫',
        stemBranch: '',
        element: StarElement.earth,
        description: '主管下属、朋友、客户、社交关系',
        domains: ['下属', '朋友', '客户', '社交', '人脉'],
      ),
      
      // 官禄宫 (8)
      ZiweiPalace(
        type: PalaceType.career,
        index: 8,
        name: '官禄宫',
        stemBranch: '',
        element: StarElement.earth,
        description: '主管事业、工作、官运、名声',
        domains: ['事业', '工作', '官运', '名声', '成就'],
      ),
      
      // 田宅宫 (9)
      ZiweiPalace(
        type: PalaceType.property,
        index: 9,
        name: '田宅宫',
        stemBranch: '',
        element: StarElement.earth,
        description: '主管房产、家庭、居住环境',
        domains: ['房产', '家庭', '居住环境', '不动产', '家族'],
      ),
      
      // 福德宫 (10)
      ZiweiPalace(
        type: PalaceType.fortune,
        index: 10,
        name: '福德宫',
        stemBranch: '',
        element: StarElement.earth,
        description: '主管精神享受、兴趣爱好、福分',
        domains: ['精神享受', '兴趣爱好', '福分', '修养', '品味'],
      ),
      
      // 父母宫 (11)
      ZiweiPalace(
        type: PalaceType.parents,
        index: 11,
        name: '父母宫',
        stemBranch: '',
        element: StarElement.earth,
        description: '主管父母、长辈、上司、师长',
        domains: ['父母', '长辈', '上司', '师长', '贵人'],
      ),
    ];
  }
  
  /// 根据命宫位置调整宫位顺序
  static List<ZiweiPalace> adjustPalaceOrder(List<ZiweiPalace> palaces, int lifePalaceIndex) {
    List<ZiweiPalace> adjustedPalaces = [];
    
    for (int i = 0; i < 12; i++) {
      int actualIndex = (lifePalaceIndex + i) % 12;
      ZiweiPalace palace = palaces[i];
      
      // 更新宫位的实际索引
      adjustedPalaces.add(ZiweiPalace(
        type: palace.type,
        index: actualIndex,
        name: palace.name,
        stemBranch: palace.stemBranch,
        element: palace.element,
        description: palace.description,
        domains: palace.domains,
        isBodyPalace: palace.isBodyPalace,
        stars: palace.stars,
      ));
    }
    
    return adjustedPalaces;
  }
}
