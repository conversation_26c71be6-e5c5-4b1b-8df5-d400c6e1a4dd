// lib/models/advanced_settings.dart

/// 年柱起始点选择枚举
enum YearPillarStartPoint {
  lichun, // 以立春为新年
  lunarNewYear, // 以正月初一为新年
}

/// 子时处理方式枚举
enum ZiHourMode {
  early, // 早子时：23:00-01:00算作第二天
  late, // 晚子时：23:00-24:00算作当天，00:00-01:00算作第二天
}

/// 闰月分界处理方式枚举
enum LeapMonthBoundary {
  middle, // 闰月月中分界（默认）
  previous, // 上月闰月：整个闰月都算作上个月
  next, // 下月闰月：整个闰月都算作下个月
}

/// 高级设置模型
class AdvancedSettings {
  /// 年柱起始点选择
  final YearPillarStartPoint yearPillarStartPoint;
  
  /// 子时处理方式
  final ZiHourMode ziHourMode;
  
  /// 闰月分界处理方式
  final LeapMonthBoundary leapMonthBoundary;
  
  /// 是否使用真太阳时
  final bool useApparentSolarTime;
  
  /// 是否显示节气信息
  final bool showSolarTerms;
  
  const AdvancedSettings({
    this.yearPillarStartPoint = YearPillarStartPoint.lichun,
    this.ziHourMode = ZiHourMode.early,
    this.leapMonthBoundary = LeapMonthBoundary.middle,
    this.useApparentSolarTime = true,
    this.showSolarTerms = true,
  });
  
  /// 创建默认设置
  factory AdvancedSettings.defaultSettings() {
    return const AdvancedSettings();
  }
  
  /// 从JSON创建设置
  factory AdvancedSettings.fromJson(Map<String, dynamic> json) {
    return AdvancedSettings(
      yearPillarStartPoint: YearPillarStartPoint.values.firstWhere(
        (e) => e.toString() == json['yearPillarStartPoint'],
        orElse: () => YearPillarStartPoint.lichun,
      ),
      ziHourMode: ZiHourMode.values.firstWhere(
        (e) => e.toString() == json['ziHourMode'],
        orElse: () => ZiHourMode.early,
      ),
      leapMonthBoundary: LeapMonthBoundary.values.firstWhere(
        (e) => e.toString() == json['leapMonthBoundary'],
        orElse: () => LeapMonthBoundary.middle,
      ),
      useApparentSolarTime: json['useApparentSolarTime'] ?? true,
      showSolarTerms: json['showSolarTerms'] ?? true,
    );
  }
  
  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'yearPillarStartPoint': yearPillarStartPoint.toString(),
      'ziHourMode': ziHourMode.toString(),
      'leapMonthBoundary': leapMonthBoundary.toString(),
      'useApparentSolarTime': useApparentSolarTime,
      'showSolarTerms': showSolarTerms,
    };
  }
  
  /// 复制并修改设置
  AdvancedSettings copyWith({
    YearPillarStartPoint? yearPillarStartPoint,
    ZiHourMode? ziHourMode,
    LeapMonthBoundary? leapMonthBoundary,
    bool? useApparentSolarTime,
    bool? showSolarTerms,
  }) {
    return AdvancedSettings(
      yearPillarStartPoint: yearPillarStartPoint ?? this.yearPillarStartPoint,
      ziHourMode: ziHourMode ?? this.ziHourMode,
      leapMonthBoundary: leapMonthBoundary ?? this.leapMonthBoundary,
      useApparentSolarTime: useApparentSolarTime ?? this.useApparentSolarTime,
      showSolarTerms: showSolarTerms ?? this.showSolarTerms,
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is AdvancedSettings &&
        other.yearPillarStartPoint == yearPillarStartPoint &&
        other.ziHourMode == ziHourMode &&
        other.leapMonthBoundary == leapMonthBoundary &&
        other.useApparentSolarTime == useApparentSolarTime &&
        other.showSolarTerms == showSolarTerms;
  }
  
  @override
  int get hashCode {
    return yearPillarStartPoint.hashCode ^
        ziHourMode.hashCode ^
        leapMonthBoundary.hashCode ^
        useApparentSolarTime.hashCode ^
        showSolarTerms.hashCode;
  }
  
  @override
  String toString() {
    return 'AdvancedSettings('
        'yearPillarStartPoint: $yearPillarStartPoint, '
        'ziHourMode: $ziHourMode, '
        'leapMonthBoundary: $leapMonthBoundary, '
        'useApparentSolarTime: $useApparentSolarTime, '
        'showSolarTerms: $showSolarTerms'
        ')';
  }
}

/// 高级设置扩展方法
extension AdvancedSettingsExtension on AdvancedSettings {
  
  /// 获取年柱起始点的中文名称
  String get yearPillarStartPointName {
    switch (yearPillarStartPoint) {
      case YearPillarStartPoint.lichun:
        return '以立春为新年';
      case YearPillarStartPoint.lunarNewYear:
        return '以正月初一为新年';
    }
  }
  
  /// 获取年柱起始点的描述
  String get yearPillarStartPointDescription {
    switch (yearPillarStartPoint) {
      case YearPillarStartPoint.lichun:
        return '符合传统命理学标准，年柱干支在立春时刻发生变化';
      case YearPillarStartPoint.lunarNewYear:
        return '年柱干支在农历新年零时发生变化';
    }
  }
  
  /// 获取子时处理方式的中文名称
  String get ziHourModeName {
    switch (ziHourMode) {
      case ZiHourMode.early:
        return '早子时';
      case ZiHourMode.late:
        return '晚子时';
    }
  }
  
  /// 获取子时处理方式的描述
  String get ziHourModeDescription {
    switch (ziHourMode) {
      case ZiHourMode.early:
        return '23:00-01:00算作第二天，即23:00后日柱干支按次日计算';
      case ZiHourMode.late:
        return '23:00-24:00算作当天，00:00-01:00算作第二天';
    }
  }
  
  /// 获取闰月分界处理方式的中文名称
  String get leapMonthBoundaryName {
    switch (leapMonthBoundary) {
      case LeapMonthBoundary.middle:
        return '闰月月中分界';
      case LeapMonthBoundary.previous:
        return '上月闰月';
      case LeapMonthBoundary.next:
        return '下月闰月';
    }
  }
  
  /// 获取闰月分界处理方式的描述
  String get leapMonthBoundaryDescription {
    switch (leapMonthBoundary) {
      case LeapMonthBoundary.middle:
        return '闰月的前半月算上个月，后半月算下个月';
      case LeapMonthBoundary.previous:
        return '整个闰月都算作上个月';
      case LeapMonthBoundary.next:
        return '整个闰月都算作下个月';
    }
  }
  
  /// 获取所有设置的摘要
  String get summary {
    return '年柱起始：${yearPillarStartPointName}，'
           '子时处理：${ziHourModeName}，'
           '闰月分界：${leapMonthBoundaryName}，'
           '真太阳时：${useApparentSolarTime ? "启用" : "禁用"}';
  }
  
  /// 检查设置是否为默认值
  bool get isDefault {
    return this == AdvancedSettings.defaultSettings();
  }
  
  /// 获取与默认设置的差异
  List<String> getDifferencesFromDefault() {
    final defaultSettings = AdvancedSettings.defaultSettings();
    List<String> differences = [];
    
    if (yearPillarStartPoint != defaultSettings.yearPillarStartPoint) {
      differences.add('年柱起始点：${yearPillarStartPointName}');
    }
    
    if (ziHourMode != defaultSettings.ziHourMode) {
      differences.add('子时处理：${ziHourModeName}');
    }
    
    if (leapMonthBoundary != defaultSettings.leapMonthBoundary) {
      differences.add('闰月分界：${leapMonthBoundaryName}');
    }
    
    if (useApparentSolarTime != defaultSettings.useApparentSolarTime) {
      differences.add('真太阳时：${useApparentSolarTime ? "启用" : "禁用"}');
    }
    
    if (showSolarTerms != defaultSettings.showSolarTerms) {
      differences.add('节气信息：${showSolarTerms ? "显示" : "隐藏"}');
    }
    
    return differences;
  }
}
