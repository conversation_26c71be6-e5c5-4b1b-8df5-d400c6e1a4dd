// lib/services/paipan_service.dart
import 'dart:math' as math;

/// 排盘服务类 - 基于paipan.php的完整实现
/// 提供完整的万年历、八字排盘、节气计算等功能
///
/// 版本更新：
/// - 修正日柱计算精度，提高浮点运算精度
/// - 统一时柱计算算法，确保与其他计算模块一致
/// - 优化子时处理逻辑，支持早晚子时区分
class PaipanService {
  /// 是否区分早晚子时，true则23:00-24:00算成上一天
  bool zwz = false;

  /// 十天干
  final List<String> ctg = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];

  /// 五行
  final List<String> cwx = ['木', '火', '土', '金', '水'];

  /// 十二地支
  final List<String> cdz = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];

  /// 地支对应五行
  final List<int> dzwx = [4, 2, 0, 0, 2, 1, 1, 2, 3, 3, 2, 4];

  /// 地支转天干
  final List<int> dztg = [8, 5, 0, 2, 4, 3, 2, 5, 6, 7, 4, 9];

  /// 地支藏干
  final List<List<int>> dzcg = [
    [9], [5, 9, 7], [0, 2, 4], [1], [4, 1, 9], [2, 4, 6],
    [3, 5], [5, 3, 1], [6, 8, 4], [7], [4, 7, 3], [8, 0]
  ];

  /// 十二生肖
  final List<String> csa = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];

  /// 十二星座
  final List<String> cxz = ['水瓶座', '双鱼座', '白羊座', '金牛座', '双子座', '巨蟹座', '狮子座', '处女座', '天秤座', '天蝎座', '射手座', '摩羯座'];

  /// 星期
  final List<String> wkd = ['日', '一', '二', '三', '四', '五', '六'];

  /// 廿四节气(从春分开始)
  final List<String> jq = [
    '春分', '清明', '谷雨', '立夏', '小满', '芒种', '夏至', '小暑', '大暑', '立秋', '处暑', '白露',
    '秋分', '寒露', '霜降', '立冬', '小雪', '大雪', '冬至', '小寒', '大寒', '立春', '雨水', '惊蛰'
  ];

  /// 均值朔望月長
  final double synmonth = 29.530588853;

  /// 十大神杀
  final List<List<String>> tenGod = [
    ['比', '劫'], ['食', '伤'], ['财', '才'], ['杀', '官'], ['枭', '印']
  ];

  /// 十二长生
  final List<String> cs = ["生", "沐", "冠", "临", "旺", "衰", "病", "死", "墓", "绝", "胎", "养"];

  /// 本气
  final List<String> selfQi = ['禄', '刃', '长生', '墓库', '余气', '死', '绝'];

  /// 长生位置，天干对应的地支索引
  final List<int> csTg2dz = [11, 6, 2, 9, 2, 9, 5, 0, 8, 3];

  /// 因子数组
  final List<double> ptsa = [485, 203, 199, 182, 156, 136, 77, 74, 70, 58, 52, 50, 45, 44, 29, 18, 17, 16, 14, 12, 12, 12, 9, 8];
  final List<double> ptsb = [324.96, 337.23, 342.08, 27.85, 73.14, 171.52, 222.54, 296.72, 243.58, 119.81, 297.17, 21.02, 247.54, 325.15, 60.93, 155.12, 288.79, 198.04, 199.76, 95.39, 287.11, 320.81, 227.73, 15.45];
  final List<double> ptsc = [1934.136, 32964.467, 20.186, 445267.112, 45036.886, 22518.443, 65928.934, 3034.906, 9037.513, 33718.147, 150.678, 2281.226, 29929.562, 31555.956, 4443.417, 67555.328, 4562.452, 62894.029, 31436.921, 14577.848, 31931.756, 34777.259, 1222.114, 16859.074];

  /// 计算指定年(公历)的春分点
  double? _ve(int yy) {
    if (yy < -8000 || yy > 8001) {
      return null;
    }
    
    if (yy >= 1000 && yy <= 8001) {
      double m = (yy - 2000) / 1000;
      return 2451623.80984 + 365242.37404 * m + 0.05169 * m * m - 0.00411 * m * m * m - 0.00057 * m * m * m * m;
    }
    
    if (yy >= -8000 && yy < 1000) {
      double m = yy / 1000;
      return 1721139.29189 + 365242.1374 * m + 0.06134 * m * m + 0.00111 * m * m * m - 0.00071 * m * m * m * m;
    }
    
    return null;
  }

  /// 地球摄动偏移量
  double _perturbation(double jd) {
    double t = (jd - 2451545) / 36525;
    double s = 0;
    
    for (int k = 0; k < 24; k++) {
      s += ptsa[k] * math.cos(ptsb[k] * 2 * math.pi / 360 + ptsc[k] * 2 * math.pi / 360 * t);
    }
    
    double w = 35999.373 * t - 2.47;
    double l = 1 + 0.0334 * math.cos(w * 2 * math.pi / 360) + 0.0007 * math.cos(2 * w * 2 * math.pi / 360);
    
    return 0.00001 * s / l;
  }

  /// 求∆t
  double _deltaT(int yy, int mm) {
    double y = yy + (mm - 0.5) / 12;
    double dt;

    if (y <= -500) {
      double u = (y - 1820) / 100;
      dt = -20 + 32 * u * u;
    } else if (y < 500) {
      double u = y / 100;
      dt = 10583.6 - 1014.41 * u + 33.78311 * u * u - 5.952053 * u * u * u - 
           0.1798452 * u * u * u * u + 0.022174192 * u * u * u * u * u + 
           0.0090316521 * u * u * u * u * u * u;
    } else if (y < 1600) {
      double u = (y - 1000) / 100;
      dt = 1574.2 - 556.01 * u + 71.23472 * u * u + 0.319781 * u * u * u - 
           0.8503463 * u * u * u * u - 0.005050998 * u * u * u * u * u + 
           0.0083572073 * u * u * u * u * u * u;
    } else if (y < 1700) {
      double t = y - 1600;
      dt = 120 - 0.9808 * t - 0.01532 * t * t + t * t * t / 7129;
    } else if (y < 1800) {
      double t = y - 1700;
      dt = 8.83 + 0.1603 * t - 0.0059285 * t * t + 0.00013336 * t * t * t - t * t * t * t / 1174000;
    } else if (y < 1860) {
      double t = y - 1800;
      dt = 13.72 - 0.332447 * t + 0.0068612 * t * t + 0.0041116 * t * t * t - 
           0.00037436 * t * t * t * t + 0.0000121272 * t * t * t * t * t - 
           0.0000001699 * t * t * t * t * t * t + 0.000000000875 * t * t * t * t * t * t * t;
    } else if (y < 1900) {
      double t = y - 1860;
      dt = 7.62 + 0.5737 * t - 0.251754 * t * t + 0.01680668 * t * t * t - 
           0.0004473624 * t * t * t * t + t * t * t * t * t / 233174;
    } else if (y < 1920) {
      double t = y - 1900;
      dt = -2.79 + 1.494119 * t - 0.0598939 * t * t + 0.0061966 * t * t * t - 0.000197 * t * t * t * t;
    } else if (y < 1941) {
      double t = y - 1920;
      dt = 21.2 + 0.84493 * t - 0.0761 * t * t + 0.0020936 * t * t * t;
    } else if (y < 1961) {
      double t = y - 1950;
      dt = 29.07 + 0.407 * t - t * t / 233 + t * t * t / 2547;
    } else if (y < 1986) {
      double t = y - 1975;
      dt = 45.45 + 1.067 * t - t * t / 260 - t * t * t / 718;
    } else if (y < 2005) {
      double t = y - 2000;
      dt = 63.86 + 0.3345 * t - 0.060374 * t * t + 0.0017275 * t * t * t + 
           0.000651814 * t * t * t * t + 0.00002373599 * t * t * t * t * t;
    } else if (y < 2050) {
      double t = y - 2000;
      dt = 62.92 + 0.32217 * t + 0.005589 * t * t;
    } else if (y < 2150) {
      double u = (y - 1820) / 100;
      dt = -20 + 32 * u * u - 0.5628 * (2150 - y);
    } else {
      double u = (y - 1820) / 100;
      dt = -20 + 32 * u * u;
    }

    if (y < 1955 || y >= 2005) {
      dt = dt - (0.000012932 * (y - 1955) * (y - 1955));
    }
    
    return dt / 60; // 将秒转换为分
  }

  /// 判断公历日期是否有效
  bool validDate(int yy, int mm, int dd) {
    if (yy < -1000 || yy > 3000) {
      return false;
    }

    if (mm < 1 || mm > 12) {
      return false;
    }

    if (yy == 1582 && mm == 10 && dd >= 5 && dd < 15) {
      return false; // 这段日期不存在
    }

    int ndf1 = -(yy % 4 == 0 ? 1 : 0);
    int ndf2 = (yy > 1582) ? ((yy % 400 == 0 ? 1 : 0) - (yy % 100 == 0 ? 1 : 0)) : 0;
    int ndf = ndf1 + ndf2;
    int dom = 30 + (((mm - 7.5).abs() + 0.5) % 2).floor() - (mm == 2 ? 1 : 0) * (2 + ndf);

    if (dd <= 0 || dd > dom) {
      return false;
    }

    return true;
  }

  /// 将公历时间转换为儒略日历时间
  double? solar2Julian(int yy, int mm, int dd, [int hh = 0, int mt = 0, int ss = 0]) {
    if (!validDate(yy, mm, dd)) {
      return null;
    }
    if (hh < 0 || hh >= 24) return null;
    if (mt < 0 || mt >= 60) return null;
    if (ss < 0 || ss >= 60) return null;

    int yp = yy + ((mm - 3) / 10).floor();
    double init;
    double jdy;

    if ((yy > 1582) || (yy == 1582 && mm > 10) || (yy == 1582 && mm == 10 && dd >= 15)) {
      init = 1721119.5;
      jdy = (yp * 365.25).floor().toDouble() - (yp / 100).floor().toDouble() + (yp / 400).floor().toDouble();
    } else if ((yy < 1582) || (yy == 1582 && mm < 10) || (yy == 1582 && mm == 10 && dd <= 4)) {
      init = 1721117.5;
      jdy = (yp * 365.25).floor().toDouble();
    } else {
      return null;
    }

    int mp = (mm + 9) % 12;
    double jdm = mp * 30.0 + ((mp + 1) * 34 / 57).floor().toDouble();
    double jdd = (dd - 1).toDouble();
    double jdh = (hh + (mt + (ss / 60)) / 60) / 24;

    return jdy + jdm + jdd + jdh + init;
  }

  /// 将儒略日历时间转换为公历时间
  List<int> julian2Solar(double jd) {
    double y4h, init;

    if (jd >= 2299160.5) {
      y4h = 146097;
      init = 1721119.5;
    } else {
      y4h = 146100;
      init = 1721117.5;
    }

    double jdr = (jd - init).floor().toDouble();
    double yh = y4h / 4;
    int cen = ((jdr + 0.75) / yh).floor();
    double d = (jdr + 0.75 - cen * yh).floor().toDouble();
    double ywl = 1461 / 4;
    int jy = ((d + 0.75) / ywl).floor();
    d = (d + 0.75 - ywl * jy + 1).floor().toDouble();
    double ml = 153 / 5;
    int mp = ((d - 0.5) / ml).floor();
    d = ((d - 0.5) - 30.6 * mp + 1).floor().toDouble();
    int y = (100 * cen) + jy;
    int m = (mp + 2) % 12 + 1;
    if (m < 3) y = y + 1;

    int sd = ((jd + 0.5 - (jd + 0.5).floor()) * 24 * 60 * 60 + 0.00005).floor();
    int mt = (sd / 60).floor();
    int ss = sd % 60;
    int hh = (mt / 60).floor();
    mt = mt % 60;

    return [y, m, d.floor(), hh, mt, ss];
  }

  /// 计算公历的某天是星期几
  int? getWeek(int yy, int mm, int dd) {
    double? jd = solar2Julian(yy, mm, dd, 12);
    if (jd == null) return null;

    return (((jd + 1).floor() % 7) + 7) % 7;
  }

  /// 根据公历月日计算星座下标
  int? getZodiac(int mm, int dd) {
    if (mm < 1 || mm > 12) return null;
    if (dd < 1 || dd > 31) return null;

    List<int> dds = [20, 19, 21, 20, 21, 22, 23, 23, 23, 24, 22, 22];
    int kn = mm - 1;

    if (dd < dds[kn]) {
      kn = ((kn + 12) - 1) % 12;
    }

    return kn;
  }

  /// 获取公历某个月有多少天
  int getSolarDays(int yy, int mm) {
    if (yy < -1000 || yy > 3000) return 0;
    if (mm < 1 || mm > 12) return 0;

    int ndf1 = -(yy % 4 == 0 ? 1 : 0);
    int ndf2 = (yy > 1582) ? ((yy % 400 == 0 ? 1 : 0) - (yy % 100 == 0 ? 1 : 0)) : 0;
    int ndf = ndf1 + ndf2;

    return 30 + (((mm - 7.5).abs() + 0.5) % 2).floor() - (mm == 2 ? 1 : 0) * (2 + ndf);
  }

  /// 获取指定年的春分开始的24节气理论值
  List<double> _meanJQJD(int yy) {
    double? jd = _ve(yy);
    if (jd == null) return [];

    double ty = _ve(yy + 1)! - jd;
    int num = 24 + 2;

    double ath = 2 * math.pi / 24;
    double tx = (jd - 2451545) / 365250;
    double e = 0.0167086342 - 0.0004203654 * tx - 0.0000126734 * tx * tx +
               0.0000001444 * tx * tx * tx - 0.0000000002 * tx * tx * tx * tx +
               0.0000000003 * tx * tx * tx * tx * tx;
    double tt = yy / 1000;
    double vp = 111.25586939 - 17.0119934518333 * tt - 0.044091890166673 * tt * tt -
                4.37356166661345E-04 * tt * tt * tt + 8.16716666602386E-06 * tt * tt * tt * tt;
    double rvp = vp * 2 * math.pi / 360;

    List<double> peri = [];
    for (int i = 0; i < num; i++) {
      int flag = 0;
      double th = ath * i + rvp;
      if (th > math.pi && th <= 3 * math.pi) {
        th = 2 * math.pi - th;
        flag = 1;
      }
      if (th > 3 * math.pi) {
        th = 4 * math.pi - th;
        flag = 2;
      }
      double f1 = 2 * math.atan((math.sqrt((1 - e) / (1 + e)) * math.tan(th / 2)));
      double f2 = (e * math.sqrt(1 - e * e) * math.sin(th)) / (1 + e * math.cos(th));
      double f = (f1 - f2) * ty / 2 / math.pi;
      if (flag == 1) f = ty - f;
      if (flag == 2) f = 2 * ty - f;
      peri.add(f);
    }

    List<double> jqjd = [];
    for (int i = 0; i < num; i++) {
      jqjd.add(jd + peri[i] - peri[0]);
    }

    return jqjd;
  }

  /// 获取指定年的春分开始作Perturbaton調整後的24节气
  Map<int, double> _getAdjustedJQ(int yy, int start, int end) {
    if (start < 0 || start > 25 || end < 0 || end > 25) {
      return {};
    }

    Map<int, double> jq = {};
    List<double> jqjd = _meanJQJD(yy);

    for (int k = start; k <= end && k < jqjd.length; k++) {
      double ptb = _perturbation(jqjd[k]);
      double dt = _deltaT(yy, ((k + 1) / 2).floor() + 3);
      jq[k] = jqjd[k] + ptb - dt / 60 / 24;
      jq[k] = jq[k]! + 1 / 3; // 中国时间比格林威治时间先行8小时
    }

    return jq;
  }

  /// 求出以某年立春點開始的節
  List<double> _getPureJQsinceSpring(int yy) {
    List<double> jdpjq = [];

    Map<int, double> dj = _getAdjustedJQ(yy - 1, 19, 23);
    for (int k = 19; k <= 23; k++) {
      if (dj.containsKey(k) && k % 2 == 1) {
        jdpjq.add(dj[k]!);
      }
    }

    dj = _getAdjustedJQ(yy, 0, 25);
    for (int k = 0; k <= 25; k++) {
      if (dj.containsKey(k) && k % 2 == 1) {
        jdpjq.add(dj[k]!);
      }
    }

    return jdpjq;
  }

  /// 求出含某公历年立春點開始的24节气
  List<List<int>> get24JieQi(int yy) {
    List<List<int>> jq = [];

    Map<int, double> dj = _getAdjustedJQ(yy - 1, 21, 23);
    for (int k = 21; k <= 23; k++) {
      if (dj.containsKey(k)) {
        jq.add(julian2Solar(dj[k]!));
      }
    }

    dj = _getAdjustedJQ(yy, 0, 20);
    for (int k = 0; k <= 20; k++) {
      if (dj.containsKey(k)) {
        jq.add(julian2Solar(dj[k]!));
      }
    }

    return jq;
  }

  /// 四柱計算，分早子时晚子时，传公历
  Map<String, dynamic> getGanZhi(int yy, int mm, int dd, int hh, [int mt = 0, int ss = 0]) {
    double? jd = solar2Julian(yy, mm, dd, hh, mt, math.max(1, ss));
    if (jd == null) return {};

    List<int> tg = [];
    List<int> dz = [];

    List<double> jq = _getPureJQsinceSpring(yy);
    if (jd < jq[1]) {
      yy = yy - 1;
      jq = _getPureJQsinceSpring(yy);
    }

    int ygz = ((yy + 4712 + 24) % 60 + 60) % 60;
    tg.add(ygz % 10); // 年干
    dz.add(ygz % 12); // 年支

    int ix = 0;
    for (int j = 0; j < jq.length; j++) {
      if (jq[j] >= jd) {
        ix = j - 1;
        break;
      }
    }

    int tmm = ((yy + 4712) * 12 + (ix - 1) + 60) % 60;
    int mgz = (tmm + 50) % 60;
    tg.add(mgz % 10); // 月干
    dz.add(mgz % 12); // 月支

    // 高精度日柱计算
    // 计算日柱之干支，加0.5是将起始点从正午改为从0点开始
    double jda = jd + 0.5;
    // 将jd的小数部分化为秒，并加上起始点前移的一小时(3600秒)
    double thes = ((jda - jda.floor()) * 86400.0) + 3600.0;
    // 将秒数化为日数，加回到jd的整数部分，提高精度
    double dayjd = jda.floor() + (thes / 86400.0);

    // 使用高精度计算干支索引，基准点调整为更精确的值
    // 原始算法：甲子日为基准，但需要考虑历法转换的精度
    int dgz = ((dayjd + 49.0).floor() % 60 + 60) % 60;
    tg.add(dgz % 10); // 日干
    dz.add(dgz % 12); // 日支

    // 处理早晚子时：区分早晚子时，日柱前移一柱
    if (zwz && (hh >= 23)) {
      tg[2] = (tg[2] + 10 - 1) % 10;
      dz[2] = (dz[2] + 12 - 1) % 12;
    }

    // 高精度时柱计算
    // 计算时柱之干支，使用更精确的浮点运算
    double dh = dayjd * 12.0;
    int hgz = ((dh + 48.0).floor() % 60 + 60) % 60;
    tg.add(hgz % 10); // 時干
    dz.add(hgz % 12); // 時支

    return {
      'tg': tg,
      'dz': dz,
      'jd': jd,
      'jq': jq,
      'ix': ix,
    };
  }

  /// 计算十神
  Map<String, dynamic> _getTenGod(int dayTgInt, int otherTgInt) {
    int l2Index = (dayTgInt + otherTgInt) % 2;
    int dayWx = _getTgWx(dayTgInt);
    int otherWx = _getTgWx(otherTgInt);
    int l1Index = otherWx - dayWx;
    if (l1Index < 0) l1Index += 5;

    return {
      'index': [l1Index, l2Index],
      'char': tenGod[l1Index][l2Index]
    };
  }

  /// 获取天干五行索引
  int _getTgWx(int tg) {
    if (tg % 2 == 1) tg--;
    return (tg / 2).floor();
  }

  /// 计算十二长生
  Map<String, dynamic> getCs(int tg, int dz) {
    int csDzIndex = csTg2dz[tg];
    int moveNum;
    if (tg % 2 == 0) {
      moveNum = dz - csDzIndex;
    } else {
      moveNum = csDzIndex - dz;
    }
    if (moveNum < 0) moveNum += 12;

    return {
      'index': moveNum,
      'char': cs[moveNum]
    };
  }

  /// 纳音运算
  List<dynamic> naYin(int tg, int dz) {
    if (tg % 2 == 1) {
      tg = tg - 1;
      dz = dz - 1;
    }

    Map<int, Map<int, List<dynamic>>> naYinMap = {
      0: {
        0: ['海中金', 3], 2: ['大溪水', 4], 4: ['佛灯火', 1],
        6: ['沙中金', 3], 8: ['井泉水', 4], 10: ['山头火', 1]
      },
      2: {
        0: ['涧下水', 4], 2: ['炉中火', 1], 4: ['沙中土', 2],
        6: ['天河水', 4], 8: ['山下火', 1], 10: ['房上土', 2]
      },
      4: {
        0: ['霹雳火', 1], 2: ['城头土', 2], 4: ['大林木', 0],
        6: ['天上火', 1], 8: ['大驿土', 2], 10: ['平地木', 0]
      },
      6: {
        0: ['壁上土', 2], 2: ['松柏木', 0], 4: ['白腊金', 3],
        6: ['路边土', 2], 8: ['石榴木', 0], 10: ['钗钏金', 3]
      },
      8: {
        0: ['桑松木', 0], 2: ['金箔金', 3], 4: ['长流水', 4],
        6: ['杨柳木', 0], 8: ['剑锋金', 3], 10: ['大海水', 4]
      }
    };

    return naYinMap[tg]?[dz] ?? ['未知', 0];
  }

  /// 公历年排盘 - 主要排盘函数（简化版）
  Map<String, dynamic> getInfo(int gd, int yy, int mm, int dd, int hh, [int mt = 0, int ss = 0]) {
    // 调用完整版本的排盘函数
    return getFullInfo(gd, yy, mm, dd, hh, mt, ss);
  }

  /// 计算本气
  Map<String, dynamic> _getSelfQi(int tg, int dz) {
    List<List<int?>> biao = [
      [2, 3, 5, 6, 5, 6, 8, 9, 11, 0],
      [3, null, 6, null, 7, null, 9, null, 0, null],
      [11, 11, 2, 2, 2, 2, 4, 4, 8, 8],
      [7, 7, 10, 10, 10, 4, 1, 1, 4, 4],
      [4, 4, 7, 7, null, null, 10, 10, 1, 1],
      [6, 6, 9, 9, 9, 9, 0, 0, 3, 3],
      [8, 8, 11, 11, 11, 11, 2, 2, 5, 5]
    ];

    int index = -1;
    for (int i = 0; i < 7; i++) {
      if (biao[i][tg] == dz) {
        index = i;
        break;
      }
    }

    if (index == -1) {
      return {'index': -1, 'char': '--'};
    } else {
      return {'index': index, 'char': selfQi[index]};
    }
  }

  /// 求出實際新月點
  double _trueNewMoon(int k) {
    double jdt = 2451550.09765 + k * synmonth;
    double t = (jdt - 2451545) / 36525;
    double t2 = t * t;
    double t3 = t2 * t;
    double t4 = t3 * t;

    // mean time of phase
    double pt = jdt + 0.0001337 * t2 - 0.00000015 * t3 + 0.00000000073 * t4;

    // Sun's mean anomaly
    double m = 2.5534 + 29.10535669 * k - 0.0000218 * t2 - 0.00000011 * t3;

    // Moon's mean anomaly
    double mprime = 201.5643 + 385.81693528 * k + 0.0107438 * t2 + 0.00001239 * t3 - 0.000000058 * t4;

    // Moon's argument of latitude
    double f = 160.7108 + 390.67050274 * k - 0.0016341 * t2 - 0.00000227 * t3 + 0.000000011 * t4;

    // Longitude of the ascending node of the lunar orbit
    double omega = 124.7746 - 1.5637558 * k + 0.0020691 * t2 + 0.00000215 * t3;

    // 乘式因子
    double es = 1 - 0.002516 * t - 0.0000074 * t2;

    // 因perturbation造成的偏移
    double apt1 = -0.4072 * math.sin((math.pi / 180) * mprime);
    apt1 += 0.17241 * es * math.sin((math.pi / 180) * m);
    apt1 += 0.01608 * math.sin((math.pi / 180) * 2 * mprime);
    apt1 += 0.01039 * math.sin((math.pi / 180) * 2 * f);
    apt1 += 0.00739 * es * math.sin((math.pi / 180) * (mprime - m));
    apt1 -= 0.00514 * es * math.sin((math.pi / 180) * (mprime + m));
    apt1 += 0.00208 * es * es * math.sin((math.pi / 180) * (2 * m));
    apt1 -= 0.00111 * math.sin((math.pi / 180) * (mprime - 2 * f));
    apt1 -= 0.00057 * math.sin((math.pi / 180) * (mprime + 2 * f));
    apt1 += 0.00056 * es * math.sin((math.pi / 180) * (2 * mprime + m));
    apt1 -= 0.00042 * math.sin((math.pi / 180) * 3 * mprime);
    apt1 += 0.00042 * es * math.sin((math.pi / 180) * (m + 2 * f));
    apt1 += 0.00038 * es * math.sin((math.pi / 180) * (m - 2 * f));
    apt1 -= 0.00024 * es * math.sin((math.pi / 180) * (2 * mprime - m));
    apt1 -= 0.00017 * math.sin((math.pi / 180) * omega);
    apt1 -= 0.00007 * math.sin((math.pi / 180) * (mprime + 2 * m));
    apt1 += 0.00004 * math.sin((math.pi / 180) * (2 * mprime - 2 * f));
    apt1 += 0.00004 * math.sin((math.pi / 180) * (3 * m));
    apt1 += 0.00003 * math.sin((math.pi / 180) * (mprime + m - 2 * f));
    apt1 += 0.00003 * math.sin((math.pi / 180) * (2 * mprime + 2 * f));
    apt1 -= 0.00003 * math.sin((math.pi / 180) * (mprime + m + 2 * f));
    apt1 += 0.00003 * math.sin((math.pi / 180) * (mprime - m + 2 * f));
    apt1 -= 0.00002 * math.sin((math.pi / 180) * (mprime - m - 2 * f));
    apt1 -= 0.00002 * math.sin((math.pi / 180) * (3 * mprime + m));
    apt1 += 0.00002 * math.sin((math.pi / 180) * (4 * mprime));

    double apt2 = 0.000325 * math.sin((math.pi / 180) * (299.77 + 0.107408 * k - 0.009173 * t2));
    apt2 += 0.000165 * math.sin((math.pi / 180) * (251.88 + 0.016321 * k));
    apt2 += 0.000164 * math.sin((math.pi / 180) * (251.83 + 26.651886 * k));
    apt2 += 0.000126 * math.sin((math.pi / 180) * (349.42 + 36.412478 * k));
    apt2 += 0.00011 * math.sin((math.pi / 180) * (84.66 + 18.206239 * k));
    apt2 += 0.000062 * math.sin((math.pi / 180) * (141.74 + 53.303771 * k));
    apt2 += 0.00006 * math.sin((math.pi / 180) * (207.14 + 2.453732 * k));
    apt2 += 0.000056 * math.sin((math.pi / 180) * (154.84 + 7.30686 * k));
    apt2 += 0.000047 * math.sin((math.pi / 180) * (34.52 + 27.261239 * k));
    apt2 += 0.000042 * math.sin((math.pi / 180) * (207.19 + 0.121824 * k));
    apt2 += 0.00004 * math.sin((math.pi / 180) * (291.34 + 1.844379 * k));
    apt2 += 0.000037 * math.sin((math.pi / 180) * (161.72 + 24.198154 * k));
    apt2 += 0.000035 * math.sin((math.pi / 180) * (239.56 + 25.513099 * k));
    apt2 += 0.000023 * math.sin((math.pi / 180) * (331.55 + 3.592518 * k));

    return pt + apt1 + apt2;
  }

  /// 對於指定日期時刻所屬的朔望月,求出其均值新月點的月序數
  List<dynamic> _meanNewMoon(double jd) {
    int kn = ((jd - 2451550.09765) / synmonth).floor();
    double jdt = 2451550.09765 + kn * synmonth;
    double t = (jdt - 2451545) / 36525;
    double thejd = jdt + 0.0001337 * t * t - 0.00000015 * t * t * t + 0.00000000073 * t * t * t * t;
    return [kn, thejd];
  }

  /// 求出自冬至點為起點的連續15個中氣
  List<double> _getZQsinceWinterSolstice(int yy) {
    List<double> jdzq = [];

    Map<int, double> dj = _getAdjustedJQ(yy - 1, 18, 23);
    if (dj.containsKey(18)) jdzq.add(dj[18]!); // 冬至
    if (dj.containsKey(20)) jdzq.add(dj[20]!); // 大寒
    if (dj.containsKey(22)) jdzq.add(dj[22]!); // 雨水

    dj = _getAdjustedJQ(yy, 0, 23);
    for (int k = 0; k <= 23; k++) {
      if (dj.containsKey(k) && k % 2 == 0) {
        jdzq.add(dj[k]!);
      }
    }

    return jdzq;
  }

  /// 求算以含冬至中氣為阴曆11月開始的連續16個朔望月
  List<double> _getSMsinceWinterSolstice(int yy, double jdws) {
    List<double> tjd = [];
    double? jd = solar2Julian(yy - 1, 11, 1, 0, 0, 0);
    if (jd == null) return [];

    List<dynamic> meanResult = _meanNewMoon(jd);
    int kn = meanResult[0];
    double thejd = meanResult[1];

    for (int i = 0; i <= 19; i++) {
      int k = kn + i;
      double mjd = thejd + synmonth * i;
      tjd.add(_trueNewMoon(k) + 1 / 3);
      tjd[i] = tjd[i] - _deltaT(yy, i - 1) / 1440;
    }

    int j = 0;
    for (j = 0; j <= 18; j++) {
      if ((tjd[j] + 0.5).floor() > (jdws + 0.5).floor()) {
        break;
      }
    }

    List<double> jdnm = [];
    for (int k = 0; k <= 15; k++) {
      jdnm.add(tjd[j - 1 + k]);
    }

    return jdnm;
  }

  /// 以比較日期法求算冬月及其餘各月名稱代碼,包含閏月
  List<dynamic> _getZQandSMandLunarMonthCode(int yy) {
    List<double> jdzq = _getZQsinceWinterSolstice(yy);
    List<double> jdnm = _getSMsinceWinterSolstice(yy, jdzq[0]);
    Map<int, double> mc = {};

    int yz = 0; // 設定旗標,0表示未遇到閏月,1表示已遇到閏月
    if ((jdzq[12] + 0.5).floor() >= (jdnm[13] + 0.5).floor()) {
      for (int i = 1; i <= 14; i++) {
        if ((jdnm[i] + 0.5).floor() > (jdzq[i - 1 - yz] + 0.5).floor() &&
            (jdnm[i + 1] + 0.5).floor() <= (jdzq[i - yz] + 0.5).floor()) {
          mc[i] = i - 0.5;
          yz = 1;
        } else {
          mc[i] = (i - yz).toDouble();
        }
      }
    } else {
      for (int i = 0; i <= 12; i++) {
        mc[i] = i.toDouble();
      }
      for (int i = 13; i <= 14; i++) {
        if ((jdnm[i] + 0.5).floor() > (jdzq[i - 1 - yz] + 0.5).floor() &&
            (jdnm[i + 1] + 0.5).floor() <= (jdzq[i - yz] + 0.5).floor()) {
          mc[i] = i - 0.5;
          yz = 1;
        } else {
          mc[i] = (i - yz).toDouble();
        }
      }
    }

    return [jdzq, jdnm, mc];
  }

  /// 获取农历某个月有多少天
  int getLunarDays(int yy, int mm, bool isLeap) {
    if (yy < -1000 || yy > 3000) return 0;
    if (mm < 1 || mm > 12) return 0;

    List<dynamic> result = _getZQandSMandLunarMonthCode(yy);
    List<double> jdnm = result[1];
    Map<int, double> mc = result[2];

    int leap = 0;
    for (int j = 1; j <= 14; j++) {
      if (mc[j]! - mc[j]!.floor() > 0) {
        leap = (mc[j]! + 0.5).floor();
        break;
      }
    }

    mm = mm + 2;

    List<int> nofd = [];
    for (int i = 0; i <= 14; i++) {
      nofd.add((jdnm[i + 1] + 0.5).floor() - (jdnm[i] + 0.5).floor());
    }

    int dy = 0;
    int er = 0;

    if (isLeap) {
      if (leap < 3) {
        er = 1;
      } else {
        if (leap != mm) {
          er = 2;
        } else {
          dy = nofd[mm];
        }
      }
    } else {
      if (leap == 0) {
        dy = nofd[mm - 1];
      } else {
        dy = nofd[mm + (mm > leap ? 1 : 0) - 1];
      }
    }

    return dy;
  }

  /// 获取农历某年的闰月,0为无闰月
  int getLeap(int yy) {
    List<dynamic> result = _getZQandSMandLunarMonthCode(yy);
    Map<int, double> mc = result[2];

    int leap = 0;
    for (int j = 1; j <= 14; j++) {
      if (mc[j]! - mc[j]!.floor() > 0) {
        leap = (mc[j]! + 0.5).floor();
        break;
      }
    }

    return math.max(0, leap - 2);
  }

  /// 将农历时间转换成公历时间
  List<int>? lunar2Solar(int yy, int mm, int dd, bool isLeap) {
    if (yy < -7000 || yy > 7000) return null;
    if (yy < -1000 || yy > 3000) return null;
    if (mm < 1 || mm > 12) return null;
    if (dd < 1 || dd > 30) return null;

    List<dynamic> result = _getZQandSMandLunarMonthCode(yy);
    List<double> jdnm = result[1];
    Map<int, double> mc = result[2];

    int leap = 0;
    for (int j = 1; j <= 14; j++) {
      if (mc[j]! - mc[j]!.floor() > 0) {
        leap = (mc[j]! + 0.5).floor();
        break;
      }
    }

    mm = mm + 2;

    List<int> nofd = [];
    for (int i = 0; i <= 14; i++) {
      nofd.add((jdnm[i + 1] + 0.5).floor() - (jdnm[i] + 0.5).floor());
    }

    double jd = 0;
    int er = 0;

    if (isLeap) {
      if (leap < 3) {
        er = 1;
      } else {
        if (leap != mm) {
          er = 2;
        } else {
          if (dd <= nofd[mm]) {
            jd = jdnm[mm] + dd - 1;
          } else {
            er = 3;
          }
        }
      }
    } else {
      if (leap == 0) {
        if (dd <= nofd[mm - 1]) {
          jd = jdnm[mm - 1] + dd - 1;
        } else {
          er = 4;
        }
      } else {
        if (dd <= nofd[mm + (mm > leap ? 1 : 0) - 1]) {
          jd = jdnm[mm + (mm > leap ? 1 : 0) - 1] + dd - 1;
        } else {
          er = 4;
        }
      }
    }

    if (er != 0) return null;
    List<int> solarResult = julian2Solar(jd);
    return solarResult.sublist(0, 3);
  }

  /// 将公历时间转换成农历时间
  List<int>? solar2Lunar(int yy, int mm, int dd) {
    if (!validDate(yy, mm, dd)) return null;

    int prev = 0;
    int isLeap = 0;

    List<dynamic> result = _getZQandSMandLunarMonthCode(yy);
    List<double> jdnm = result[1];
    Map<int, double> mc = result[2];

    double? jd = solar2Julian(yy, mm, dd, 12, 0, 0);
    if (jd == null) return null;

    if (jd.floor() < (jdnm[0] + 0.5).floor()) {
      prev = 1;
      result = _getZQandSMandLunarMonthCode(yy - 1);
      jdnm = result[1];
      mc = result[2];
    }

    int mi = 0;
    for (int i = 0; i <= 14; i++) {
      if (jd.floor() >= (jdnm[i] + 0.5).floor() && jd.floor() < (jdnm[i + 1] + 0.5).floor()) {
        mi = i;
        break;
      }
    }

    if (mc[mi]! < 2 || prev == 1) {
      yy = yy - 1;
    }

    if ((mc[mi]! - mc[mi]!.floor()) * 2 + 1 != 1) {
      isLeap = 1;
    }

    mm = ((mc[mi]! + 10).floor() % 12) + 1;
    dd = jd.floor() - (jdnm[mi] + 0.5).floor() + 1;

    return [yy, mm, dd, isLeap];
  }

  /// 计算凶亡地支
  Map<String, dynamic> _getXiongWang(int dayTgInt, int dayDzInt) {
    int xwStart = dayDzInt - dayTgInt - 2;
    if (xwStart < 0) xwStart += 12;
    if (xwStart == 12) xwStart = 0;

    int xwEnd = xwStart + 1;
    if (xwEnd == 12) xwEnd = 0;

    return {
      'index': [xwStart, xwEnd],
      'char': cdz[xwStart] + cdz[xwEnd]
    };
  }

  /// 计算命宫
  Map<String, dynamic> _getGong(int yearTg, int monthDz, int hourDz) {
    int gongDz = (29 - monthDz - hourDz) % 12;
    int xi = gongDz < 2 ? 1 : 0;
    int gongTg = ((yearTg % 5) * 2 + gongDz + 12 * xi) % 10;

    return {
      'index': [gongTg, gongDz],
      'char': ctg[gongTg] + cdz[gongDz]
    };
  }

  /// 计算北京时间属于哪个时辰,并划分时头时中还是时尾
  Map<String, dynamic> getCTPart(int hh, int ii) {
    List<String> des = ['时头', '时中', '时尾'];
    int shiChen = ((hh + 1) / 2).floor();
    int part = 0;

    if (hh % 2 == 1) {
      if (ii > 40) part = 1;
    } else {
      if (ii < 20) {
        part = 1;
      } else {
        part = 2;
      }
    }

    return {
      'index': [shiChen, part],
      'char': cdz[shiChen] + des[part]
    };
  }

  /// 获取三合的地支和属性
  Map<String, dynamic> getSanHe(int dz) {
    int fir = (dz % 4) * 3;
    int sec = (fir + 4) % 12;
    int thr = (sec + 4) % 12;
    List<int> juArray = [4, 0, 1, 3];
    int ju = juArray[(fir / 3).floor()];

    return {
      'sanhe': [fir, sec, thr],
      'ju': ju
    };
  }

  /// 获取某地支的相冲的地支
  int getChong(int dz) {
    return (dz + 6) % 12;
  }

  /// 根据某一个地支,获取刑它的地支
  int getXingFrom(int dz) {
    List<int> xingMap = [3, 7, 5, 0, 4, 8, 6, 10, 2, 9, 1, 11];
    return xingMap[dz];
  }

  /// 六合
  Map<String, dynamic> getLiuHe(int dz) {
    int tmp = dz == 0 ? 12 : dz;
    int he = (13 - tmp) % 12;
    List<int> hua = [2, 2, 0, 1, 3, 4, 2];
    int ju = dz < he ? hua[dz] : hua[he];

    return {
      'index': he,
      'ju': ju
    };
  }

  /// 获取六害的另外一个地支
  int getChuan(int dz) {
    return (19 - dz) % 12;
  }

  /// 获取相破的另外一个地支
  int getPo(int dz) {
    List<int> map = [9, 4, 11, 6, 1, 8, 6, 10, 5, 0, 2, 11];
    return map[dz];
  }

  /// 天干相合的另外一个天干与合成的局
  Map<String, dynamic>? tgHe(int monthDz, int tgA) {
    if (tgA > 11) return null;

    int tgFrom, tgTag;
    if (tgA >= 5) {
      tgFrom = tgA - 5;
      tgTag = tgA;
    } else {
      tgFrom = tgA;
      tgTag = tgA + 5;
    }

    List<List<int>> map = [
      [1, 4, 5, 6, 7, 10],
      [4, 8, 9, 10, 1],
      [8, 9, 11, 0, 1],
      [2, 3, 4, 11, 0, 1],
      [2, 3, 4, 5, 6, 7]
    ];
    List<int> juMap = [2, 3, 4, 0, 1];
    List<int> dzMust = map[tgFrom];

    if (dzMust.contains(monthDz)) {
      return {
        'he': [tgFrom, tgTag],
        'ju': juMap[tgFrom]
      };
    } else {
      return null;
    }
  }

  /// 完善的公历年排盘 - 包含所有功能
  Map<String, dynamic> getFullInfo(int gd, int yy, int mm, int dd, int hh, [int mt = 0, int ss = 0]) {
    if (![0, 1].contains(gd)) return {};

    Map<String, dynamic> ret = {};
    ret['sex'] = gd;

    List<int> bigTg = [];
    List<int> bigDz = [];

    Map<String, dynamic> ganZhiResult = getGanZhi(yy, mm, dd, hh, mt, ss);
    if (ganZhiResult.isEmpty) return {};

    List<int> tg = ganZhiResult['tg'];
    List<int> dz = ganZhiResult['dz'];
    double jd = ganZhiResult['jd'];
    List<double> jq = ganZhiResult['jq'];
    int ix = ganZhiResult['ix'];

    // 计算凶亡
    Map<String, dynamic> xiongWang = _getXiongWang(tg[2], dz[2]);
    int pn = tg[0] % 2; // 起大运.阴阳年干:0阳年1阴年

    double span;
    if ((gd == 0 && pn == 0) || (gd == 1 && pn == 1)) {
      // 阳男阴女顺排：从月柱开始顺数
      span = jq[ix + 1] - jd; // 到下一个节气的时间跨度
      for (int i = 1; i <= 12; i++) {
        bigTg.add((tg[1] + i) % 10); // 月干顺排
        bigDz.add((dz[1] + i) % 12); // 月支顺排
      }
    } else {
      // 阴男阳女逆排：往前数一个节
      span = jd - jq[ix]; // 从当前节气开始的时间跨度
      for (int i = 1; i <= 12; i++) {
        // 修正：大运应该从月柱开始排，不管顺逆都是如此
        // 逆排就是月干支往前推
        bigTg.add((tg[1] + 10 - i) % 10);  // 从月干开始逆排
        bigDz.add((dz[1] + 12 - i) % 12);  // 从月支开始逆排
      }
    }

    int days = (span * 4 * 30).floor();
    int y = (days / 360).floor();
    int m = ((days % 360) / 30).floor();
    int d = (days % 360) % 30;

    ret['tg'] = tg; // 四柱天干
    ret['dz'] = dz; // 四柱地支
    ret['bazi'] = [];
    ret['sc'] = getCTPart(hh, mt); // 时辰划分
    ret['dz_cg'] = []; // 地支藏干

    // 构建八字字符串和地支藏干
    List<Map<String, dynamic>> tgCgGod = [];
    List<Map<String, dynamic>> dzMainGod = [];
    List<List<Map<String, dynamic>>> dzGod = [];
    List<Map<String, dynamic>> selfQiList = [];
    List<List<dynamic>> naYinList = [];
    List<Map<String, dynamic>> csList = [];
    List<Map<String, dynamic>> yearCsList = [];
    List<Map<String, dynamic>> monthCsList = [];
    List<Map<String, dynamic>> hourCsList = [];

    for (int i = 0; i <= 3; i++) {
      ret['bazi'].add([ctg[tg[i]], cdz[dz[i]]]);
      tgCgGod.add(_getTenGod(tg[2], tg[i]));

      List<int> tmpDzcg = dzcg[dz[i]];
      List<Map<String, dynamic>> tmpDzGod = [];
      List<String> tmpDzcgChar = [];
      for (int cg in tmpDzcg) {
        tmpDzGod.add(_getTenGod(tg[2], cg));
        tmpDzcgChar.add(ctg[cg]);
      }
      ret['dz_cg'].add({
        'index': tmpDzcg,
        'char': tmpDzcgChar
      });

      dzMainGod.add(_getTenGod(tg[2], dztg[dz[i]]));
      dzGod.add(tmpDzGod);
      selfQiList.add(_getSelfQi(tg[2], dz[i]));
      naYinList.add(naYin(tg[i], dz[i]));

      csList.add(getCs(tg[2], dz[i])); // 日长生
      yearCsList.add(getCs(tg[0], dz[i])); // 年长生
      monthCsList.add(getCs(tg[1], dz[i])); // 月长生
      hourCsList.add(getCs(tg[3], dz[i])); // 时长生
    }

    // 日干设为"元"
    tgCgGod[2] = {'index': [5, 5], 'char': '元'};

    ret['na_yin'] = naYinList;
    ret['xw'] = xiongWang; // 凶亡
    ret['gong'] = _getGong(tg[0], dz[1], dz[3]); // 命宫
    ret['tg_cg_god'] = tgCgGod;
    ret['dz_main_god'] = dzMainGod;
    ret['dz_cg_god'] = dzGod;
    ret['day_cs'] = csList;
    ret['year_cs'] = yearCsList;
    ret['month_cs'] = monthCsList;
    ret['hour_cs'] = hourCsList;
    ret['self_qi'] = selfQiList;
    ret['big_tg'] = bigTg;
    ret['big_dz'] = bigDz;
    ret['start_desc'] = "$y年$m月$d天起运";

    // 修正起运时间计算：三天折合一年，一天折合四个月，一个时辰折合十天
    // 反推得到一年按360天算，所以 span * 120 是正确的
    double startJdtime = jd + span * 120;
    ret['start_time'] = julian2Solar(startJdtime);

    ret['big'] = [];
    ret['years_info'] = [];
    ret['big_start_time'] = [];
    ret['big_god'] = [];
    ret['big_cs'] = [];

    ret['xz'] = cxz[getZodiac(mm, dd) ?? 0]; // 星座
    ret['sx'] = csa[dz[0]]; // 生肖

    // 大运信息
    for (int i = 0; i < 12; i++) {
      ret['big'].add(ctg[bigTg[i]] + cdz[bigDz[i]]);
      ret['big_cs'].add(getCs(tg[2], bigDz[i]));
      ret['big_god'].add(_getTenGod(tg[2], bigTg[i]));
      // 修正：每步大运间隔10年，使用365.25天更精确
      ret['big_start_time'].add(julian2Solar(startJdtime + i * 10 * 365.25));
    }

    // 流年信息
    for (int i = 1, j = 0; ; i++) {
      if ((yy + i) < ret['start_time'][0]) {
        continue;
      }
      if (j++ >= 120) {
        break;
      }

      int t = (tg[0] + i) % 10;
      int d = (dz[0] + i) % 12;

      List<int> tmpYearDzcg = dzcg[d];
      List<Map<String, dynamic>> tmpYearGod = [];
      for (int cg in tmpYearDzcg) {
        tmpYearGod.add(_getTenGod(tg[2], cg));
      }

      ret['years_info'].add({
        'year': yy + i - 1,
        'index': [t, d],
        'char': ctg[t] + cdz[d],
        'cg': tmpYearDzcg,
        'cs': getCs(tg[2], d),
        'tg_god': _getTenGod(tg[2], t),
        'dz_god': tmpYearGod
      });
    }

    return ret;
  }
}
