// lib/utils/perpetual_calendar.dart
import '../services/paipan_service.dart';

/// 万年历功能模块
/// 提供完整的公历、农历日期查询和转换功能
/// 重构后统一使用 PaipanService 进行所有计算
class PerpetualCalendar {
  static final PaipanService _paipanService = PaipanService();

  /// 获取指定年份的所有月份信息
  /// [year] 公历年份
  /// 返回包含每月详细信息的列表
  static List<Map<String, dynamic>> getYearInfo(int year) {
    List<Map<String, dynamic>> months = [];

    for (int month = 1; month <= 12; month++) {
      months.add(getMonthInfo(year, month));
    }

    return months;
  }

  /// 获取指定月份的详细信息
  /// [year] 公历年份
  /// [month] 公历月份
  /// 返回月份详细信息
  static Map<String, dynamic> getMonthInfo(int year, int month) {
    DateTime firstDay = DateTime(year, month, 1);
    DateTime lastDay = DateTime(year, month + 1, 0);
    int daysInMonth = lastDay.day;

    List<Map<String, dynamic>> days = [];

    for (int day = 1; day <= daysInMonth; day++) {
      DateTime date = DateTime(year, month, day);
      days.add(getDayInfo(date));
    }

    return {
      'year': year,
      'month': month,
      'daysInMonth': daysInMonth,
      'firstDay': firstDay,
      'lastDay': lastDay,
      'days': days,
    };
  }

  /// 获取指定日期的详细信息
  /// [date] 公历日期
  /// 返回日期详细信息
  static Map<String, dynamic> getDayInfo(DateTime date) {
    // 使用统一的排盘服务获取干支信息
    Map<String, dynamic> ganZhiResult = _paipanService.getGanZhi(
      date.year, date.month, date.day, 12, 0, 0
    );

    // 使用排盘服务获取农历信息
    List<int>? lunarResult = _paipanService.solar2Lunar(date.year, date.month, date.day);
    Map<String, dynamic> lunarInfo = {};

    if (lunarResult != null && lunarResult.length >= 4) {
      lunarInfo = {
        'year': lunarResult[0],
        'month': lunarResult[1],
        'day': lunarResult[2],
        'isLeapMonth': lunarResult[3] == 1,
      };
    } else {
      // 如果转换失败，使用默认值
      lunarInfo = {
        'year': date.year,
        'month': date.month,
        'day': date.day,
        'isLeapMonth': false,
      };
    }

    // 获取星期信息
    String weekdayName = _getWeekdayName(date.weekday);

    // 从排盘服务获取干支信息
    String yearStemBranch = '';
    String monthStemBranch = '';
    String dayStemBranch = '';

    if (ganZhiResult.isNotEmpty) {
      List<int> tg = ganZhiResult['tg'];
      List<int> dz = ganZhiResult['dz'];
      yearStemBranch = _paipanService.ctg[tg[0]] + _paipanService.cdz[dz[0]];
      monthStemBranch = _paipanService.ctg[tg[1]] + _paipanService.cdz[dz[1]];
      dayStemBranch = _paipanService.ctg[tg[2]] + _paipanService.cdz[dz[2]];
    }

    // 获取生肖
    String zodiac = '';
    if (ganZhiResult.isNotEmpty) {
      List<int> dz = ganZhiResult['dz'];
      zodiac = _paipanService.csa[dz[0]];
    }

    return {
      'solar': {
        'year': date.year,
        'month': date.month,
        'day': date.day,
        'weekday': weekdayName,
        'weekdayName': weekdayName,
        'weekdayIndex': date.weekday,
        'date': date,
      },
      'lunar': {
        'year': lunarInfo['year'],
        'month': lunarInfo['month'],
        'day': lunarInfo['day'],
        'isLeapMonth': lunarInfo['isLeapMonth'],
        'monthName': _getLunarMonthName(lunarInfo['month'], lunarInfo['isLeapMonth']),
        'dayName': _getLunarDayName(lunarInfo['day']),
        'zodiac': zodiac,
      },
      'stemBranch': {
        'year': yearStemBranch,
        'month': monthStemBranch,
        'day': dayStemBranch,
      },
      'date': date,
      'ganZhiData': ganZhiResult, // 添加完整的干支数据
    };
  }

  /// 查找指定日期范围内的特殊日期
  /// [startDate] 开始日期
  /// [endDate] 结束日期
  /// [criteria] 查找条件
  static List<Map<String, dynamic>> findSpecialDates(
    DateTime startDate,
    DateTime endDate,
    Map<String, dynamic> criteria
  ) {
    List<Map<String, dynamic>> results = [];
    DateTime current = startDate;

    while (current.isBefore(endDate) || current.isAtSameMomentAs(endDate)) {
      Map<String, dynamic> dayInfo = getDayInfo(current);

      if (_matchesCriteria(dayInfo, criteria)) {
        results.add(dayInfo);
      }

      current = current.add(Duration(days: 1));
    }

    return results;
  }

  /// 获取指定年份的农历年信息
  /// [lunarYear] 农历年份
  static Map<String, dynamic> getLunarYearInfo(int lunarYear) {
    // 使用排盘服务进行农历转公历
    List<int>? lunarNewYearResult = _paipanService.lunar2Solar(lunarYear, 1, 1, false);
    List<int>? nextLunarNewYearResult = _paipanService.lunar2Solar(lunarYear + 1, 1, 1, false);

    if (lunarNewYearResult == null || nextLunarNewYearResult == null) {
      // 如果转换失败，返回默认值
      return {
        'year': lunarYear,
        'startDate': DateTime(lunarYear, 1, 1),
        'endDate': DateTime(lunarYear, 12, 31),
        'totalDays': 365,
        'leapMonth': 0,
        'hasLeapMonth': false,
        'stemBranch': _getYearStemBranch(lunarYear),
      };
    }

    DateTime lunarNewYear = DateTime(lunarNewYearResult[0], lunarNewYearResult[1], lunarNewYearResult[2]);
    DateTime nextLunarNewYear = DateTime(nextLunarNewYearResult[0], nextLunarNewYearResult[1], nextLunarNewYearResult[2]);
    DateTime lunarYearEnd = nextLunarNewYear.subtract(Duration(days: 1));

    // 计算农历年的总天数
    int totalDays = lunarYearEnd.difference(lunarNewYear).inDays + 1;

    // 获取闰月信息（简化实现）
    int leapMonth = 0; // 需要实现具体的闰月计算逻辑

    return {
      'year': lunarYear,
      'startDate': lunarNewYear,
      'endDate': lunarYearEnd,
      'totalDays': totalDays,
      'leapMonth': leapMonth,
      'hasLeapMonth': leapMonth > 0,
      'stemBranch': _getYearStemBranch(lunarYear),
    };
  }
  
  /// 获取两个日期之间的天数
  /// [startDate] 开始日期
  /// [endDate] 结束日期
  static int getDaysBetween(DateTime startDate, DateTime endDate) {
    return endDate.difference(startDate).inDays;
  }
  
  /// 获取指定日期是一年中的第几天
  /// [date] 日期
  static int getDayOfYear(DateTime date) {
    DateTime firstDayOfYear = DateTime(date.year, 1, 1);
    return date.difference(firstDayOfYear).inDays + 1;
  }
  
  /// 获取指定日期是一年中的第几周
  /// [date] 日期
  static int getWeekOfYear(DateTime date) {
    DateTime firstDayOfYear = DateTime(date.year, 1, 1);
    int dayOfYear = getDayOfYear(date);
    int firstWeekday = firstDayOfYear.weekday;
    
    return ((dayOfYear + firstWeekday - 2) / 7).ceil();
  }
  
  /// 判断是否为闰年
  /// [year] 年份
  static bool isLeapYear(int year) {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
  }
  

  

  
  /// 获取农历月份名称
  static String _getLunarMonthName(int month, bool isLeapMonth) {
    const monthNames = [
      '', '正月', '二月', '三月', '四月', '五月', '六月',
      '七月', '八月', '九月', '十月', '冬月', '腊月'
    ];

    if (month < 1 || month > 12) return '未知月';

    String name = monthNames[month];
    return isLeapMonth ? '闰$name' : name;
  }

  /// 获取农历日期名称
  static String _getLunarDayName(int day) {
    const dayNames = [
      '', '初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
      '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
      '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'
    ];

    if (day < 1 || day > 30) return '未知日';
    return dayNames[day];
  }
  
  /// 获取年干支
  static String _getYearStemBranch(int year) {
    // 使用排盘服务计算年干支
    Map<String, dynamic> ganZhiResult = _paipanService.getGanZhi(year, 1, 1, 12, 0, 0);
    if (ganZhiResult.isNotEmpty) {
      List<int> tg = ganZhiResult['tg'];
      List<int> dz = ganZhiResult['dz'];
      return _paipanService.ctg[tg[0]] + _paipanService.cdz[dz[0]];
    }
    return '';
  }

  /// 获取星期名称
  static String _getWeekdayName(int weekday) {
    const weekdayNames = ['', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];
    if (weekday < 1 || weekday > 7) return '未知';
    return weekdayNames[weekday];
  }

  /// 检查日期信息是否匹配查找条件
  static bool _matchesCriteria(Map<String, dynamic> dayInfo, Map<String, dynamic> criteria) {
    // 实现查找条件匹配逻辑
    // 可以根据干支、农历日期、星期等条件进行匹配

    // 检查干支条件
    if (criteria.containsKey('stemBranch')) {
      Map<String, String> targetStemBranch = criteria['stemBranch'];
      Map<String, String> dayStemBranch = dayInfo['stemBranch'];

      for (String key in targetStemBranch.keys) {
        if (dayStemBranch[key] != targetStemBranch[key]) {
          return false;
        }
      }
    }

    // 检查农历条件
    if (criteria.containsKey('lunar')) {
      Map<String, dynamic> targetLunar = criteria['lunar'];
      Map<String, dynamic> dayLunar = dayInfo['lunar'];

      for (String key in targetLunar.keys) {
        if (dayLunar[key] != targetLunar[key]) {
          return false;
        }
      }
    }

    // 检查星期条件
    if (criteria.containsKey('weekday')) {
      int targetWeekday = criteria['weekday'];
      int dayWeekday = dayInfo['solar']['weekdayIndex'];
      if (dayWeekday != targetWeekday) {
        return false;
      }
    }

    return true;
  }
}
