# 四个核心功能模块实现说明

## 功能概述

已成功实现四个核心功能模块，大幅增强了系统的准确性和灵活性：

1. **万年历功能模块** (`PerpetualCalendar`)
2. **二十四节气计算功能** (`SolarTermsCalculator`)
3. **年柱起始点选择功能**
4. **子时处理方式选择功能**
5. **闰月分界处理选择功能**
6. **高级设置管理** (`AdvancedSettings` + `SettingsService`)

## 1. 万年历功能模块

### 核心功能
- **完整的万年历查询**：支持公历、农历日期查询和转换
- **年份信息获取**：获取指定年份的所有月份详细信息
- **月份信息获取**：获取指定月份的所有日期详细信息
- **日期信息获取**：获取指定日期的完整信息（公历、农历、干支、星期）
- **特殊日期查找**：根据条件查找特定日期
- **农历年信息**：获取农历年的详细信息（闰月、总天数等）

### 主要方法
```dart
// 获取年份信息
List<Map<String, dynamic>> getYearInfo(int year)

// 获取月份信息
Map<String, dynamic> getMonthInfo(int year, int month)

// 获取日期信息
Map<String, dynamic> getDayInfo(DateTime date)

// 查找特殊日期
List<Map<String, dynamic>> findSpecialDates(DateTime startDate, DateTime endDate, Map<String, dynamic> criteria)
```

## 2. 二十四节气计算功能

### 核心功能
- **精确到秒级的节气计算**：基于天体力学算法
- **地理位置修正**：考虑真太阳时和地理位置影响
- **完整年份节气**：计算指定年份的所有24个节气
- **节气查询**：获取最近的、之前的、之后的节气
- **节气判断**：判断指定日期是否为节气日

### 技术特点
- **天文算法**：基于太阳黄经计算，使用牛顿迭代法精确求解
- **时差修正**：考虑地球轨道偏心率的时差方程
- **地方时支持**：根据经纬度进行地方时修正

### 主要方法
```dart
// 计算年份所有节气
List<Map<String, dynamic>> calculateYearSolarTerms(int year, {double? longitude})

// 计算指定节气时间
DateTime calculateSolarTerm(int year, int termIndex, {double? longitude})

// 获取最近节气
Map<String, dynamic> getNearestSolarTerm(DateTime date, {double? longitude})
```

## 3. 年柱起始点选择功能

### 选项说明
- **以立春为新年**（默认）：符合传统命理学标准，年柱干支在立春时刻发生变化
- **以正月初一为新年**：年柱干支在农历新年零时发生变化

### 实现原理
- 当选择"以立春为新年"时，系统会精确计算立春的时分秒
- 如果出生时间在立春之前，年柱按上一年计算
- 如果出生时间在立春之后，年柱按当年计算

### 影响范围
- 直接影响年柱干支的计算结果
- 在立春前后出生的人会有不同的年柱

## 4. 子时处理方式选择功能

### 选项说明
- **早子时**（默认）：23:00-01:00算作第二天，即23:00后日柱干支按次日计算
- **晚子时**：23:00-24:00算作当天，00:00-01:00算作第二天

### 实现原理
- 早子时模式：23:00开始就按次日的日柱计算
- 晚子时模式：只有过了00:00才按次日的日柱计算

### 影响范围
- 影响日柱干支的计算，特别是在23:00-01:00时间段
- 不同的处理方式可能导致不同的日柱结果

## 5. 闰月分界处理选择功能

### 选项说明
- **闰月月中分界**（默认）：闰月的前半月算上个月，后半月算下个月
- **上月闰月**：整个闰月都算作上个月
- **下月闰月**：整个闰月都算作下个月

### 实现原理
- 月中分界：以闰月15日为界限
- 上月闰月：整个闰月归属于前一个月
- 下月闰月：整个闰月归属于后一个月

### 影响范围
- 影响月柱干支的计算
- 在闰月出生的人会根据不同设置得到不同的月柱

## 6. 高级设置管理

### 设置模型 (`AdvancedSettings`)
```dart
class AdvancedSettings {
  final YearPillarStartPoint yearPillarStartPoint;
  final ZiHourMode ziHourMode;
  final LeapMonthBoundary leapMonthBoundary;
  final bool useApparentSolarTime;
  final bool showSolarTerms;
}
```

### 持久化服务 (`SettingsService`)
- **设置保存**：自动保存用户的设置选择
- **设置加载**：应用启动时自动加载之前的设置
- **设置重置**：支持重置为默认设置
- **设置导入导出**：支持设置的备份和恢复

## 界面集成

### 高级设置界面
- **设置切换开关**：控制是否显示高级设置选项
- **选项对话框**：为每个设置提供清晰的选择界面
- **实时更新**：设置变化立即触发重新计算
- **设置说明**：为每个选项提供详细的说明文档

### 节气信息显示
- **节气卡片**：显示最近的节气信息
- **节气详情**：包含节气时间、季节、描述等
- **可选显示**：用户可选择是否显示节气信息

## 技术优势

### 1. 精确性
- **天文算法**：基于精确的天体力学计算
- **地理修正**：考虑地理位置对时间的影响
- **多种标准**：支持不同的传统计算标准

### 2. 灵活性
- **多种选项**：提供多种计算方式供用户选择
- **设置持久化**：保存用户的偏好设置
- **实时切换**：可以实时切换不同的计算方式

### 3. 兼容性
- **向后兼容**：保持与现有功能的完全兼容
- **渐进增强**：新功能作为可选项，不影响基本使用
- **标准支持**：支持多种传统命理学标准

## 应用场景

### 1. 专业命理应用
- **精确排盘**：为专业命理师提供精确的计算工具
- **多种标准**：支持不同流派的计算方法
- **边界处理**：正确处理时辰、年份、月份的边界情况

### 2. 学术研究
- **算法验证**：提供多种算法供研究对比
- **历史数据**：支持历史日期的准确计算
- **节气研究**：精确的节气时间计算

### 3. 文化传承
- **传统知识**：展示传统历法和节气文化
- **教育价值**：帮助用户理解传统时间体系
- **文化保护**：保持传统计算方法的准确性

## 使用建议

### 1. 设置选择
- **默认设置**：推荐使用默认设置，符合主流标准
- **特殊需求**：根据具体需求调整相应设置
- **一致性**：在同一批计算中保持设置的一致性

### 2. 精度要求
- **时间精确**：建议提供精确到分钟的出生时间
- **地理位置**：提供准确的出生地经纬度
- **边界注意**：在时辰、节气边界时特别注意

### 3. 结果验证
- **多种方式**：可以尝试不同设置进行对比验证
- **专业咨询**：重要应用建议咨询专业人士
- **文档参考**：参考相关的传统文献和现代研究

这四个核心功能模块的实现，为紫微斗数等传统命理应用提供了更加精确、灵活、专业的计算基础，确保了系统的准确性和实用性。
