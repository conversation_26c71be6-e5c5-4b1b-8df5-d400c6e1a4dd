// 日柱计算精度测试
// 用于验证修正后的日柱计算精度

import 'lib/services/paipan_service.dart';
import 'lib/utils/stem_branch_calculator.dart';
import 'lib/models/advanced_settings.dart';

void main() {
  print('=== 日柱计算精度测试 ===\n');
  
  // 测试用例：一些关键的日期
  List<Map<String, dynamic>> testCases = [
    {
      'date': DateTime(2024, 1, 1, 12, 0, 0),
      'description': '2024年元旦正午',
    },
    {
      'date': DateTime(2024, 2, 10, 0, 0, 0),  // 春节
      'description': '2024年春节子时',
    },
    {
      'date': DateTime(2024, 6, 21, 6, 0, 0),  // 夏至
      'description': '2024年夏至卯时',
    },
    {
      'date': DateTime(2024, 12, 21, 18, 0, 0), // 冬至
      'description': '2024年冬至酉时',
    },
    {
      'date': DateTime(2024, 3, 15, 23, 30, 0), // 子时测试
      'description': '2024年3月15日晚子时',
    },
  ];
  
  PaipanService paipanService = PaipanService();
  AdvancedSettings settings = AdvancedSettings.defaultSettings();
  
  for (var testCase in testCases) {
    DateTime date = testCase['date'];
    String description = testCase['description'];
    
    print('测试案例: $description');
    print('日期时间: ${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}:${date.second.toString().padLeft(2, '0')}');
    
    // 使用PaipanService计算
    Map<String, dynamic> paipanResult = paipanService.getGanZhi(
      date.year, date.month, date.day, date.hour, date.minute, date.second
    );
    
    String paipanDayPillar = '';
    if (paipanResult.isNotEmpty) {
      List<int> tg = paipanResult['tg'];
      List<int> dz = paipanResult['dz'];
      paipanDayPillar = paipanService.ctg[tg[2]] + paipanService.cdz[dz[2]];
    }
    
    // 使用StemBranchCalculator计算
    String stemBranchDayPillar = StemBranchCalculator.getDayStemBranchWithSettings(
      date, 
      settings: settings
    );
    
    print('PaipanService结果:     $paipanDayPillar');
    print('StemBranchCalculator: $stemBranchDayPillar');
    print('结果一致性: ${paipanDayPillar == stemBranchDayPillar ? '✓ 一致' : '✗ 不一致'}');
    print('');
  }
  
  // 测试子时处理
  print('=== 子时处理测试 ===\n');
  
  DateTime ziHourDate = DateTime(2024, 3, 15, 23, 30, 0);
  
  // 测试早子时模式
  AdvancedSettings earlyZiSettings = settings.copyWith(ziHourMode: ZiHourMode.early);
  String earlyZiResult = StemBranchCalculator.getDayStemBranchWithSettings(
    ziHourDate, 
    settings: earlyZiSettings
  );
  
  // 测试晚子时模式
  AdvancedSettings lateZiSettings = settings.copyWith(ziHourMode: ZiHourMode.late);
  String lateZiResult = StemBranchCalculator.getDayStemBranchWithSettings(
    ziHourDate, 
    settings: lateZiSettings
  );
  
  print('测试时间: 2024-03-15 23:30:00');
  print('早子时模式结果: $earlyZiResult');
  print('晚子时模式结果: $lateZiResult');
  print('两种模式差异: ${earlyZiResult != lateZiResult ? '✓ 正确区分' : '✗ 未正确区分'}');
  
  print('\n=== 测试完成 ===');
}
