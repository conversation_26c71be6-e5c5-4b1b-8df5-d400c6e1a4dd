// lib/services/chart_service.dart
import '../models/birth_data.dart';
import '../models/chart_data.dart';
import '../models/palace.dart';
import 'chart_calculator.dart';

class ChartService {
  final ChartCalculator _calculator = ChartCalculator();
  
  // 计算命盘
  ChartData calculateChart(BirthData birthData) {
    try {
      // 使用计算器计算命盘数据
      final chartData = _calculator.calculateChart(birthData);
      return chartData;
    } catch (e) {
      print('计算命盘失败: $e');
      // 返回一个基础的命盘数据
      return _createDefaultChart(birthData);
    }
  }

  
  // 创建默认命盘（用于演示）
  ChartData _createDefaultChart(BirthData birthData) {
    final palaces = <Palace>[];
    final palaceNames = [
      '命宫', '兄弟宫', '夫妻宫', '子女宫', '财帛宫', '疾厄宫',
      '迁移宫', '奴仆宫', '官禄宫', '田宅宫', '福德宫', '父母宫'
    ];
    
    final earthBranches = [
      '子', '丑', '寅', '卯', '辰', '巳',
      '午', '未', '申', '酉', '戌', '亥'
    ];
    
    // 简单的命宫计算（基于出生月份和时辰）
    final ascendant = (birthData.month + birthData.hour) % 12;
    final bodyPalace = (ascendant + 1) % 12;
    
    // 创建示例宫位数据
    for (int i = 0; i < 12; i++) {
      final mainStars = <String>[];
      final minorStars = <String>[];
      final transformStars = <String>[];
      
      // 为命宫添加一些示例主星
      if (i == 0) { // 命宫
        mainStars.addAll(['紫微', '天府']);
      } else if (i == 4) { // 财帛宫
        mainStars.add('武曲');
      } else if (i == 8) { // 官禄宫
        mainStars.add('天相');
      }
      
      // 添加一些示例辅星
      if (i % 3 == 0) {
        minorStars.add('左辅');
      }
      if (i % 4 == 0) {
        minorStars.add('右弼');
      }
      
      // 添加一些化星
      if (i == ascendant) {
        transformStars.add('化权');
      }
      
      palaces.add(Palace(
        index: i,
        name: palaceNames[i],
        earthBranch: earthBranches[(ascendant + i) % 12],
        mainStars: mainStars,
        minorStars: minorStars,
        transformStars: transformStars,
      ));
    }
    
    return ChartData(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: birthData.name,
      gender: birthData.gender,
      birthData: birthData,
      ascendant: ascendant,
      bodyPalace: bodyPalace,
      palaces: palaces,
      createdAt: DateTime.now(),
    );
  }
  
  // 获取宫位详细信息
  Map<String, dynamic> getPalaceDetails(Palace palace) {
    return {
      'name': palace.name,
      'earthBranch': palace.earthBranch,
      'mainStars': palace.mainStars,
      'minorStars': palace.minorStars,
      'transformStars': palace.transformStars,
      'description': _getPalaceDescription(palace),
    };
  }
  
  // 获取宫位描述
  String _getPalaceDescription(Palace palace) {
    switch (palace.name) {
      case '命宫':
        return '代表个人的性格特质、外貌特征、先天体质等';
      case '兄弟宫':
        return '代表兄弟姐妹关系、朋友关系、合作伙伴等';
      case '夫妻宫':
        return '代表婚姻状况、配偶特征、感情运势等';
      case '子女宫':
        return '代表子女状况、创造力、部属关系等';
      case '财帛宫':
        return '代表财运状况、理财能力、收入来源等';
      case '疾厄宫':
        return '代表健康状况、疾病倾向、意外灾害等';
      case '迁移宫':
        return '代表外出运势、人际关系、社交能力等';
      case '奴仆宫':
        return '代表朋友关系、部属关系、社交圈子等';
      case '官禄宫':
        return '代表事业运势、工作状况、社会地位等';
      case '田宅宫':
        return '代表不动产、居住环境、家庭状况等';
      case '福德宫':
        return '代表精神享受、兴趣爱好、福气深浅等';
      case '父母宫':
        return '代表父母关系、长辈关系、学习能力等';
      default:
        return '宫位信息';
    }
  }
}
