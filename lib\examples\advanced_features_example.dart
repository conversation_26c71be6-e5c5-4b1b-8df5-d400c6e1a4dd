// lib/examples/advanced_features_example.dart
// 四个核心功能模块使用示例

import 'package:flutter/material.dart';
import '../utils/perpetual_calendar.dart';
import '../utils/solar_terms_calculator.dart';
import '../utils/stem_branch_calculator.dart';
import '../models/advanced_settings.dart';
import '../services/settings_service.dart';

/// 高级功能示例类
class AdvancedFeaturesExample {
  
  /// 演示万年历功能
  static void demonstratePerpetualCalendar() {
    print('=== 万年历功能演示 ===');
    
    // 获取2024年信息
    DateTime testDate = DateTime(2024, 6, 21); // 夏至
    Map<String, dynamic> dayInfo = PerpetualCalendar.getDayInfo(testDate);
    
    print('日期：${testDate.year}年${testDate.month}月${testDate.day}日');
    print('星期：${dayInfo['solar']['weekday']}');
    print('农历：${dayInfo['lunar']['year']}年${dayInfo['lunar']['monthName']}${dayInfo['lunar']['dayName']}');
    print('年干支：${dayInfo['stemBranch']['year']}');
    print('月干支：${dayInfo['stemBranch']['month']}');
    print('日干支：${dayInfo['stemBranch']['day']}');
    print('');
    
    // 获取月份信息
    Map<String, dynamic> monthInfo = PerpetualCalendar.getMonthInfo(2024, 6);
    print('2024年6月信息：');
    print('总天数：${monthInfo['daysInMonth']}天');
    print('第一天：${monthInfo['firstDay']}');
    print('最后一天：${monthInfo['lastDay']}');
    print('');
  }
  
  /// 演示二十四节气计算
  static void demonstrateSolarTerms() {
    print('=== 二十四节气计算演示 ===');
    
    // 计算2024年的所有节气
    List<Map<String, dynamic>> solarTerms = SolarTermsCalculator.calculateYearSolarTerms(2024);
    
    print('2024年部分节气时间：');
    for (int i = 0; i < 6; i++) {
      var term = solarTerms[i];
      DateTime time = term['time'];
      print('${term['name']}：${time.year}年${time.month}月${time.day}日 ${time.hour}:${time.minute}:${time.second}');
    }
    print('');
    
    // 获取指定日期的最近节气
    DateTime testDate = DateTime(2024, 6, 21, 12, 0, 0);
    Map<String, dynamic> nearestTerm = SolarTermsCalculator.getNearestSolarTerm(testDate);
    
    print('${testDate.year}年${testDate.month}月${testDate.day}日最近的节气：');
    print('节气：${nearestTerm['name']}');
    print('时间：${_formatDateTime(nearestTerm['time'])}');
    print('季节：${nearestTerm['season']}');
    print('描述：${nearestTerm['description']}');
    print('');
  }
  
  /// 演示年柱起始点选择功能
  static void demonstrateYearPillarStartPoint() {
    print('=== 年柱起始点选择演示 ===');
    
    // 测试立春前后的日期
    DateTime beforeLichun = DateTime(2024, 2, 3, 12, 0, 0); // 立春前
    DateTime afterLichun = DateTime(2024, 2, 5, 12, 0, 0);  // 立春后
    
    // 以立春为新年
    AdvancedSettings lichunSettings = AdvancedSettings(
      yearPillarStartPoint: YearPillarStartPoint.lichun,
    );
    
    // 以正月初一为新年
    AdvancedSettings lunarSettings = AdvancedSettings(
      yearPillarStartPoint: YearPillarStartPoint.lunarNewYear,
    );
    
    print('立春前日期（2024年2月3日）：');
    String yearStem1 = StemBranchCalculator.getYearStemBranch(
      beforeLichun.year, 
      birthTime: beforeLichun, 
      settings: lichunSettings
    );
    String yearStem2 = StemBranchCalculator.getYearStemBranch(
      beforeLichun.year, 
      birthTime: beforeLichun, 
      settings: lunarSettings
    );
    print('以立春为新年：$yearStem1');
    print('以正月初一为新年：$yearStem2');
    print('');
    
    print('立春后日期（2024年2月5日）：');
    String yearStem3 = StemBranchCalculator.getYearStemBranch(
      afterLichun.year, 
      birthTime: afterLichun, 
      settings: lichunSettings
    );
    String yearStem4 = StemBranchCalculator.getYearStemBranch(
      afterLichun.year, 
      birthTime: afterLichun, 
      settings: lunarSettings
    );
    print('以立春为新年：$yearStem3');
    print('以正月初一为新年：$yearStem4');
    print('');
  }
  
  /// 演示子时处理方式
  static void demonstrateZiHourMode() {
    print('=== 子时处理方式演示 ===');
    
    // 测试子时时间
    DateTime ziTime1 = DateTime(2024, 6, 21, 23, 30, 0); // 23:30
    DateTime ziTime2 = DateTime(2024, 6, 22, 0, 30, 0);  // 00:30
    
    // 早子时设置
    AdvancedSettings earlyZiSettings = AdvancedSettings(
      ziHourMode: ZiHourMode.early,
    );
    
    // 晚子时设置
    AdvancedSettings lateZiSettings = AdvancedSettings(
      ziHourMode: ZiHourMode.late,
    );
    
    print('23:30时间：');
    String dayStem1 = StemBranchCalculator.getDayStemBranchWithSettings(
      ziTime1, 
      settings: earlyZiSettings
    );
    String dayStem2 = StemBranchCalculator.getDayStemBranchWithSettings(
      ziTime1, 
      settings: lateZiSettings
    );
    print('早子时处理：$dayStem1');
    print('晚子时处理：$dayStem2');
    print('');
    
    print('00:30时间：');
    String dayStem3 = StemBranchCalculator.getDayStemBranchWithSettings(
      ziTime2, 
      settings: earlyZiSettings
    );
    String dayStem4 = StemBranchCalculator.getDayStemBranchWithSettings(
      ziTime2, 
      settings: lateZiSettings
    );
    print('早子时处理：$dayStem3');
    print('晚子时处理：$dayStem4');
    print('');
  }
  
  /// 演示闰月分界处理
  static void demonstrateLeapMonthBoundary() {
    print('=== 闰月分界处理演示 ===');
    
    // 假设闰四月的情况
    int year = 2024;
    int leapMonth = 4;
    int earlyDay = 10;  // 闰月前半月
    int lateDay = 20;   // 闰月后半月
    
    // 三种处理方式
    AdvancedSettings middleSettings = AdvancedSettings(
      leapMonthBoundary: LeapMonthBoundary.middle,
    );
    
    AdvancedSettings previousSettings = AdvancedSettings(
      leapMonthBoundary: LeapMonthBoundary.previous,
    );
    
    AdvancedSettings nextSettings = AdvancedSettings(
      leapMonthBoundary: LeapMonthBoundary.next,
    );
    
    print('闰四月初十：');
    String monthStem1 = StemBranchCalculator.getMonthStemBranch(
      year, leapMonth,
      lunarDay: earlyDay,
      isLeapMonth: true,
      settings: middleSettings,
    );
    String monthStem2 = StemBranchCalculator.getMonthStemBranch(
      year, leapMonth,
      lunarDay: earlyDay,
      isLeapMonth: true,
      settings: previousSettings,
    );
    String monthStem3 = StemBranchCalculator.getMonthStemBranch(
      year, leapMonth,
      lunarDay: earlyDay,
      isLeapMonth: true,
      settings: nextSettings,
    );
    print('月中分界：$monthStem1');
    print('上月闰月：$monthStem2');
    print('下月闰月：$monthStem3');
    print('');
    
    print('闰四月二十：');
    String monthStem4 = StemBranchCalculator.getMonthStemBranch(
      year, leapMonth,
      lunarDay: lateDay,
      isLeapMonth: true,
      settings: middleSettings,
    );
    String monthStem5 = StemBranchCalculator.getMonthStemBranch(
      year, leapMonth,
      lunarDay: lateDay,
      isLeapMonth: true,
      settings: previousSettings,
    );
    String monthStem6 = StemBranchCalculator.getMonthStemBranch(
      year, leapMonth,
      lunarDay: lateDay,
      isLeapMonth: true,
      settings: nextSettings,
    );
    print('月中分界：$monthStem4');
    print('上月闰月：$monthStem5');
    print('下月闰月：$monthStem6');
    print('');
  }
  
  /// 演示设置持久化
  static Future<void> demonstrateSettingsPersistence() async {
    print('=== 设置持久化演示 ===');
    
    // 初始化设置服务
    await SettingsService.instance.initialize();
    
    // 创建自定义设置
    AdvancedSettings customSettings = AdvancedSettings(
      yearPillarStartPoint: YearPillarStartPoint.lunarNewYear,
      ziHourMode: ZiHourMode.late,
      leapMonthBoundary: LeapMonthBoundary.previous,
      useApparentSolarTime: false,
      showSolarTerms: true,
    );
    
    print('保存自定义设置：');
    print(customSettings.summary);
    
    // 保存设置
    bool saveResult = await SettingsService.instance.saveAdvancedSettings(customSettings);
    print('保存结果：${saveResult ? "成功" : "失败"}');
    print('');
    
    // 加载设置
    AdvancedSettings loadedSettings = await SettingsService.instance.loadAdvancedSettings();
    print('加载的设置：');
    print(loadedSettings.summary);
    print('设置是否一致：${customSettings == loadedSettings}');
    print('');
    
    // 重置设置
    bool resetResult = await SettingsService.instance.resetAdvancedSettings();
    print('重置结果：${resetResult ? "成功" : "失败"}');
    
    AdvancedSettings defaultSettings = await SettingsService.instance.loadAdvancedSettings();
    print('重置后的设置：');
    print(defaultSettings.summary);
    print('');
  }
  
  /// 运行所有演示
  static Future<void> runAllDemonstrations() async {
    print('四个核心功能模块演示\n');
    
    demonstratePerpetualCalendar();
    demonstrateSolarTerms();
    demonstrateYearPillarStartPoint();
    demonstrateZiHourMode();
    demonstrateLeapMonthBoundary();
    await demonstrateSettingsPersistence();
    
    print('所有演示完成！');
  }
  
  /// 格式化日期时间
  static String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
  }
}
