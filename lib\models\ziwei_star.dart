// lib/models/ziwei_star.dart
// 紫微斗数星曜模型

/// 星曜类型枚举
enum StarType {
  main,        // 主曜
  auxiliary,   // 辅曜
  miscellaneous, // 杂曜
  transform,   // 化曜
  yearly,      // 流年星
}

/// 星曜亮度等级
enum StarBrightness {
  temple,      // 庙
  prosperous,  // 旺
  favorable,   // 得地
  neutral,     // 平
  trapped,     // 陷
  fallen,      // 落
}

/// 星曜五行属性
enum StarElement {
  wood,   // 木
  fire,   // 火
  earth,  // 土
  metal,  // 金
  water,  // 水
}

/// 星曜阴阳属性
enum StarPolarity {
  yang,   // 阳
  yin,    // 阴
}

/// 紫微斗数星曜模型
class ZiweiStar {
  /// 星曜名称
  final String name;
  
  /// 星曜类型
  final StarType type;
  
  /// 五行属性
  final StarElement element;
  
  /// 阴阳属性
  final StarPolarity polarity;
  
  /// 星曜描述
  final String description;
  
  /// 星曜特性
  final List<String> characteristics;
  
  /// 代表意义
  final String meaning;
  
  /// 所在宫位（动态）
  final int? palaceIndex;

  /// 亮度等级（动态，根据宫位确定）
  final StarBrightness? brightness;
  
  /// 是否为主星
  bool get isMainStar => type == StarType.main;
  
  /// 是否为辅星
  bool get isAuxiliaryStar => type == StarType.auxiliary;
  
  /// 是否为化曜
  bool get isTransformStar => type == StarType.transform;
  
  const ZiweiStar({
    required this.name,
    required this.type,
    required this.element,
    required this.polarity,
    required this.description,
    required this.characteristics,
    required this.meaning,
    this.palaceIndex,
    this.brightness,
  });
  
  /// 复制星曜并设置宫位
  ZiweiStar copyWithPalace(int palaceIndex, {StarBrightness? brightness}) {
    return ZiweiStar(
      name: name,
      type: type,
      element: element,
      polarity: polarity,
      description: description,
      characteristics: characteristics,
      meaning: meaning,
      palaceIndex: palaceIndex,
      brightness: brightness ?? this.brightness,
    );
  }
  
  /// 获取星曜的完整信息
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'type': type.toString(),
      'element': element.toString(),
      'polarity': polarity.toString(),
      'description': description,
      'characteristics': characteristics,
      'meaning': meaning,
      'palaceIndex': palaceIndex,
      'brightness': brightness?.toString(),
    };
  }
  
  @override
  String toString() {
    return name;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ZiweiStar && other.name == name;
  }
  
  @override
  int get hashCode => name.hashCode;
}

/// 化曜类型
enum TransformType {
  lu,    // 化禄
  quan,  // 化权
  ke,    // 化科
  ji,    // 化忌
}

/// 化曜星
class TransformStar extends ZiweiStar {
  /// 化曜类型
  final TransformType transformType;
  
  /// 原始星曜
  final String originalStar;
  
  const TransformStar({
    required String name,
    required this.transformType,
    required this.originalStar,
    required String description,
    required List<String> characteristics,
    required String meaning,
    int? palaceIndex,
    StarBrightness? brightness,
  }) : super(
    name: name,
    type: StarType.transform,
    element: StarElement.earth, // 化曜一般属土
    polarity: StarPolarity.yang,
    description: description,
    characteristics: characteristics,
    meaning: meaning,
    palaceIndex: palaceIndex,
    brightness: brightness,
  );
  
  /// 获取化曜的完整名称
  String get fullName => '$originalStar$name';
  
  @override
  Map<String, dynamic> toMap() {
    var map = super.toMap();
    map['transformType'] = transformType.toString();
    map['originalStar'] = originalStar;
    map['fullName'] = fullName;
    return map;
  }
}

/// 星曜组合（如紫微天府、武曲贪狼等）
class StarCombination {
  /// 组合名称
  final String name;
  
  /// 包含的星曜
  final List<String> stars;
  
  /// 组合特性
  final String characteristics;
  
  /// 组合意义
  final String meaning;
  
  /// 吉凶性质
  final bool isAuspicious;
  
  const StarCombination({
    required this.name,
    required this.stars,
    required this.characteristics,
    required this.meaning,
    required this.isAuspicious,
  });
  
  /// 检查是否匹配指定的星曜列表
  bool matches(List<String> starNames) {
    return stars.every((star) => starNames.contains(star));
  }
}

/// 星曜亮度表（根据宫位确定亮度）
class StarBrightnessTable {
  /// 星曜名称
  final String starName;
  
  /// 各宫位的亮度（索引0-11对应命宫到父母宫）
  final List<StarBrightness> brightness;
  
  const StarBrightnessTable({
    required this.starName,
    required this.brightness,
  });
  
  /// 获取指定宫位的亮度
  StarBrightness getBrightness(int palaceIndex) {
    return brightness[palaceIndex % 12];
  }
}

/// 宫位星曜配置
class PalaceStarConfig {
  /// 宫位索引
  final int palaceIndex;
  
  /// 宫位中的星曜
  final List<ZiweiStar> stars;
  
  /// 宫位干支
  final String stemBranch;
  
  /// 宫位五行
  final StarElement element;
  
  PalaceStarConfig({
    required this.palaceIndex,
    required this.stars,
    required this.stemBranch,
    required this.element,
  });
  
  /// 添加星曜到宫位
  void addStar(ZiweiStar star) {
    stars.add(star.copyWithPalace(palaceIndex));
  }
  
  /// 获取主星列表
  List<ZiweiStar> get mainStars {
    return stars.where((star) => star.isMainStar).toList();
  }
  
  /// 获取辅星列表
  List<ZiweiStar> get auxiliaryStars {
    return stars.where((star) => star.isAuxiliaryStar).toList();
  }
  
  /// 获取化曜列表
  List<ZiweiStar> get transformStars {
    return stars.where((star) => star.isTransformStar).toList();
  }
  
  /// 检查是否有指定星曜
  bool hasStar(String starName) {
    return stars.any((star) => star.name == starName);
  }
  
  /// 获取星曜组合
  List<StarCombination> getStarCombinations(List<StarCombination> allCombinations) {
    List<String> starNames = stars.map((star) => star.name).toList();
    return allCombinations.where((combo) => combo.matches(starNames)).toList();
  }
}
