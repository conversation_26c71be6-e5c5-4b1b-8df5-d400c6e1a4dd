// lib/services/auth_service.dart
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import 'api_service.dart';

class AuthService extends ChangeNotifier {
  User? _currentUser;
  final ApiService _apiService = ApiService();
  
  User? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null;
  
  // 初始化，检查本地存储的用户信息
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('current_user');
      final token = prefs.getString('auth_token');
      
      if (userJson != null && token != null) {
        // 恢复用户信息
        _currentUser = User.fromJson({
          'userId': prefs.getString('user_id') ?? '',
          'username': prefs.getString('username') ?? '',
          'email': prefs.getString('email') ?? '',
          'createdAt': prefs.getString('created_at') ?? DateTime.now().toIso8601String(),
          'subscription': {
            'type': prefs.getString('subscription_type') ?? 'free',
            'expiresAt': prefs.getString('subscription_expires'),
          }
        });
        
        _apiService.setAuthToken(token);
      }
    } catch (e) {
      print('初始化认证服务失败: $e');
    }
  }
  
  // 用户注册
  Future<bool> register(String email, String password, String username) async {
    try {
      final user = await _apiService.register(email, password, username);
      
      if (user != null) {
        _currentUser = user;
        await _saveUserToLocal(user);
        return true;
      }
      
      // 如果API注册失败，创建本地用户（用于演示）
      final localUser = User(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        username: username,
        email: email,
        createdAt: DateTime.now(),
        subscription: Subscription(type: 'free'),
      );
      
      _currentUser = localUser;
      await _saveUserToLocal(localUser);
      notifyListeners();
      return true;
    } catch (e) {
      print('注册失败: $e');
      return false;
    }
  }
  
  // 用户登录
  Future<bool> login(String email, String password) async {
    try {
      final user = await _apiService.login(email, password);
      
      if (user != null) {
        _currentUser = user;
        await _saveUserToLocal(user);
        return true;
      }
      
      // 如果API登录失败，检查本地用户（用于演示）
      final prefs = await SharedPreferences.getInstance();
      final savedEmail = prefs.getString('email');
      
      if (savedEmail == email) {
        // 模拟登录成功
        final localUser = User(
          id: prefs.getString('user_id') ?? '1',
          username: prefs.getString('username') ?? 'Demo User',
          email: email,
          createdAt: DateTime.now(),
          subscription: Subscription(type: 'free'),
        );
        
        _currentUser = localUser;
        await _saveUserToLocal(localUser);
        notifyListeners();
        return true;
      }
      
      return false;
    } catch (e) {
      print('登录失败: $e');
      return false;
    }
  }
  
  // 用户登出
  Future<void> logout() async {
    try {
      _currentUser = null;
      notifyListeners();
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user');
      await prefs.remove('auth_token');
      await prefs.remove('user_id');
      await prefs.remove('username');
      await prefs.remove('email');
      await prefs.remove('created_at');
      await prefs.remove('subscription_type');
      await prefs.remove('subscription_expires');
    } catch (e) {
      print('登出失败: $e');
    }
  }
  
  // 保存用户信息到本地
  Future<void> _saveUserToLocal(User user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setString('user_id', user.id);
      await prefs.setString('username', user.username);
      await prefs.setString('email', user.email);
      await prefs.setString('created_at', user.createdAt.toIso8601String());
      await prefs.setString('subscription_type', user.subscription.type);
      
      if (user.subscription.expiresAt != null) {
        await prefs.setString('subscription_expires', 
            user.subscription.expiresAt!.toIso8601String());
      }
      
      // 保存一个简单的token（实际应用中应该从服务器获取）
      await prefs.setString('auth_token', 'demo_token_${user.id}');
    } catch (e) {
      print('保存用户信息失败: $e');
    }
  }
  
  // 更新用户订阅状态
  Future<void> updateSubscription(Subscription subscription) async {
    if (_currentUser != null) {
      _currentUser = User(
        id: _currentUser!.id,
        username: _currentUser!.username,
        email: _currentUser!.email,
        createdAt: _currentUser!.createdAt,
        lastLogin: _currentUser!.lastLogin,
        subscription: subscription,
      );
      
      await _saveUserToLocal(_currentUser!);
    }
  }
}
