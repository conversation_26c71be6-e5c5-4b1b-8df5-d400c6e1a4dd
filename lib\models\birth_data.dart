// lib/models/birth_data.dart
import 'location.dart';

class BirthData {
  final String name;
  final String gender; // 'male' or 'female'
  final String calendar; // 'solar' or 'lunar'
  final int year;
  final int month;
  final int day;
  final int hour; // 0-11 对应子时到亥时
  final int minute; // 分钟
  final bool isLeapMonth;
  final Location? location;

  BirthData({
    this.name = '',
    required this.gender,
    required this.calendar,
    required this.year,
    required this.month,
    required this.day,
    required this.hour,
    this.minute = 0,
    this.isLeapMonth = false,
    this.location,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'gender': gender,
      'calendar': calendar,
      'year': year,
      'month': month,
      'day': day,
      'hour': hour,
      'minute': minute,
      'isLeapMonth': isLeapMonth,
      'location': location?.toJson(),
    };
  }

  Map<String, dynamic> toMap() => toJson();

  factory BirthData.fromJson(Map<String, dynamic> json) {
    return BirthData(
      name: json['name'] as String? ?? '',
      gender: json['gender'] as String,
      calendar: json['calendar'] as String,
      year: json['year'] as int,
      month: json['month'] as int,
      day: json['day'] as int,
      hour: json['hour'] as int,
      minute: json['minute'] as int? ?? 0,
      isLeapMonth: json['isLeapMonth'] as bool? ?? false,
      location: json['location'] != null ? Location.fromJson(json['location'] as Map<String, dynamic>) : null,
    );
  }
}

