// lib/models/user.dart
class User {
  final String id;
  final String username;
  final String email;
  final DateTime createdAt;
  final DateTime? lastLogin;
  final Subscription subscription;
  
  User({
    required this.id,
    required this.username,
    required this.email,
    required this.createdAt,
    this.lastLogin,
    required this.subscription,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'userId': id,
      'username': username,
      'email': email,
      'createdAt': createdAt.toIso8601String(),
      'lastLogin': lastLogin?.toIso8601String(),
      'subscription': subscription.toJson(),
    };
  }
  
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['userId'] as String,
      username: json['username'] as String,
      email: json['email'] as String,
      createdAt: DateTime.parse(json['createdAt']),
      lastLogin: json['lastLogin'] != null 
          ? DateTime.parse(json['lastLogin']) 
          : null,
      subscription: Subscription.fromJson(json['subscription']),
    );
  }
}

class Subscription {
  final String type; // 'free', 'premium', 'professional'
  final DateTime? expiresAt;
  
  Subscription({
    required this.type,
    this.expiresAt,
  });
  
  bool get isActive {
    if (type == 'free') return true;
    if (expiresAt == null) return false;
    return DateTime.now().isBefore(expiresAt!);
  }
  
  bool get isPremium => type == 'premium' && isActive;
  bool get isProfessional => type == 'professional' && isActive;
  
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'expiresAt': expiresAt?.toIso8601String(),
    };
  }
  
  factory Subscription.fromJson(Map<String, dynamic> json) {
    return Subscription(
      type: json['type'] as String,
      expiresAt: json['expiresAt'] != null 
          ? DateTime.parse(json['expiresAt']) 
          : null,
    );
  }
}
