// lib/services/chart_calculator.dart
import '../models/birth_data.dart';
import '../models/chart_data.dart';
import '../models/palace.dart';
import '../utils/calendar_converter.dart';
import 'paipan_service.dart';

class ChartCalculator {
  // 紫微斗数排盘核心算法
  ChartData calculateChart(BirthData birthData) {
    // 使用新的排盘服务
    PaipanService paipanService = PaipanService();

    // 计算完整的排盘信息
    Map<String, dynamic> paipanResult = paipanService.getInfo(
      birthData.gender == 'male' ? 0 : 1,
      birthData.year,
      birthData.month,
      birthData.day,
      birthData.hour,
      birthData.minute,
      0, // 秒数
    );
    // 1. 如果是农历，转换为公历
    final solarDate = birthData.calendar == 'lunar'
        ? CalendarConverter.lunarToSolar(
            birthData.year,
            birthData.month,
            birthData.day,
            birthData.isLeapMonth,
          )
        : DateTime(birthData.year, birthData.month, birthData.day);

    // 2. 创建完整的出生时间（包含时分）
    final fullBirthTime = DateTime(
      solarDate.year,
      solarDate.month,
      solarDate.day,
      birthData.hour,
      birthData.minute,
    );

    // 3. 计算时辰索引（考虑真太阳时，如果有地理位置信息）
    int hourIndex;
    if (birthData.location != null) {
      hourIndex = CalendarConverter.getHourIndexByApparentTime(
        fullBirthTime,
        birthData.location!.longitude
      );
    } else {
      hourIndex = CalendarConverter.getHourIndexByMeanTime(fullBirthTime);
    }

    // 4. 计算命宫（使用精确的时辰索引）
    final ascendantIndex = _calculateAscendant(
      solarDate.month,
      solarDate.day,
      hourIndex,
    );

    // 5. 计算身宫
    final bodyPalaceIndex = (14 - ascendantIndex) % 12;

    // 6. 安星入宫
    final palaces = _placePlanets(ascendantIndex, solarDate.year, fullBirthTime, hourIndex);

    // 7. 创建命盘数据
    return ChartData(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: birthData.name,
      gender: birthData.gender,
      birthData: birthData,
      ascendant: ascendantIndex,
      bodyPalace: bodyPalaceIndex,
      palaces: palaces,
      createdAt: DateTime.now(),
      paipanData: paipanResult, // 添加排盘数据
    );
  }

  int _calculateAscendant(int month, int day, int hourIndex) {
    // 实现命宫计算公式
    // 月 + 时辰索引，然后取余12
    // 注意：这里的hourIndex是0-11的时辰索引，不是小时数
    return (month + hourIndex) % 12;
  }

  List<Palace> _placePlanets(int ascendantIndex, int year, DateTime birthTime, int hourIndex) {
    // 实现紫微星系安星算法
    // 这里是简化版，实际算法更复杂
    
    // 1. 创建12宫位，包含天干地支信息
    final palaces = List.generate(12, (index) {
      final palaceIndex = (ascendantIndex + index) % 12;

      // 获取宫位的地支
      String earthBranch = _getEarthBranch(palaceIndex);

      // 计算宫位的天干（基于年干和宫位地支）
      String heavenlyStem = _calculatePalaceHeavenlyStem(year, palaceIndex);

      return Palace(
        index: palaceIndex,
        name: _getPalaceName(index),
        earthBranch: earthBranch,
        mainStars: [],
        minorStars: [],
        transformStars: [],
      );
    });

    // 2. 安紫微星系
    _placeMainStars(palaces, year, birthTime);

    // 3. 安辅星
    _placeMinorStars(palaces, year, birthTime, hourIndex);

    // 4. 安化星
    _placeTransformStars(palaces, year, birthTime);

    return palaces;
  }
  
  String _getPalaceName(int relativeIndex) {
    // 根据相对位置返回宫位名称
    const palaceNames = [
      '命宫', '兄弟', '夫妻', '子女',
      '财帛', '疾厄', '迁移', '交友',
      '官禄', '田宅', '福德', '父母'
    ];
    return palaceNames[relativeIndex];
  }
  
  String _getEarthBranch(int absoluteIndex) {
    // 返回地支名称
    const earthBranches = [
      '寅', '卯', '辰', '巳', '午', '未',
      '申', '酉', '戌', '亥', '子', '丑'
    ];
    return earthBranches[absoluteIndex];
  }

  String _calculatePalaceHeavenlyStem(int year, int palaceIndex) {
    // 计算宫位天干
    // 基于年干和宫位地支的关系
    String yearStemBranch = CalendarConverter.getYearStemBranch(year);
    String yearStem = yearStemBranch[0];

    // 简化算法：根据年干推算各宫位天干
    const heavenlyStems = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    int yearStemIndex = heavenlyStems.indexOf(yearStem);

    // 宫位天干 = (年干索引 + 宫位索引) % 10
    int palaceStemIndex = (yearStemIndex + palaceIndex) % 10;
    return heavenlyStems[palaceStemIndex];
  }
  
  void _placeMainStars(List<Palace> palaces, int year, DateTime birthTime) {
    // 计算紫微星系位置
    // 这里使用简化算法，实际更复杂
    
    // 1. 计算紫微星位置
    final ziWeiPosition = _calculateZiWeiPosition(year);
    palaces[ziWeiPosition].mainStars.add('紫微');
    
    // 2. 计算其他主星位置
    // 天机星在紫微之后一宫
    palaces[(ziWeiPosition + 1) % 12].mainStars.add('天机');
    
    // 太阳星在紫微之后二宫
    palaces[(ziWeiPosition + 2) % 12].mainStars.add('太阳');
    
    // 武曲星在紫微之后三宫
    palaces[(ziWeiPosition + 3) % 12].mainStars.add('武曲');
    
    // 天同星在紫微之后四宫
    palaces[(ziWeiPosition + 4) % 12].mainStars.add('天同');
    
    // 廉贞星在紫微之后五宫
    palaces[(ziWeiPosition + 5) % 12].mainStars.add('廉贞');
    
    // 计算天府星位置(与紫微相对)
    final tianFuPosition = (ziWeiPosition + 6) % 12;
    palaces[tianFuPosition].mainStars.add('天府');
    
    // 太阴星在天府之后一宫
    palaces[(tianFuPosition + 1) % 12].mainStars.add('太阴');
    
    // 贪狼星在天府之后二宫
    palaces[(tianFuPosition + 2) % 12].mainStars.add('贪狼');
    
    // 巨门星在天府之后三宫
    palaces[(tianFuPosition + 3) % 12].mainStars.add('巨门');
    
    // 天相星在天府之后四宫
    palaces[(tianFuPosition + 4) % 12].mainStars.add('天相');
    
    // 天梁星在天府之后五宫
    palaces[(tianFuPosition + 5) % 12].mainStars.add('天梁');
    
    // 七杀星在天府之后六宫
    palaces[(tianFuPosition + 6) % 12].mainStars.add('七杀');
    
    // 破军星在天府之后七宫
    palaces[(tianFuPosition + 7) % 12].mainStars.add('破军');
  }
  
  int _calculateZiWeiPosition(int year) {
    // 计算紫微星位置
    // 简化版算法，实际更复杂
    final lunarYear = year % 10; // 简化，实际需要转农历年
    return (lunarYear * 2) % 12;
  }
  
  void _placeMinorStars(List<Palace> palaces, int year, DateTime birthTime, int hourIndex) {
    // 安辅星算法
    // 简化版，实际更复杂

    // 左辅右弼
    final leftAssistant = year % 12;
    final rightAssistant = (12 - leftAssistant) % 12;
    palaces[leftAssistant].minorStars.add('左辅');
    palaces[rightAssistant].minorStars.add('右弼');

    // 文昌文曲
    final wenChang = (year + 4) % 12;
    final wenQu = (year + 10) % 12;
    palaces[wenChang].minorStars.add('文昌');
    palaces[wenQu].minorStars.add('文曲');

    // 天魁天钺
    final tianKui = (year + 1) % 12;
    final tianYue = (year + 7) % 12;
    palaces[tianKui].minorStars.add('天魁');
    palaces[tianYue].minorStars.add('天钺');

    // 禄存
    final luCun = (year + 2) % 12;
    palaces[luCun].minorStars.add('禄存');

    // 擎羊陀罗
    final qingYang = (luCun + 1) % 12;
    final tuoLuo = (luCun + 11) % 12;
    palaces[qingYang].minorStars.add('擎羊');
    palaces[tuoLuo].minorStars.add('陀罗');
  }

  void _placeTransformStars(List<Palace> palaces, int year, DateTime birthTime) {
    // 安化星算法
    // 简化版，实际更复杂

    // 根据年干安四化星
    final yearStem = year % 10;

    // 简化的四化表
    final transformMap = {
      0: {'禄': 2, '权': 5, '科': 8, '忌': 11}, // 甲年
      1: {'禄': 3, '权': 6, '科': 9, '忌': 0},  // 乙年
      2: {'禄': 4, '权': 7, '科': 10, '忌': 1}, // 丙年
      3: {'禄': 5, '权': 8, '科': 11, '忌': 2}, // 丁年
      4: {'禄': 6, '权': 9, '科': 0, '忌': 3},  // 戊年
      5: {'禄': 7, '权': 10, '科': 1, '忌': 4}, // 己年
      6: {'禄': 8, '权': 11, '科': 2, '忌': 5}, // 庚年
      7: {'禄': 9, '权': 0, '科': 3, '忌': 6},  // 辛年
      8: {'禄': 10, '权': 1, '科': 4, '忌': 7}, // 壬年
      9: {'禄': 11, '权': 2, '科': 5, '忌': 8}, // 癸年
    };

    final transforms = transformMap[yearStem] ?? transformMap[0]!;

    transforms.forEach((transformType, palaceIndex) {
      palaces[palaceIndex].transformStars.add('化$transformType');
    });
  }
}
