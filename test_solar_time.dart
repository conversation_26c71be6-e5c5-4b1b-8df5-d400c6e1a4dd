// 测试真假太阳时转换和天干地支计算功能
import 'lib/utils/calendar_converter.dart';

void main() {
  print('=== 真假太阳时转换和天干地支计算功能测试 ===\n');
  
  // 测试日期：2024年1月1日 12:00:00
  DateTime testDate = DateTime(2024, 1, 1, 12, 0, 0);
  double longitude = 116.4074; // 北京经度
  
  print('测试日期：${testDate.year}年${testDate.month}月${testDate.day}日 ${testDate.hour}:${testDate.minute}:${testDate.second}');
  print('测试地点：东经 ${longitude}度\n');
  
  // 1. 测试真假太阳时转换
  print('=== 1. 真假太阳时转换测试 ===');
  DateTime apparentTime = CalendarConverter.meanToApparentSolarTime(testDate, longitude);
  DateTime backToMeanTime = CalendarConverter.apparentToMeanSolarTime(apparentTime, longitude);
  
  print('平太阳时：${testDate.hour}:${testDate.minute}:${testDate.second}');
  print('真太阳时：${apparentTime.hour}:${apparentTime.minute}:${apparentTime.second}');
  print('转换回平太阳时：${backToMeanTime.hour}:${backToMeanTime.minute}:${backToMeanTime.second}');
  print('转换精度验证：${testDate == backToMeanTime ? "通过" : "失败"}\n');
  
  // 2. 测试时辰计算
  print('=== 2. 时辰计算测试 ===');
  int hourIndexMean = CalendarConverter.getHourIndexByMeanTime(testDate);
  int hourIndexApparent = CalendarConverter.getHourIndexByApparentTime(testDate, longitude);
  
  print('平太阳时时辰：${CalendarConverter.getHourName(hourIndexMean)}时 (${CalendarConverter.getModernHourRange(hourIndexMean)})');
  print('真太阳时时辰：${CalendarConverter.getHourName(hourIndexApparent)}时 (${CalendarConverter.getModernHourRange(hourIndexApparent)})\n');
  
  // 3. 测试阳历阴历转换
  print('=== 3. 阳历阴历转换测试 ===');
  Map<String, dynamic> lunarInfo = CalendarConverter.solarToLunar(testDate);
  DateTime backToSolar = CalendarConverter.lunarToSolar(
    lunarInfo['year'], 
    lunarInfo['month'], 
    lunarInfo['day'], 
    lunarInfo['isLeapMonth']
  );
  
  print('公历：${testDate.year}年${testDate.month}月${testDate.day}日');
  print('农历：${lunarInfo['year']}年${CalendarConverter.getLunarMonthName(lunarInfo['month'], lunarInfo['isLeapMonth'])}${CalendarConverter.getLunarDayName(lunarInfo['day'])}');
  print('转换回公历：${backToSolar.year}年${backToSolar.month}月${backToSolar.day}日');
  print('转换精度验证：${testDate.year == backToSolar.year && testDate.month == backToSolar.month && testDate.day == backToSolar.day ? "通过" : "失败"}\n');
  
  // 4. 测试完整的四柱干支计算
  print('=== 4. 四柱干支计算测试 ===');
  Map<String, String> fourPillars = CalendarConverter.getFourPillars(testDate, longitude: longitude);
  Map<String, String> fourPillarsMean = CalendarConverter.getFourPillars(testDate);
  
  print('使用真太阳时的四柱干支：');
  print('年柱：${fourPillars['year']}');
  print('月柱：${fourPillars['month']}');
  print('日柱：${fourPillars['day']}');
  print('时柱：${fourPillars['hour']}\n');
  
  print('使用平太阳时的四柱干支：');
  print('年柱：${fourPillarsMean['year']}');
  print('月柱：${fourPillarsMean['month']}');
  print('日柱：${fourPillarsMean['day']}');
  print('时柱：${fourPillarsMean['hour']}\n');
  
  // 5. 测试时辰详细信息
  print('=== 5. 时辰详细信息测试 ===');
  Map<String, dynamic> hourDetails = CalendarConverter.getHourDetails(hourIndexApparent);
  print('当前时辰详细信息：');
  print('名称：${hourDetails['name']}');
  print('时间范围：${hourDetails['range']}');
  print('五行：${hourDetails['element']}');
  print('方位：${hourDetails['direction']}');
  print('季节：${hourDetails['season']}');
  print('描述：${hourDetails['description']}');
  print('对应脏腑：${hourDetails['organs'].join('、')}');
  print('养生建议：${hourDetails['activity']}\n');
  
  // 6. 测试不同时间的时辰变化
  print('=== 6. 一天中不同时间的时辰变化测试 ===');
  for (int hour = 0; hour < 24; hour += 2) {
    DateTime timeTest = DateTime(2024, 1, 1, hour, 0, 0);
    int hourIdx = CalendarConverter.getHourIndexByMeanTime(timeTest);
    String hourName = CalendarConverter.getHourName(hourIdx);
    String hourRange = CalendarConverter.getModernHourRange(hourIdx);
    String hourStemBranch = CalendarConverter.getHourStemBranch(timeTest, hourIdx);
    
    print('${hour.toString().padLeft(2, '0')}:00 -> ${hourName}时 (${hourRange}) 干支：${hourStemBranch}');
  }
  
  print('\n=== 测试完成 ===');
}
