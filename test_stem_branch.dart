// 测试干支计算功能
import 'lib/utils/stem_branch_calculator.dart';
import 'lib/utils/perpetual_calendar.dart';
import 'lib/services/paipan_service.dart';
import 'lib/models/advanced_settings.dart';

void main() {
  print('=== 2025年干支计算对比测试 ===');
  
  // 测试时间：2025年6月15日 12:00
  DateTime testTime = DateTime(2025, 6, 15, 12, 0);
  
  print('测试时间：${testTime.toString()}');
  print('');
  
  // 1. 测试StemBranchCalculator（不使用立春起年）
  print('1. StemBranchCalculator结果（不使用立春起年）：');
  try {
    AdvancedSettings settingsNoLichun = AdvancedSettings(
      yearPillarStartPoint: YearPillarStartPoint.lunarNewYear,
      ziHourMode: ZiHourMode.late,
      leapMonthBoundary: LeapMonthBoundary.middle,
      useApparentSolarTime: false,
      showSolarTerms: false,
    );

    String yearStemBranch = StemBranchCalculator.getYearStemBranch(2025, birthTime: testTime, settings: settingsNoLichun);
    print('   年干支：$yearStemBranch');

    Map<String, String> fourPillars = StemBranchCalculator.getFourPillarsWithSettings(
      testTime, 5, 6, // 假设农历五月，午时
      settings: settingsNoLichun,
    );
    print('   完整四柱：${fourPillars}');
  } catch (e) {
    print('   计算失败：$e');
  }

  print('');

  // 1.5. 测试StemBranchCalculator（使用立春起年）
  print('1.5. StemBranchCalculator结果（使用立春起年）：');
  try {
    AdvancedSettings settingsWithLichun = AdvancedSettings(
      yearPillarStartPoint: YearPillarStartPoint.lichun,
      ziHourMode: ZiHourMode.late,
      leapMonthBoundary: LeapMonthBoundary.middle,
      useApparentSolarTime: false,
      showSolarTerms: false,
    );

    String yearStemBranch = StemBranchCalculator.getYearStemBranch(2025, birthTime: testTime, settings: settingsWithLichun);
    print('   年干支：$yearStemBranch');

    Map<String, String> fourPillars = StemBranchCalculator.getFourPillarsWithSettings(
      testTime, 5, 6, // 假设农历五月，午时
      settings: settingsWithLichun,
    );
    print('   完整四柱：${fourPillars}');
  } catch (e) {
    print('   计算失败：$e');
  }
  
  print('');
  
  // 2. 测试PerpetualCalendar
  print('2. PerpetualCalendar结果：');
  try {
    Map<String, dynamic> dayInfo = PerpetualCalendar.getDayInfo(testTime);
    Map<String, dynamic> stemBranch = dayInfo['stemBranch'];
    print('   年干支：${stemBranch['year']}');
    print('   月干支：${stemBranch['month']}');
    print('   日干支：${stemBranch['day']}');
  } catch (e) {
    print('   计算失败：$e');
  }
  
  print('');
  
  // 3. 测试PaipanService
  print('3. PaipanService结果：');
  try {
    PaipanService paipanService = PaipanService();
    Map<String, dynamic> ganZhiResult = paipanService.getGanZhi(2025, 6, 15, 12);
    
    if (ganZhiResult.isNotEmpty) {
      List<int> tg = ganZhiResult['tg'];
      List<int> dz = ganZhiResult['dz'];
      
      String yearStemBranch = paipanService.ctg[tg[0]] + paipanService.cdz[dz[0]];
      String monthStemBranch = paipanService.ctg[tg[1]] + paipanService.cdz[dz[1]];
      String dayStemBranch = paipanService.ctg[tg[2]] + paipanService.cdz[dz[2]];
      String hourStemBranch = paipanService.ctg[tg[3]] + paipanService.cdz[dz[3]];
      
      print('   年干支：$yearStemBranch');
      print('   月干支：$monthStemBranch');
      print('   日干支：$dayStemBranch');
      print('   时干支：$hourStemBranch');
      
      // 详细计算过程
      print('   年干支计算详情：');
      print('     天干索引：${tg[0]} → ${paipanService.ctg[tg[0]]}');
      print('     地支索引：${dz[0]} → ${paipanService.cdz[dz[0]]}');
    } else {
      print('   计算失败：返回空结果');
    }
  } catch (e) {
    print('   计算失败：$e');
  }
  
  print('');
  print('=== 其他年份测试 ===');
  
  // 测试其他年份
  List<int> testYears = [2020, 2021, 2022, 2023, 2024, 2025, 2026];
  
  for (int year in testYears) {
    print('$year年：');
    
    // StemBranchCalculator
    String stemCalc = StemBranchCalculator.getYearStemBranch(year);
    
    // PaipanService
    PaipanService paipanService = PaipanService();
    Map<String, dynamic> ganZhiResult = paipanService.getGanZhi(year, 6, 15, 12);
    String paipanCalc = '';
    if (ganZhiResult.isNotEmpty) {
      List<int> tg = ganZhiResult['tg'];
      List<int> dz = ganZhiResult['dz'];
      paipanCalc = paipanService.ctg[tg[0]] + paipanService.cdz[dz[0]];
    }
    
    print('  StemBranchCalculator: $stemCalc');
    print('  PaipanService: $paipanCalc');
    print('  一致性: ${stemCalc == paipanCalc ? '✓' : '✗'}');
    print('');
  }
}
