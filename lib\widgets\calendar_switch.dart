// lib/widgets/calendar_switch.dart
import 'package:flutter/material.dart';

class CalendarSwitch extends StatelessWidget {
  final bool isLunar;
  final ValueChanged<bool> onChanged;
  
  CalendarSwitch({
    required this.isLunar,
    required this.onChanged,
  });
  
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '公历',
          style: TextStyle(
            color: !isLunar ? Theme.of(context).primaryColor : Colors.grey,
            fontWeight: !isLunar ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Switch(
          value: isLunar,
          onChanged: onChanged,
          activeColor: Theme.of(context).primaryColor,
        ),
        Text(
          '农历',
          style: TextStyle(
            color: isLunar ? Theme.of(context).primaryColor : Colors.grey,
            fontWeight: isLunar ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ],
    );
  }
}
