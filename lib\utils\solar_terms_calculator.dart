// lib/utils/solar_terms_calculator.dart
import '../services/paipan_service.dart';
import 'solar_time_converter.dart';

/// 二十四节气计算器
/// 重构后统一使用 PaipanService 进行精确的节气计算
/// 基于 paipan.php 的完整实现，确保计算精度和一致性
class SolarTermsCalculator {
  static final PaipanService _paipanService = PaipanService();

  // 二十四节气名称
  static const List<String> solarTermNames = [
    '立春', '雨水', '惊蛰', '春分', '清明', '谷雨',
    '立夏', '小满', '芒种', '夏至', '小暑', '大暑',
    '立秋', '处暑', '白露', '秋分', '寒露', '霜降',
    '立冬', '小雪', '大雪', '冬至', '小寒', '大寒'
  ];

  // 节气对应的太阳黄经度数
  static const List<double> solarTermLongitudes = [
    315, 330, 345, 0, 15, 30,
    45, 60, 75, 90, 105, 120,
    135, 150, 165, 180, 195, 210,
    225, 240, 255, 270, 285, 300
  ];
  
  /// 计算指定年份的所有节气时间
  /// [year] 年份
  /// [longitude] 地理经度（可选，用于地方时修正）
  /// 返回包含所有节气信息的列表
  static List<Map<String, dynamic>> calculateYearSolarTerms(int year, {double? longitude}) {
    List<Map<String, dynamic>> solarTerms = [];

    // 使用 PaipanService 获取精确的24节气数据
    List<List<int>> jieQiList = _paipanService.get24JieQi(year);

    for (int i = 0; i < jieQiList.length && i < solarTermNames.length; i++) {
      List<int> jieQiTime = jieQiList[i];

      DateTime termTime;
      if (jieQiTime.length >= 6) {
        termTime = DateTime(
          jieQiTime[0], jieQiTime[1], jieQiTime[2],
          jieQiTime[3], jieQiTime[4], jieQiTime[5],
        );
      } else {
        // 如果数据不完整，使用默认时间
        termTime = DateTime(year, 1, 1, 12, 0, 0);
      }

      // 如果提供了地理经度，进行地方时修正
      if (longitude != null) {
        termTime = SolarTimeConverter.meanToApparentSolarTime(termTime, longitude);
      }

      solarTerms.add({
        'index': i,
        'name': solarTermNames[i],
        'longitude': solarTermLongitudes[i],
        'time': termTime,
        'year': year,
        'season': _getSeason(i),
        'description': _getDescription(i),
      });
    }

    return solarTerms;
  }
  
  /// 计算指定节气的精确时间
  /// [year] 年份
  /// [termIndex] 节气索引 (0-23)
  /// [longitude] 地理经度（可选）
  /// 返回节气的精确时间
  static DateTime calculateSolarTerm(int year, int termIndex, {double? longitude}) {
    if (termIndex < 0 || termIndex >= solarTermNames.length) {
      throw ArgumentError('节气索引超出范围: $termIndex');
    }

    // 使用 PaipanService 获取精确的24节气数据
    List<List<int>> jieQiList = _paipanService.get24JieQi(year);

    if (termIndex < jieQiList.length) {
      List<int> jieQiTime = jieQiList[termIndex];

      DateTime termTime;
      if (jieQiTime.length >= 6) {
        termTime = DateTime(
          jieQiTime[0], jieQiTime[1], jieQiTime[2],
          jieQiTime[3], jieQiTime[4], jieQiTime[5],
        );
      } else {
        // 如果数据不完整，使用默认时间
        termTime = DateTime(year, 1, 1, 12, 0, 0);
      }

      // 如果提供了地理经度，进行地方时修正
      if (longitude != null) {
        termTime = SolarTimeConverter.meanToApparentSolarTime(termTime, longitude);
      }

      return termTime;
    }

    // 如果获取失败，返回默认时间
    return DateTime(year, 1, 1, 12, 0, 0);
  }
  
  /// 获取指定日期最近的节气
  /// [date] 日期
  /// [longitude] 地理经度（可选）
  /// 返回最近的节气信息
  static Map<String, dynamic> getNearestSolarTerm(DateTime date, {double? longitude}) {
    int year = date.year;
    List<Map<String, dynamic>> yearTerms = calculateYearSolarTerms(year, longitude: longitude);
    
    // 如果日期在年初，也检查上一年的节气
    if (date.month <= 2) {
      List<Map<String, dynamic>> prevYearTerms = calculateYearSolarTerms(year - 1, longitude: longitude);
      yearTerms = [...prevYearTerms, ...yearTerms];
    }
    
    // 如果日期在年末，也检查下一年的节气
    if (date.month >= 11) {
      List<Map<String, dynamic>> nextYearTerms = calculateYearSolarTerms(year + 1, longitude: longitude);
      yearTerms = [...yearTerms, ...nextYearTerms];
    }
    
    // 找到最近的节气
    Map<String, dynamic>? nearestTerm;
    Duration minDifference = Duration(days: 365);

    for (var term in yearTerms) {
      try {
        DateTime termTime = term['time'] as DateTime;
        Duration difference = termTime.difference(date).abs();
        if (difference < minDifference) {
          minDifference = difference;
          nearestTerm = term;
        }
      } catch (e) {
        print('处理节气数据时出错: $e, 节气数据: $term');
        continue;
      }
    }

    // 如果没有找到合适的节气，返回默认值
    if (nearestTerm == null && yearTerms.isNotEmpty) {
      nearestTerm = yearTerms.first;
    }

    return nearestTerm ?? {
      'index': 0,
      'name': '立春',
      'longitude': 315.0,
      'time': DateTime(date.year, 2, 4, 12, 0, 0),
      'year': date.year,
      'season': '春季',
      'description': '春季开始，万物复苏',
    };
  }
  
  /// 获取指定日期之前的最近节气
  /// [date] 日期
  /// [longitude] 地理经度（可选）
  /// 返回之前最近的节气信息
  static Map<String, dynamic> getPreviousSolarTerm(DateTime date, {double? longitude}) {
    int year = date.year;
    List<Map<String, dynamic>> yearTerms = calculateYearSolarTerms(year, longitude: longitude);
    
    // 添加上一年的节气以处理年初的情况
    List<Map<String, dynamic>> prevYearTerms = calculateYearSolarTerms(year - 1, longitude: longitude);
    yearTerms = [...prevYearTerms, ...yearTerms];
    
    // 找到指定日期之前的最近节气
    Map<String, dynamic>? previousTerm;
    
    for (var term in yearTerms.reversed) {
      if ((term['time'] as DateTime).isBefore(date)) {
        previousTerm = term;
        break;
      }
    }
    
    return previousTerm ?? yearTerms.last;
  }
  
  /// 获取指定日期之后的最近节气
  /// [date] 日期
  /// [longitude] 地理经度（可选）
  /// 返回之后最近的节气信息
  static Map<String, dynamic> getNextSolarTerm(DateTime date, {double? longitude}) {
    int year = date.year;
    List<Map<String, dynamic>> yearTerms = calculateYearSolarTerms(year, longitude: longitude);
    
    // 添加下一年的节气以处理年末的情况
    List<Map<String, dynamic>> nextYearTerms = calculateYearSolarTerms(year + 1, longitude: longitude);
    yearTerms = [...yearTerms, ...nextYearTerms];
    
    // 找到指定日期之后的最近节气
    Map<String, dynamic>? nextTerm;
    
    for (var term in yearTerms) {
      if ((term['time'] as DateTime).isAfter(date)) {
        nextTerm = term;
        break;
      }
    }
    
    return nextTerm ?? yearTerms.first;
  }
  
  /// 判断指定日期是否为节气日
  /// [date] 日期
  /// [tolerance] 容差（小时）
  /// [longitude] 地理经度（可选）
  /// 返回是否为节气日
  static bool isSolarTermDay(DateTime date, {int tolerance = 12, double? longitude}) {
    Map<String, dynamic> nearestTerm = getNearestSolarTerm(date, longitude: longitude);
    DateTime termTime = nearestTerm['time'];
    
    Duration difference = termTime.difference(date).abs();
    return difference.inHours <= tolerance;
  }
  
  // === 保留的辅助方法，用于向后兼容 ===
  
  /// 获取节气所属季节
  static String _getSeason(int termIndex) {
    if (termIndex >= 0 && termIndex <= 5) return '春季';
    if (termIndex >= 6 && termIndex <= 11) return '夏季';
    if (termIndex >= 12 && termIndex <= 17) return '秋季';
    return '冬季';
  }
  
  /// 获取节气描述
  static String _getDescription(int termIndex) {
    const descriptions = [
      '春季开始，万物复苏', '雨量增多，气温回升', '春雷响动，蛰虫苏醒', '昼夜等长，春分时节',
      '清明时节，万物清洁明净', '雨水充沛，谷物生长', '夏季开始，万物繁茂', '麦类作物籽粒饱满',
      '有芒作物成熟收割', '白昼最长，夏至时节', '天气炎热，小暑时节', '一年最热，大暑时节',
      '秋季开始，暑去凉来', '暑气消散，秋高气爽', '露水增多，天气转凉', '昼夜等长，秋分时节',
      '露水更凉，深秋时节', '霜降时节，天气更冷', '冬季开始，万物收藏', '开始降雪，天气寒冷',
      '雪量增大，天寒地冻', '白昼最短，冬至时节', '天气寒冷，小寒时节', '一年最冷，大寒时节'
    ];
    
    return descriptions[termIndex];
  }
}
