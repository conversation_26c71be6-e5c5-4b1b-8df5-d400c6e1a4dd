## 数据模型设计

### 用户模型(User)
```json
{
  "userId": "string",
  "username": "string",
  "email": "string",
  "password": "string(hashed)",
  "createdAt": "timestamp",
  "lastLogin": "timestamp",
  "subscription": {
    "type": "free|premium|professional",
    "expiresAt": "timestamp"
  }
}
```

### 命盘模型(Chart)
```json
{
  "chartId": "string",
  "userId": "string",
  "name": "string",
  "gender": "male|female",
  "birthData": {
    "calendar": "solar|lunar",
    "year": "number",
    "month": "number",
    "day": "number",
    "hour": "number",
    "minute": "number",
    "isLeapMonth": "boolean",
    "location": {
      "country": "string",
      "province": "string",
      "city": "string",
      "latitude": "number",
      "longitude": "number"
    }
  },
  "chartData": {
    "ascendant": "number", // 命宫地支索引
    "bodyPalace": "number", // 身宫地支索引
    "palaces": [
      {
        "index": "number",
        "name": "string", // 宫位名称
        "earthBranch": "string", // 地支
        "mainStars": ["string"], // 主星列表
        "minorStars": ["string"], // 辅星列表
        "transformStars": ["string"] // 化星列表
      }
    ]
  },
  "createdAt": "timestamp",
  "lastViewed": "timestamp",
  "notes": "string"
}
```

### 解读记录模型(Interpretation)
```json
{
  "interpretationId": "string",
  "chartId": "string",
  "userId": "string",
  "type": "basic|advanced|professional",
  "content": {
    "overview": "string",
    "personality": "string",
    "career": "string",
    "wealth": "string",
    "relationships": "string",
    "health": "string"
  },
  "aiGenerated": "boolean",
  "createdAt": "timestamp"
}
```