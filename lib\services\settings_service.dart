// lib/services/settings_service.dart
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/advanced_settings.dart';

/// 设置持久化服务
class SettingsService {
  static const String _advancedSettingsKey = 'advanced_settings';
  static const String _userPreferencesKey = 'user_preferences';
  
  static SettingsService? _instance;
  static SettingsService get instance => _instance ??= SettingsService._();
  
  SettingsService._();
  
  SharedPreferences? _prefs;
  
  /// 初始化服务
  Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
  }
  
  /// 确保服务已初始化
  Future<void> _ensureInitialized() async {
    if (_prefs == null) {
      await initialize();
    }
  }
  
  /// 保存高级设置
  Future<bool> saveAdvancedSettings(AdvancedSettings settings) async {
    await _ensureInitialized();
    
    try {
      String jsonString = jsonEncode(settings.toJson());
      bool result = await _prefs!.setString(_advancedSettingsKey, jsonString);
      
      if (result) {
        print('高级设置已保存: ${settings.summary}');
      }
      
      return result;
    } catch (e) {
      print('保存高级设置失败: $e');
      return false;
    }
  }
  
  /// 加载高级设置
  Future<AdvancedSettings> loadAdvancedSettings() async {
    await _ensureInitialized();
    
    try {
      String? jsonString = _prefs!.getString(_advancedSettingsKey);
      
      if (jsonString != null) {
        Map<String, dynamic> json = jsonDecode(jsonString);
        AdvancedSettings settings = AdvancedSettings.fromJson(json);
        print('高级设置已加载: ${settings.summary}');
        return settings;
      }
    } catch (e) {
      print('加载高级设置失败: $e');
    }
    
    // 返回默认设置
    AdvancedSettings defaultSettings = AdvancedSettings.defaultSettings();
    print('使用默认高级设置: ${defaultSettings.summary}');
    return defaultSettings;
  }
  
  /// 重置高级设置为默认值
  Future<bool> resetAdvancedSettings() async {
    await _ensureInitialized();
    
    try {
      bool result = await _prefs!.remove(_advancedSettingsKey);
      
      if (result) {
        print('高级设置已重置为默认值');
      }
      
      return result;
    } catch (e) {
      print('重置高级设置失败: $e');
      return false;
    }
  }
  
  /// 保存用户偏好设置
  Future<bool> saveUserPreference(String key, dynamic value) async {
    await _ensureInitialized();
    
    try {
      Map<String, dynamic> preferences = await _loadUserPreferences();
      preferences[key] = value;
      
      String jsonString = jsonEncode(preferences);
      bool result = await _prefs!.setString(_userPreferencesKey, jsonString);
      
      if (result) {
        print('用户偏好已保存: $key = $value');
      }
      
      return result;
    } catch (e) {
      print('保存用户偏好失败: $e');
      return false;
    }
  }
  
  /// 加载用户偏好设置
  Future<T?> loadUserPreference<T>(String key, {T? defaultValue}) async {
    await _ensureInitialized();
    
    try {
      Map<String, dynamic> preferences = await _loadUserPreferences();
      return preferences[key] as T? ?? defaultValue;
    } catch (e) {
      print('加载用户偏好失败: $e');
      return defaultValue;
    }
  }
  
  /// 删除用户偏好设置
  Future<bool> removeUserPreference(String key) async {
    await _ensureInitialized();
    
    try {
      Map<String, dynamic> preferences = await _loadUserPreferences();
      preferences.remove(key);
      
      String jsonString = jsonEncode(preferences);
      bool result = await _prefs!.setString(_userPreferencesKey, jsonString);
      
      if (result) {
        print('用户偏好已删除: $key');
      }
      
      return result;
    } catch (e) {
      print('删除用户偏好失败: $e');
      return false;
    }
  }
  
  /// 加载所有用户偏好设置
  Future<Map<String, dynamic>> _loadUserPreferences() async {
    try {
      String? jsonString = _prefs!.getString(_userPreferencesKey);
      
      if (jsonString != null) {
        return jsonDecode(jsonString) as Map<String, dynamic>;
      }
    } catch (e) {
      print('加载用户偏好失败: $e');
    }
    
    return {};
  }
  
  /// 清除所有设置
  Future<bool> clearAllSettings() async {
    await _ensureInitialized();
    
    try {
      bool result1 = await _prefs!.remove(_advancedSettingsKey);
      bool result2 = await _prefs!.remove(_userPreferencesKey);
      
      bool result = result1 && result2;
      
      if (result) {
        print('所有设置已清除');
      }
      
      return result;
    } catch (e) {
      print('清除设置失败: $e');
      return false;
    }
  }
  
  /// 导出设置
  Future<Map<String, dynamic>> exportSettings() async {
    await _ensureInitialized();
    
    try {
      AdvancedSettings advancedSettings = await loadAdvancedSettings();
      Map<String, dynamic> userPreferences = await _loadUserPreferences();
      
      return {
        'advancedSettings': advancedSettings.toJson(),
        'userPreferences': userPreferences,
        'exportTime': DateTime.now().toIso8601String(),
        'version': '1.0',
      };
    } catch (e) {
      print('导出设置失败: $e');
      return {};
    }
  }
  
  /// 导入设置
  Future<bool> importSettings(Map<String, dynamic> settingsData) async {
    await _ensureInitialized();
    
    try {
      // 导入高级设置
      if (settingsData.containsKey('advancedSettings')) {
        AdvancedSettings advancedSettings = AdvancedSettings.fromJson(
          settingsData['advancedSettings'] as Map<String, dynamic>
        );
        await saveAdvancedSettings(advancedSettings);
      }
      
      // 导入用户偏好
      if (settingsData.containsKey('userPreferences')) {
        Map<String, dynamic> userPreferences = settingsData['userPreferences'] as Map<String, dynamic>;
        String jsonString = jsonEncode(userPreferences);
        await _prefs!.setString(_userPreferencesKey, jsonString);
      }
      
      print('设置导入成功');
      return true;
    } catch (e) {
      print('导入设置失败: $e');
      return false;
    }
  }
  
  /// 获取设置统计信息
  Future<Map<String, dynamic>> getSettingsStats() async {
    await _ensureInitialized();
    
    try {
      AdvancedSettings advancedSettings = await loadAdvancedSettings();
      Map<String, dynamic> userPreferences = await _loadUserPreferences();
      
      return {
        'advancedSettingsCount': 5, // 高级设置项数量
        'userPreferencesCount': userPreferences.length,
        'isDefaultSettings': advancedSettings.isDefault,
        'lastModified': _prefs!.getString('${_advancedSettingsKey}_modified'),
        'settingsSize': _calculateSettingsSize(),
      };
    } catch (e) {
      print('获取设置统计失败: $e');
      return {};
    }
  }
  
  /// 计算设置占用的存储空间
  int _calculateSettingsSize() {
    try {
      String? advancedSettings = _prefs!.getString(_advancedSettingsKey);
      String? userPreferences = _prefs!.getString(_userPreferencesKey);
      
      int size = 0;
      if (advancedSettings != null) {
        size += advancedSettings.length;
      }
      if (userPreferences != null) {
        size += userPreferences.length;
      }
      
      return size;
    } catch (e) {
      return 0;
    }
  }
  
  /// 备份设置
  Future<bool> backupSettings() async {
    try {
      Map<String, dynamic> settings = await exportSettings();
      String backupKey = '${_advancedSettingsKey}_backup_${DateTime.now().millisecondsSinceEpoch}';
      
      String jsonString = jsonEncode(settings);
      bool result = await _prefs!.setString(backupKey, jsonString);
      
      if (result) {
        print('设置备份成功: $backupKey');
      }
      
      return result;
    } catch (e) {
      print('备份设置失败: $e');
      return false;
    }
  }
  
  /// 恢复设置备份
  Future<bool> restoreSettings(String backupKey) async {
    try {
      String? jsonString = _prefs!.getString(backupKey);
      
      if (jsonString != null) {
        Map<String, dynamic> settings = jsonDecode(jsonString);
        bool result = await importSettings(settings);
        
        if (result) {
          print('设置恢复成功: $backupKey');
        }
        
        return result;
      }
      
      return false;
    } catch (e) {
      print('恢复设置失败: $e');
      return false;
    }
  }
}
