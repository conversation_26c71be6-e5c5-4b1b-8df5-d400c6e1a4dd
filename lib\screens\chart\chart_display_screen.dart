// lib/screens/chart/chart_display_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/chart_data.dart';
import '../../models/palace.dart';
import '../../services/storage_service.dart';

class ChartDisplayScreen extends StatefulWidget {
  final ChartData chartData;
  
  ChartDisplayScreen({required this.chartData});
  
  @override
  _ChartDisplayScreenState createState() => _ChartDisplayScreenState();
}

class _ChartDisplayScreenState extends State<ChartDisplayScreen> {
  Palace? _selectedPalace;
  bool _isSaved = false;

  @override
  void initState() {
    super.initState();
    _checkIfSaved();
  }
  
  Future<void> _checkIfSaved() async {
    try {
      final storageService = Provider.of<StorageService>(context, listen: false);
      final charts = await storageService.getAllCharts();
      final saved = charts.any((chart) => chart.id == widget.chartData.id);
      setState(() {
        _isSaved = saved;
      });
    } catch (e) {
      // 如果出错，默认为未保存
      setState(() {
        _isSaved = false;
      });
    }
  }

  Future<void> _toggleSave() async {
    try {
      final storageService = Provider.of<StorageService>(context, listen: false);

      if (_isSaved) {
        await storageService.deleteChart(widget.chartData.id);
      } else {
        await storageService.saveChart(widget.chartData);
      }

      setState(() {
        _isSaved = !_isSaved;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_isSaved ? '命盘已保存' : '命盘已移除'),
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('操作失败：$e'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }
  
  void _shareChart() {
    // 实现分享功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('分享功能开发中'),
        duration: Duration(seconds: 2),
      ),
    );
  }
  
  void _viewInterpretation() {
    Navigator.pushNamed(
      context,
      '/interpretation',
      arguments: widget.chartData,
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.chartData.name.isNotEmpty
            ? '${widget.chartData.name}的命盘'
            : '紫微命盘'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(_isSaved ? Icons.favorite : Icons.favorite_border),
            onPressed: _toggleSave,
            tooltip: _isSaved ? '取消收藏' : '收藏命盘',
          ),
          IconButton(
            icon: Icon(Icons.share),
            onPressed: _shareChart,
            tooltip: '分享命盘',
          ),
          IconButton(
            icon: Icon(Icons.auto_stories),
            onPressed: _viewInterpretation,
            tooltip: '查看解读',
          ),
        ],
      ),
      body: Column(
        children: [
          // 基本信息卡片
          _buildInfoCard(),

          // 命盘图
          Expanded(
            child: _buildChartGrid(),
          ),

          // 选中宫位详情
          if (_selectedPalace != null)
            _buildPalaceDetails(),
        ],
      ),
    );
  }
  
  Widget _buildInfoCard() {
    final birthData = widget.chartData.birthData;
    return Card(
      margin: EdgeInsets.all(16),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: Colors.purple),
                SizedBox(width: 8),
                Text(
                  widget.chartData.name.isNotEmpty
                      ? widget.chartData.name
                      : '未命名',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: widget.chartData.gender == 'male'
                        ? Colors.blue
                        : Colors.pink,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    widget.chartData.gender == 'male' ? '男' : '女',
                    style: TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                SizedBox(width: 4),
                Text(
                  '${birthData.year}年${birthData.month}月${birthData.day}日 ${birthData.hour}:${birthData.minute.toString().padLeft(2, '0')}',
                  style: TextStyle(color: Colors.grey[600]),
                ),
                SizedBox(width: 16),
                Icon(Icons.location_on, size: 16, color: Colors.grey),
                SizedBox(width: 4),
                Text(
                  birthData.location?.name ?? '未知地点',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartGrid() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          childAspectRatio: 1.0,
          crossAxisSpacing: 2,
          mainAxisSpacing: 2,
        ),
        itemCount: 16,
        itemBuilder: (context, index) {
          // 紫微斗数命盘布局：
          // 巳  午  未  申
          // 辰        酉
          // 卯        戌
          // 寅  丑  子  亥

          const layoutMap = [
            4,  5,  6,  7,   // 第一行：巳午未申
            3,  -1, -1, 8,   // 第二行：辰　　酉
            2,  -1, -1, 9,   // 第三行：卯　　戌
            1,  0,  11, 10,  // 第四行：寅丑子亥
          ];

          final palaceIndex = layoutMap[index];

          // 如果是空白区域，显示中央信息
          if (palaceIndex == -1) {
            if (index == 5) { // 左上空白区域显示命主信息
              return Container(
                decoration: BoxDecoration(
                  color: Colors.purple.withOpacity(0.1),
                  border: Border.all(color: Colors.purple.withOpacity(0.3)),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('命主', style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold)),
                      Text(widget.chartData.name.isNotEmpty ? widget.chartData.name : '未命名',
                           style: TextStyle(fontSize: 10)),
                    ],
                  ),
                ),
              );
            } else if (index == 6) { // 右上空白区域显示身主信息
              return Container(
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  border: Border.all(color: Colors.orange.withOpacity(0.3)),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text('身主', style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold)),
                      Text('${widget.chartData.gender == 'male' ? '男' : '女'}命',
                           style: TextStyle(fontSize: 10)),
                    ],
                  ),
                ),
              );
            } else {
              return Container(
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  border: Border.all(color: Colors.grey.withOpacity(0.3)),
                ),
              );
            }
          }

          final palace = widget.chartData.palaces[palaceIndex];
          final isSelected = _selectedPalace?.index == palace.index;

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedPalace = isSelected ? null : palace;
              });
            },
            child: Container(
              decoration: BoxDecoration(
                color: isSelected ? Colors.purple.withOpacity(0.2) : Colors.white,
                border: Border.all(
                  color: isSelected ? Colors.purple : Colors.grey,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Padding(
                padding: EdgeInsets.all(4),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 宫位名称
                    Text(
                      palace.name,
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple,
                      ),
                    ),
                    // 地支
                    Text(
                      palace.earthBranch,
                      style: TextStyle(
                        fontSize: 8,
                        color: Colors.grey[600],
                      ),
                    ),
                    // 主星
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ...palace.mainStars.map((star) => Text(
                              star,
                              style: TextStyle(
                                fontSize: 8,
                                color: Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                            )),
                            ...palace.minorStars.map((star) => Text(
                              star,
                              style: TextStyle(
                                fontSize: 7,
                                color: Colors.blue,
                              ),
                            )),
                            ...palace.transformStars.map((star) => Text(
                              star,
                              style: TextStyle(
                                fontSize: 7,
                                color: Colors.green,
                              ),
                            )),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPalaceDetails() {
    if (_selectedPalace == null) return SizedBox.shrink();

    return Container(
      margin: EdgeInsets.all(16),
      child: Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Text(
                    _selectedPalace!.name,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                  ),
                  SizedBox(width: 8),
                  Text(
                    '(${_selectedPalace!.earthBranch})',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  Spacer(),
                  IconButton(
                    icon: Icon(Icons.close),
                    onPressed: () {
                      setState(() {
                        _selectedPalace = null;
                      });
                    },
                  ),
                ],
              ),
              SizedBox(height: 8),
              if (_selectedPalace!.mainStars.isNotEmpty) ...[
                Text('主星：', style: TextStyle(fontWeight: FontWeight.bold)),
                Text(_selectedPalace!.mainStars.join('、'),
                     style: TextStyle(color: Colors.red)),
                SizedBox(height: 4),
              ],
              if (_selectedPalace!.minorStars.isNotEmpty) ...[
                Text('辅星：', style: TextStyle(fontWeight: FontWeight.bold)),
                Text(_selectedPalace!.minorStars.join('、'),
                     style: TextStyle(color: Colors.blue)),
                SizedBox(height: 4),
              ],
              if (_selectedPalace!.transformStars.isNotEmpty) ...[
                Text('化星：', style: TextStyle(fontWeight: FontWeight.bold)),
                Text(_selectedPalace!.transformStars.join('、'),
                     style: TextStyle(color: Colors.green)),
              ],
            ],
          ),
        ),
      ),
    );
  }
}


