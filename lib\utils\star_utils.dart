// lib/utils/star_utils.dart
class StarUtils {
  // 主星列表
  static const List<String> mainStars = [
    '紫微', '天机', '太阳', '武曲', '天同', '廉贞',
    '天府', '太阴', '贪狼', '巨门', '天相', '天梁', '七杀', '破军'
  ];
  
  // 辅星列表
  static const List<String> minorStars = [
    '左辅', '右弼', '文昌', '文曲', '天魁', '天钺',
    '禄存', '擎羊', '陀罗', '火星', '铃星', '地空', '地劫'
  ];
  
  // 化星列表
  static const List<String> transformStars = [
    '化禄', '化权', '化科', '化忌'
  ];
  
  // 获取星曜属性
  static Map<String, dynamic> getStarProperties(String starName) {
    final properties = {
      // 主星属性
      '紫微': {'type': 'main', 'nature': '帝星', 'element': '土', 'brightness': 5},
      '天机': {'type': 'main', 'nature': '智慧', 'element': '木', 'brightness': 4},
      '太阳': {'type': 'main', 'nature': '贵星', 'element': '火', 'brightness': 5},
      '武曲': {'type': 'main', 'nature': '财星', 'element': '金', 'brightness': 4},
      '天同': {'type': 'main', 'nature': '福星', 'element': '水', 'brightness': 4},
      '廉贞': {'type': 'main', 'nature': '囚星', 'element': '火', 'brightness': 4},
      '天府': {'type': 'main', 'nature': '财库', 'element': '土', 'brightness': 5},
      '太阴': {'type': 'main', 'nature': '富星', 'element': '水', 'brightness': 4},
      '贪狼': {'type': 'main', 'nature': '桃花', 'element': '木', 'brightness': 4},
      '巨门': {'type': 'main', 'nature': '暗星', 'element': '水', 'brightness': 4},
      '天相': {'type': 'main', 'nature': '印星', 'element': '水', 'brightness': 4},
      '天梁': {'type': 'main', 'nature': '荫星', 'element': '土', 'brightness': 4},
      '七杀': {'type': 'main', 'nature': '将星', 'element': '金', 'brightness': 4},
      '破军': {'type': 'main', 'nature': '耗星', 'element': '水', 'brightness': 4},
      
      // 辅星属性
      '左辅': {'type': 'minor', 'nature': '贵人', 'element': '土', 'brightness': 3},
      '右弼': {'type': 'minor', 'nature': '贵人', 'element': '水', 'brightness': 3},
      '文昌': {'type': 'minor', 'nature': '文星', 'element': '金', 'brightness': 3},
      '文曲': {'type': 'minor', 'nature': '文星', 'element': '水', 'brightness': 3},
      '天魁': {'type': 'minor', 'nature': '贵人', 'element': '火', 'brightness': 3},
      '天钺': {'type': 'minor', 'nature': '贵人', 'element': '火', 'brightness': 3},
      '禄存': {'type': 'minor', 'nature': '财星', 'element': '土', 'brightness': 3},
      '擎羊': {'type': 'minor', 'nature': '刑星', 'element': '金', 'brightness': 2},
      '陀罗': {'type': 'minor', 'nature': '忌星', 'element': '金', 'brightness': 2},
      '火星': {'type': 'minor', 'nature': '杀星', 'element': '火', 'brightness': 2},
      '铃星': {'type': 'minor', 'nature': '杀星', 'element': '火', 'brightness': 2},
      '地空': {'type': 'minor', 'nature': '空星', 'element': '火', 'brightness': 1},
      '地劫': {'type': 'minor', 'nature': '劫星', 'element': '火', 'brightness': 1},
      
      // 化星属性
      '化禄': {'type': 'transform', 'nature': '财禄', 'element': '土', 'brightness': 4},
      '化权': {'type': 'transform', 'nature': '权势', 'element': '木', 'brightness': 4},
      '化科': {'type': 'transform', 'nature': '名声', 'element': '水', 'brightness': 4},
      '化忌': {'type': 'transform', 'nature': '阻碍', 'element': '水', 'brightness': 2},
    };
    
    return properties[starName] ?? {
      'type': 'unknown',
      'nature': '未知',
      'element': '未知',
      'brightness': 1
    };
  }
  
  // 判断星曜是否为吉星
  static bool isLuckyStar(String starName) {
    final luckyStars = [
      '紫微', '天府', '太阳', '太阴', '天同', '天相', '天梁',
      '左辅', '右弼', '文昌', '文曲', '天魁', '天钺', '禄存',
      '化禄', '化权', '化科'
    ];
    return luckyStars.contains(starName);
  }
  
  // 判断星曜是否为凶星
  static bool isUnluckyStar(String starName) {
    final unluckyStars = [
      '七杀', '破军', '廉贞', '贪狼', '巨门',
      '擎羊', '陀罗', '火星', '铃星', '地空', '地劫', '化忌'
    ];
    return unluckyStars.contains(starName);
  }
  
  // 获取星曜颜色
  static String getStarColor(String starName) {
    final properties = getStarProperties(starName);
    final type = properties['type'] as String;
    
    switch (type) {
      case 'main':
        return isLuckyStar(starName) ? '#4CAF50' : '#FF9800';
      case 'minor':
        return isLuckyStar(starName) ? '#2196F3' : '#F44336';
      case 'transform':
        return starName == '化忌' ? '#F44336' : '#9C27B0';
      default:
        return '#757575';
    }
  }
  
  // 获取宫位颜色
  static String getPalaceColor(int palaceIndex) {
    final colors = [
      '#E3F2FD', '#F3E5F5', '#E8F5E8', '#FFF3E0',
      '#FCE4EC', '#E0F2F1', '#F1F8E9', '#FFF8E1',
      '#E8EAF6', '#EFEBE9', '#FAFAFA', '#F9FBE7'
    ];
    return colors[palaceIndex % 12];
  }
  
  // 获取星曜组合解释
  static String getStarCombinationMeaning(List<String> stars) {
    if (stars.isEmpty) return '空宫';
    
    // 简化的组合解释
    if (stars.contains('紫微') && stars.contains('天府')) {
      return '帝王格局，富贵双全';
    } else if (stars.contains('紫微') && stars.contains('破军')) {
      return '开创格局，变动较大';
    } else if (stars.contains('天机') && stars.contains('太阴')) {
      return '机月同梁格，清贵之命';
    } else if (stars.contains('武曲') && stars.contains('贪狼')) {
      return '武贪格，先贫后富';
    } else if (stars.contains('化禄') && stars.contains('化权')) {
      return '禄权相会，名利双收';
    } else if (stars.contains('化忌')) {
      return '化忌入宫，需防阻碍';
    }
    
    return '${stars.join('、')}同宫';
  }
}
