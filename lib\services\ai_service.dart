// lib/services/ai_service.dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/chart_data.dart';

class AIService {
  final String apiUrl = 'https://api.example.com/ai/interpret';

  Future<String> generateInterpretation(ChartData chartData) async {
    try {
      // 准备请求数据
      final requestData = {
        'chartData': chartData.toJson(),
        'interpretationType': 'comprehensive',
      };

      // 发送API请求
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(requestData),
      );

      if (response.statusCode == 200) {
        // 解析响应数据
        final responseData = jsonDecode(response.body);
        return responseData['interpretation'] ?? _generateLocalInterpretation(chartData);
      } else {
        // 如果服务器返回错误，使用本地模板解读
        return _generateLocalInterpretation(chartData);
      }
    } catch (e) {
      // 如果API请求失败，使用本地模板解读
      return _generateLocalInterpretation(chartData);
    }
  }
  
  String _generateLocalInterpretation(ChartData chartData) {
    // 使用本地模板生成基础解读
    // 这是一个备用方案，当AI服务不可用时使用

    // 获取命宫信息
    final ascendantPalace = chartData.palaces[0]; // 命宫
    final careerPalace = chartData.palaces[8]; // 官禄宫
    final wealthPalace = chartData.palaces[4]; // 财帛宫
    final marriagePalace = chartData.palaces[2]; // 夫妻宫

    final interpretation = StringBuffer();

    interpretation.writeln('🌟 AI智能命盘解读');
    interpretation.writeln('');
    interpretation.writeln('📊 基本信息');
    interpretation.writeln('姓名：${chartData.name}');
    interpretation.writeln('性别：${chartData.gender == 'male' ? '男' : '女'}');
    interpretation.writeln('出生：${chartData.birthData.year}年${chartData.birthData.month}月${chartData.birthData.day}日');
    interpretation.writeln('');

    interpretation.writeln('🎯 性格特质分析');
    interpretation.writeln('命宫位于${ascendantPalace.earthBranch}，');
    if (ascendantPalace.mainStars.isNotEmpty) {
      interpretation.writeln('主星：${ascendantPalace.mainStars.join('、')}');
      interpretation.writeln(_getPersonalityAnalysis(ascendantPalace.mainStars));
    } else {
      interpretation.writeln('您具有独特的性格特质，建议多发挥自身优势。');
    }
    interpretation.writeln('');

    interpretation.writeln('💼 事业发展');
    interpretation.writeln('官禄宫位于${careerPalace.earthBranch}，');
    if (careerPalace.mainStars.isNotEmpty) {
      interpretation.writeln('主星：${careerPalace.mainStars.join('、')}');
      interpretation.writeln(_getCareerAnalysis(careerPalace.mainStars));
    } else {
      interpretation.writeln('建议选择适合自己特质的职业方向，发挥所长。');
    }
    interpretation.writeln('');

    interpretation.writeln('💰 财富运势');
    interpretation.writeln('财帛宫位于${wealthPalace.earthBranch}，');
    if (wealthPalace.mainStars.isNotEmpty) {
      interpretation.writeln('主星：${wealthPalace.mainStars.join('、')}');
      interpretation.writeln(_getWealthAnalysis(wealthPalace.mainStars));
    } else {
      interpretation.writeln('建议合理理财，开源节流，必能积累财富。');
    }
    interpretation.writeln('');

    interpretation.writeln('💕 感情婚姻');
    interpretation.writeln('夫妻宫位于${marriagePalace.earthBranch}，');
    if (marriagePalace.mainStars.isNotEmpty) {
      interpretation.writeln('主星：${marriagePalace.mainStars.join('、')}');
      interpretation.writeln(_getRelationshipAnalysis(marriagePalace.mainStars));
    } else {
      interpretation.writeln('建议以诚待人，用心经营感情，必能获得美满的婚姻。');
    }
    interpretation.writeln('');

    interpretation.writeln('📝 总结建议');
    interpretation.writeln('您的命盘整体配置良好，各宫位星曜分布合理。建议：');
    interpretation.writeln('1. 发挥自身优势，克服性格中的不足');
    interpretation.writeln('2. 在事业上选择适合的发展方向');
    interpretation.writeln('3. 理性理财，稳健投资');
    interpretation.writeln('4. 用心经营人际关系和感情');
    interpretation.writeln('5. 保持积极乐观的心态');

    return interpretation.toString();
  }
  
  String _getPersonalityAnalysis(List<String> mainStars) {
    if (mainStars.contains('紫微')) {
      return '紫微星坐命，具有领导才能，性格高贵，有王者风范。做事有条理，责任心强，但有时会显得过于严肃。适合担任管理职务。';
    } else if (mainStars.contains('天府')) {
      return '天府星坐命，性格温和稳重，具有很强的包容力。善于理财，做事踏实，是值得信赖的人。有很好的组织协调能力。';
    } else if (mainStars.contains('武曲')) {
      return '武曲星坐命，性格刚毅果断，有很强的执行力。适合从事需要专业技能的工作，但需要注意与人沟通的方式。';
    } else {
      return '根据您的命宫星曜配置，您具有独特的性格特质，建议多发挥自身优势，注意克服性格中的不足。';
    }
  }

  String _getCareerAnalysis(List<String> mainStars) {
    if (mainStars.contains('武曲')) {
      return '武曲星在官禄宫，适合从事金融、工程、技术等需要专业技能的工作。事业发展稳健，但需要耐心积累。';
    } else if (mainStars.contains('天相')) {
      return '天相星在官禄宫，适合从事服务业、公务员、教育等与人打交道的工作。事业运势平稳，贵人运佳。';
    } else if (mainStars.contains('紫微')) {
      return '紫微星在官禄宫，适合担任领导职务，在管理岗位上能发挥所长。事业发展有很大潜力。';
    } else {
      return '根据官禄宫星曜配置，建议选择适合自己特质的职业方向，发挥所长，必能在事业上有所成就。';
    }
  }

  String _getWealthAnalysis(List<String> mainStars) {
    if (mainStars.contains('武曲')) {
      return '武曲星在财帛宫，财运稳健，适合通过专业技能获得财富。理财能力强，但需要避免过于保守。';
    } else if (mainStars.contains('天府')) {
      return '天府星在财帛宫，财运良好，善于积累财富。有很好的理财观念，能够稳健地增加财富。';
    } else {
      return '根据财帛宫的星曜配置，您的财运有一定的特点。建议合理理财，开源节流，必能积累财富。';
    }
  }

  String _getRelationshipAnalysis(List<String> mainStars) {
    if (mainStars.contains('天府')) {
      return '天府星在夫妻宫，感情稳定，配偶贤良。婚姻生活和谐，能够相互扶持。';
    } else if (mainStars.contains('紫微')) {
      return '紫微星在夫妻宫，配偶有一定的社会地位。感情生活较为理想，但需要注意沟通方式。';
    } else {
      return '根据夫妻宫的星曜配置，您在感情方面有自己的特点。建议以诚待人，用心经营感情，必能获得美满的婚姻。';
    }
  }
}