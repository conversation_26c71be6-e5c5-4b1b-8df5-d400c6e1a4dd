// lib/widgets/radar_chart.dart
import 'package:flutter/material.dart';
import 'dart:math' as math;

class <PERSON>Chart extends StatelessWidget {
  final Map<String, double> data;
  final List<String> labels;
  final double maxScore;
  
  RadarChart({
    required this.data,
    required this.labels,
    required this.maxScore,
  });
  
  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(double.infinity, double.infinity),
      painter: RadarChartPainter(
        data: data,
        labels: labels,
        maxScore: maxScore,
        labelColor: Theme.of(context).textTheme.bodyMedium?.color ?? Colors.black,
        fillColor: Theme.of(context).primaryColor.withOpacity(0.2),
        strokeColor: Theme.of(context).primaryColor,
      ),
    );
  }
}

class RadarChartPainter extends CustomPainter {
  final Map<String, double> data;
  final List<String> labels;
  final double maxScore;
  final Color labelColor;
  final Color fillColor;
  final Color strokeColor;
  
  RadarChartPainter({
    required this.data,
    required this.labels,
    required this.maxScore,
    required this.labelColor,
    required this.fillColor,
    required this.strokeColor,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = math.min(size.width, size.height) / 2 - 30;
    
    // 绘制背景网格
    _drawBackground(canvas, center, radius);
    
    // 绘制数据区域
    _drawData(canvas, center, radius);
    
    // 绘制标签
    _drawLabels(canvas, center, radius);
  }
  
  void _drawBackground(Canvas canvas, Offset center, double radius) {
    final paint = Paint()
      ..color = Colors.grey[300]!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;
    
    // 绘制同心圆
    for (int i = 1; i <= 5; i++) {
      final r = radius * i / 5;
      canvas.drawCircle(center, r, paint);
    }
    
    // 绘制轴线
    final count = labels.length;
    for (int i = 0; i < count; i++) {
      final angle = 2 * math.pi * i / count - math.pi / 2;
      final dx = radius * math.cos(angle);
      final dy = radius * math.sin(angle);
      
      canvas.drawLine(
        center,
        center + Offset(dx, dy),
        paint,
      );
    }
  }
  
  void _drawData(Canvas canvas, Offset center, double radius) {
    final count = labels.length;
    final points = <Offset>[];
    
    // 计算每个数据点的位置
    for (int i = 0; i < count; i++) {
      final value = data[labels[i]] ?? 0;
      final ratio = value / maxScore;
      final angle = 2 * math.pi * i / count - math.pi / 2;
      final dx = radius * ratio * math.cos(angle);
      final dy = radius * ratio * math.sin(angle);
      
      points.add(center + Offset(dx, dy));
    }
    
    // 绘制填充区域
    final fillPaint = Paint()
      ..color = fillColor
      ..style = PaintingStyle.fill;
    
    final path = Path()..addPolygon(points, true);
    canvas.drawPath(path, fillPaint);
    
    // 绘制边框
    final strokePaint = Paint()
      ..color = strokeColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    canvas.drawPath(path, strokePaint);
    
    // 绘制数据点
    final pointPaint = Paint()
      ..color = strokeColor
      ..style = PaintingStyle.fill;
    
    for (final point in points) {
      canvas.drawCircle(point, 4, pointPaint);
    }
  }
  
  void _drawLabels(Canvas canvas, Offset center, double radius) {
    final count = labels.length;
    final textStyle = TextStyle(
      color: labelColor,
      fontSize: 12,
    );
    
    for (int i = 0; i < count; i++) {
      final angle = 2 * math.pi * i / count - math.pi / 2;
      final dx = (radius + 20) * math.cos(angle);
      final dy = (radius + 20) * math.sin(angle);
      
      final textSpan = TextSpan(
        text: labels[i],
        style: textStyle,
      );
      
      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      
      textPainter.layout();
      
      final offset = center + Offset(dx, dy);
      final textOffset = Offset(
        offset.dx - textPainter.width / 2,
        offset.dy - textPainter.height / 2,
      );
      
      textPainter.paint(canvas, textOffset);
    }
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

