# 紫微斗数命盘应用 - 项目结构和依赖关系

## 1. 项目概述

紫微斗数命盘应用是一个基于Flutter开发的中国传统命理学应用，主要功能包括：
- 出生信息输入和验证
- 四柱八字计算
- 紫微斗数命盘生成
- 命盘解读和分析
- 高级设置和个性化配置

## 2. 项目目录结构

```
ziwei_reader/
├── lib/                           # 主要源代码目录
│   ├── main.dart                  # 应用入口文件
│   ├── data/                      # 数据文件目录
│   │   ├── china_cities.dart      # 中国城市经纬度数据
│   │   └── ziwei_star_database.dart # 紫微星曜数据库
│   ├── models/                    # 数据模型目录
│   │   ├── advanced_settings.dart # 高级设置数据模型
│   │   ├── birth_data.dart        # 出生数据模型
│   │   ├── chart_data.dart        # 命盘数据模型
│   │   ├── interpretation.dart    # 解读数据模型
│   │   ├── location.dart          # 地理位置模型
│   │   ├── palace.dart            # 宫位模型
│   │   ├── user.dart              # 用户模型
│   │   ├── ziwei_chart.dart       # 紫微命盘模型
│   │   ├── ziwei_palace.dart      # 紫微宫位模型
│   │   └── ziwei_star.dart        # 紫微星曜模型
│   ├── services/                  # 业务服务层
│   │   ├── ai_service.dart        # AI解读服务
│   │   ├── api_service.dart       # API服务
│   │   ├── auth_service.dart      # 认证服务
│   │   ├── chart_calculator.dart  # 命盘计算服务
│   │   ├── chart_service.dart     # 命盘业务服务
│   │   ├── location_service.dart  # 位置服务
│   │   ├── paipan_service.dart    # 核心排盘服务（基于paipan.php）
│   │   ├── settings_service.dart  # 设置服务
│   │   ├── storage_service.dart   # 存储服务
│   │   ├── ziwei_chart_calculator.dart # 紫微命盘计算器
│   │   └── ziwei_star_placement.dart   # 紫微星曜安星算法
│   ├── screens/                   # 界面层
│   │   ├── auth/                  # 认证界面
│   │   ├── bazi/                  # 八字界面
│   │   ├── calendar/              # 万年历界面
│   │   ├── chart/                 # 命盘显示界面
│   │   ├── home/                  # 主页界面
│   │   ├── input/                 # 输入界面
│   │   │   └── birth_info_screen.dart # 出生信息输入界面
│   │   └── interpretation/        # 解读界面
│   ├── utils/                     # 工具类目录
│   │   ├── calendar_converter.dart    # 历法转换工具
│   │   ├── hour_calculator.dart       # 时辰计算工具
│   │   ├── palace_utils.dart          # 宫位工具
│   │   ├── perpetual_calendar.dart    # 万年历工具
│   │   ├── solar_terms_calculator.dart # 节气计算工具
│   │   ├── solar_time_converter.dart  # 太阳时转换工具
│   │   ├── star_utils.dart            # 星曜工具
│   │   └── stem_branch_calculator.dart # 干支计算工具
│   └── widgets/                   # 自定义组件目录
│       ├── calendar_selector.dart     # 历法选择器
│       ├── calendar_switch.dart       # 历法切换开关
│       ├── chart_view.dart            # 命盘视图
│       ├── custom_time_selector.dart  # 自定义时间选择器
│       ├── date_picker.dart           # 日期选择器
│       ├── hour_selector.dart         # 时辰选择器
│       ├── location_picker.dart       # 地点选择器
│       ├── palace_detail.dart         # 宫位详情
│       └── radar_chart.dart           # 雷达图
├── assets/                        # 资源文件目录
│   ├── data/                      # 数据文件
│   └── images/                    # 图片资源
├── test/                          # 测试文件目录
├── windows/                       # Windows平台配置
├── pubspec.yaml                   # 项目配置文件
├── paipan.php                     # PHP排盘算法参考
└── 文档文件/                      # 项目文档
    ├── 紫微斗数系统实现总结.md
    ├── 真假太阳时转换和天干地支计算功能说明.md
    └── ...
```

## 3. 核心依赖关系

### 3.1 Flutter依赖 (pubspec.yaml)

```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # 状态管理
  provider: ^6.0.5
  
  # 网络请求
  http: ^0.13.5
  
  # 本地存储
  shared_preferences: ^2.0.18
  
  # 位置服务
  geolocator: ^9.0.2
  geocoding: ^2.1.0
  
  # 日期时间处理
  intl: ^0.18.0
  
  # UI组件
  cupertino_icons: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
```

### 3.2 核心模块依赖关系

```
birth_info_screen.dart
├── models/
│   ├── birth_data.dart
│   ├── location.dart
│   ├── advanced_settings.dart
│   ├── chart_data.dart
│   └── ziwei_chart.dart
├── services/
│   ├── paipan_service.dart (核心排盘服务)
│   ├── ziwei_chart_calculator.dart
│   ├── settings_service.dart
│   └── storage_service.dart
├── data/
│   └── china_cities.dart
└── widgets/
    └── custom_time_selector.dart
```

## 4. 架构设计原则

### 4.1 分层架构
- **界面层 (Screens)**: 负责用户交互和界面展示
- **业务服务层 (Services)**: 负责业务逻辑处理
- **数据模型层 (Models)**: 负责数据结构定义
- **工具层 (Utils)**: 负责通用工具函数
- **组件层 (Widgets)**: 负责可复用UI组件

### 4.2 核心服务
- **PaipanService**: 核心排盘算法，基于paipan.php实现
- **ZiweiChartCalculator**: 紫微斗数命盘计算
- **SettingsService**: 高级设置管理
- **StorageService**: 数据持久化

### 4.3 数据流向
```
用户输入 → birth_info_screen.dart → paipan_service.dart → 计算结果 → 界面显示
```

## 5. 最近修改

### 5.1 删除的功能
- ✅ 删除了真太阳时计算功能
- ✅ 删除了节气信息显示功能
- ✅ 简化了高级设置选项

### 5.2 保留的核心功能
- ✅ 四柱八字计算
- ✅ 城市选择和附近城市推荐
- ✅ 高级设置（年柱起始、子时处理、闰月分界）
- ✅ 自定义时间选择器
- ✅ 紫微斗数命盘生成

## 6. 技术特点

- **统一排盘算法**: 所有计算统一使用PaipanService
- **模块化设计**: 清晰的模块分离和依赖关系
- **数据持久化**: 使用SharedPreferences存储用户设置
- **响应式UI**: 基于Provider的状态管理
- **跨平台支持**: Flutter框架支持多平台部署
