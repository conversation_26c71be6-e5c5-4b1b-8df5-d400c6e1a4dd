// lib/examples/solar_time_example.dart
// 真假太阳时转换和天干地支计算功能使用示例

import '../utils/calendar_converter.dart';
import '../models/location.dart';

/// 真假太阳时转换和天干地支计算示例类
class SolarTimeExample {
  
  /// 演示真假太阳时转换功能
  static void demonstrateSolarTimeConversion() {
    print('=== 真假太阳时转换示例 ===');
    
    // 示例：2024年6月21日（夏至）12:00，北京
    DateTime dateTime = DateTime(2024, 6, 21, 12, 0, 0);
    Location beijing = Location(
      country: '中国',
      province: '北京市',
      city: '北京市',
      latitude: 39.9042,
      longitude: 116.4074,
    );
    
    print('地点：${beijing.name}');
    print('经度：${beijing.longitude}°E');
    print('平太阳时：${_formatTime(dateTime)}');
    
    // 转换为真太阳时
    DateTime apparentTime = CalendarConverter.meanToApparentSolarTime(
      dateTime, 
      beijing.longitude
    );
    
    print('真太阳时：${_formatTime(apparentTime)}');
    
    // 计算时差
    int diffMinutes = apparentTime.difference(dateTime).inMinutes;
    print('时差：${diffMinutes > 0 ? '+' : ''}${diffMinutes}分钟');
    print('');
  }
  
  /// 演示时辰计算功能
  static void demonstrateHourCalculation() {
    print('=== 时辰计算示例 ===');
    
    DateTime dateTime = DateTime(2024, 1, 1, 14, 30, 0);
    double longitude = 116.4074; // 北京经度
    
    print('时间：${_formatTime(dateTime)}');
    print('地点：东经${longitude}°');
    
    // 平太阳时时辰
    int hourIndexMean = CalendarConverter.getHourIndexByMeanTime(dateTime);
    print('平太阳时时辰：${CalendarConverter.getHourName(hourIndexMean)}时');
    
    // 真太阳时时辰
    int hourIndexApparent = CalendarConverter.getHourIndexByApparentTime(dateTime, longitude);
    print('真太阳时时辰：${CalendarConverter.getHourName(hourIndexApparent)}时');
    
    // 时辰详细信息
    Map<String, dynamic> hourDetails = CalendarConverter.getHourDetails(hourIndexApparent);
    print('时辰详情：');
    print('  名称：${hourDetails['name']}');
    print('  时间范围：${hourDetails['range']}');
    print('  五行：${hourDetails['element']}');
    print('  方位：${hourDetails['direction']}');
    print('  描述：${hourDetails['description']}');
    print('');
  }
  
  /// 演示阳历阴历转换功能
  static void demonstrateLunarSolarConversion() {
    print('=== 阳历阴历转换示例 ===');
    
    DateTime solarDate = DateTime(2024, 2, 10); // 2024年春节
    
    print('公历：${solarDate.year}年${solarDate.month}月${solarDate.day}日');
    
    // 转换为农历
    Map<String, dynamic> lunarInfo = CalendarConverter.solarToLunar(solarDate);
    String lunarMonthName = CalendarConverter.getLunarMonthName(
      lunarInfo['month'], 
      lunarInfo['isLeapMonth']
    );
    String lunarDayName = CalendarConverter.getLunarDayName(lunarInfo['day']);
    
    print('农历：${lunarInfo['year']}年$lunarMonthName$lunarDayName');
    
    // 转换回公历验证
    DateTime backToSolar = CalendarConverter.lunarToSolar(
      lunarInfo['year'],
      lunarInfo['month'],
      lunarInfo['day'],
      lunarInfo['isLeapMonth'],
    );
    
    print('验证转换回公历：${backToSolar.year}年${backToSolar.month}月${backToSolar.day}日');
    print('');
  }
  
  /// 演示完整的四柱干支计算
  static void demonstrateFourPillarsCalculation() {
    print('=== 四柱干支计算示例 ===');
    
    DateTime birthTime = DateTime(2024, 1, 1, 14, 30, 0);
    double longitude = 116.4074; // 北京经度
    
    print('出生时间：${_formatDateTime(birthTime)}');
    print('出生地点：东经${longitude}°');
    
    // 使用真太阳时计算四柱
    Map<String, String> fourPillars = CalendarConverter.getFourPillars(
      birthTime, 
      longitude: longitude
    );
    
    print('四柱干支（真太阳时）：');
    print('  年柱：${fourPillars['year']}');
    print('  月柱：${fourPillars['month']}');
    print('  日柱：${fourPillars['day']}');
    print('  时柱：${fourPillars['hour']}');
    
    // 使用平太阳时计算四柱对比
    Map<String, String> fourPillarsMean = CalendarConverter.getFourPillars(birthTime);
    
    print('四柱干支（平太阳时）：');
    print('  年柱：${fourPillarsMean['year']}');
    print('  月柱：${fourPillarsMean['month']}');
    print('  日柱：${fourPillarsMean['day']}');
    print('  时柱：${fourPillarsMean['hour']}');
    print('');
  }
  
  /// 演示一天中时辰的变化
  static void demonstrateHourChanges() {
    print('=== 一天中时辰变化示例 ===');
    
    DateTime baseDate = DateTime(2024, 1, 1);
    
    print('日期：${baseDate.year}年${baseDate.month}月${baseDate.day}日');
    print('时辰变化：');
    
    for (int hour = 0; hour < 24; hour += 2) {
      DateTime time = baseDate.add(Duration(hours: hour));
      int hourIndex = CalendarConverter.getHourIndexByMeanTime(time);
      String hourName = CalendarConverter.getHourName(hourIndex);
      String hourRange = CalendarConverter.getModernHourRange(hourIndex);
      String hourStemBranch = CalendarConverter.getHourStemBranch(time, hourIndex);
      
      print('  ${hour.toString().padLeft(2, '0')}:00 -> ${hourName}时 (${hourRange}) ${hourStemBranch}');
    }
    print('');
  }
  
  /// 演示不同地点的时差影响
  static void demonstrateLocationTimeEffect() {
    print('=== 不同地点时差影响示例 ===');
    
    DateTime dateTime = DateTime(2024, 6, 21, 12, 0, 0); // 夏至正午
    
    List<Location> locations = [
      Location(country: '中国', province: '北京市', city: '北京市', latitude: 39.9042, longitude: 116.4074),
      Location(country: '中国', province: '新疆', city: '乌鲁木齐', latitude: 43.8256, longitude: 87.6168),
      Location(country: '中国', province: '黑龙江', city: '哈尔滨', latitude: 45.8038, longitude: 126.5349),
      Location(country: '中国', province: '广东', city: '广州', latitude: 23.1291, longitude: 113.2644),
    ];
    
    print('时间：${_formatTime(dateTime)}（平太阳时）');
    print('不同地点的真太阳时：');
    
    for (Location location in locations) {
      DateTime apparentTime = CalendarConverter.meanToApparentSolarTime(dateTime, location.longitude);
      int diffMinutes = apparentTime.difference(dateTime).inMinutes;
      int hourIndex = CalendarConverter.getHourIndexByApparentTime(dateTime, location.longitude);
      String hourName = CalendarConverter.getHourName(hourIndex);
      
      print('  ${location.name}（${location.longitude}°E）：${_formatTime(apparentTime)} (${diffMinutes > 0 ? '+' : ''}${diffMinutes}分钟) ${hourName}时');
    }
    print('');
  }
  
  /// 运行所有示例
  static void runAllExamples() {
    print('真假太阳时转换和天干地支计算功能示例\n');
    
    demonstrateSolarTimeConversion();
    demonstrateHourCalculation();
    demonstrateLunarSolarConversion();
    demonstrateFourPillarsCalculation();
    demonstrateHourChanges();
    demonstrateLocationTimeEffect();
    
    print('示例演示完成！');
  }
  
  /// 格式化时间显示
  static String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
  }
  
  /// 格式化日期时间显示
  static String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 ${_formatTime(dateTime)}';
  }
}
