// lib/widgets/hour_selector.dart
import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../utils/calendar_converter.dart';

class HourSelector extends StatefulWidget {
  final int initialHour;
  final Function(int) onHourSelected;
  
  HourSelector({
    this.initialHour = 0,
    required this.onHourSelected,
  });
  
  @override
  _HourSelectorState createState() => _HourSelectorState();
}

class _HourSelectorState extends State<HourSelector> {
  late int _selectedHour;
  
  @override
  void initState() {
    super.initState();
    _selectedHour = widget.initialHour;
  }
  
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 外圈
          Container(
            width: 280,
            height: 280,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.grey[200],
            ),
          ),
          
          // 内圈
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    CalendarConverter.getHourName(_selectedHour),
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    CalendarConverter.getModernHourRange(_selectedHour),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // 12个时辰按钮
          ...List.generate(12, (index) {
            final angle = index * (2 * math.pi / 12);
            final radius = 90.0;
            final x = radius * math.cos(angle - math.pi / 2);
            final y = radius * math.sin(angle - math.pi / 2);
            
            return Positioned(
              left: 140 + x - 30,
              top: 140 + y - 30,
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedHour = index;
                  });
                  widget.onHourSelected(index);
                },
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _selectedHour == index
                        ? Theme.of(context).primaryColor
                        : Colors.white,
                    border: Border.all(
                      color: Colors.grey[300]!,
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        CalendarConverter.getHourName(index),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: _selectedHour == index
                              ? Colors.white
                              : Colors.black,
                        ),
                      ),
                      Text(
                        index == 0 ? '夜半' : 
                        index == 6 ? '正午' : '',
                        style: TextStyle(
                          fontSize: 10,
                          color: _selectedHour == index
                              ? Colors.white70
                              : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}



