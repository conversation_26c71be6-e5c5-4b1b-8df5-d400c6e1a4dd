// lib/screens/home/<USER>
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/chart_data.dart';
import '../../services/storage_service.dart';
import '../../services/auth_service.dart';

class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late Future<List<ChartData>> _chartsFuture;

  @override
  void initState() {
    super.initState();
    _loadCharts();
  }

  void _loadCharts() {
    final storageService = Provider.of<StorageService>(context, listen: false);
    _chartsFuture = storageService.getAllCharts();
  }
  
  @override
  Widget build(BuildContext context) {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text('紫微命盘'),
            actions: [
              IconButton(
                icon: Icon(Icons.calendar_today),
                onPressed: () {
                  Navigator.pushNamed(context, '/calendar');
                },
                tooltip: '万年历',
              ),
              IconButton(
                icon: Icon(Icons.refresh),
                onPressed: () {
                  setState(() {
                    _loadCharts();
                  });
                },
                tooltip: '刷新',
              ),
              IconButton(
                icon: Icon(Icons.account_circle),
                onPressed: () {
                  if (authService.currentUser == null) {
                    Navigator.pushNamed(context, '/login');
                  } else {
                    _showUserMenu(context);
                  }
                },
                tooltip: '用户菜单',
              ),
            ],
          ),
          body: FutureBuilder<List<ChartData>>(
            future: _chartsFuture,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(child: CircularProgressIndicator());
              }

              if (snapshot.hasError) {
                return Center(child: Text('加载命盘失败，请重试'));
              }

              final charts = snapshot.data ?? [];

              if (charts.isEmpty) {
                return _buildEmptyState();
              }

              return Column(
                children: [
                  _buildQuickActions(),
                  Expanded(child: _buildChartList(charts)),
                ],
              );
            },
          ),
          floatingActionButton: FloatingActionButton(
            child: Icon(Icons.add),
            onPressed: () {
              Navigator.pushNamed(context, '/input');
            },
            tooltip: '创建新命盘',
          ),
        );
      },
    );
  }
  
  /// 构建快速操作按钮（当有命盘时显示）
  Widget _buildQuickActions() {
    return Container(
      padding: EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            child: OutlinedButton.icon(
              icon: Icon(Icons.add, size: 18),
              label: Text('创建命盘', style: TextStyle(fontSize: 12)),
              onPressed: () {
                Navigator.pushNamed(context, '/input');
              },
              style: OutlinedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              ),
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            child: OutlinedButton.icon(
              icon: Icon(Icons.auto_awesome, size: 18),
              label: Text('八字排盘', style: TextStyle(fontSize: 12)),
              onPressed: () {
                Navigator.pushNamed(context, '/bazi');
              },
              style: OutlinedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              ),
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            child: OutlinedButton.icon(
              icon: Icon(Icons.calendar_today, size: 18),
              label: Text('万年历', style: TextStyle(fontSize: 12)),
              onPressed: () {
                Navigator.pushNamed(context, '/calendar');
              },
              style: OutlinedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.auto_graph, size: 80, color: Colors.grey),
          SizedBox(height: 16),
          Text('还没有命盘', style: TextStyle(fontSize: 18)),
          SizedBox(height: 8),
          Text('点击下方按钮创建您的第一个命盘', style: TextStyle(color: Colors.grey)),
          SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                icon: Icon(Icons.add),
                label: Text('创建命盘'),
                onPressed: () {
                  Navigator.pushNamed(context, '/input');
                },
              ),
              SizedBox(width: 12),
              OutlinedButton.icon(
                icon: Icon(Icons.auto_awesome),
                label: Text('八字排盘'),
                onPressed: () {
                  Navigator.pushNamed(context, '/bazi');
                },
              ),
              SizedBox(width: 12),
              OutlinedButton.icon(
                icon: Icon(Icons.calendar_today),
                label: Text('万年历'),
                onPressed: () {
                  Navigator.pushNamed(context, '/calendar');
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChartList(List<ChartData> charts) {
    return ListView.builder(
      itemCount: charts.length,
      itemBuilder: (context, index) {
        final chart = charts[index];
        return Card(
          margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: ListTile(
            title: Text(chart.name.isNotEmpty ? chart.name : '未命名命盘'),
            subtitle: Text(
              '${chart.birthData.year}年${chart.birthData.month}月${chart.birthData.day}日 ${chart.birthData.hour}时'
            ),
            leading: CircleAvatar(
              child: Text(chart.gender == 'male' ? '男' : '女'),
              backgroundColor: chart.gender == 'male' ? Colors.blue : Colors.pink,
            ),
            trailing: Icon(Icons.chevron_right),
            onTap: () {
              Navigator.pushNamed(
                context,
                '/chart',
                arguments: chart.birthData,
              );
            },
            onLongPress: () {
              _showChartOptions(context, chart);
            },
          ),
        );
      },
    );
  }

  void _showUserMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(Icons.person),
                title: Text('个人资料'),
                onTap: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('个人资料功能开发中...')),
                  );
                },
              ),
              ListTile(
                leading: Icon(Icons.card_membership),
                title: Text('会员中心'),
                onTap: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('会员中心功能开发中...')),
                  );
                },
              ),
              ListTile(
                leading: Icon(Icons.auto_awesome),
                title: Text('八字排盘'),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.pushNamed(context, '/bazi');
                },
              ),
              ListTile(
                leading: Icon(Icons.calendar_today),
                title: Text('万年历'),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.pushNamed(context, '/calendar');
                },
              ),
              ListTile(
                leading: Icon(Icons.settings),
                title: Text('设置'),
                onTap: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('设置功能开发中...')),
                  );
                },
              ),
              ListTile(
                leading: Icon(Icons.exit_to_app),
                title: Text('退出登录'),
                onTap: () {
                  Navigator.pop(context);
                  final authService = Provider.of<AuthService>(context, listen: false);
                  authService.logout();
                },
              ),
            ],
          ),
        );
      },
    );
  }
  
  void _showChartOptions(BuildContext context, ChartData chart) {
    final storageService = Provider.of<StorageService>(context, listen: false);

    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(Icons.remove_red_eye),
                title: Text('查看命盘'),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.pushNamed(
                    context,
                    '/chart',
                    arguments: chart.birthData,
                  );
                },
              ),
              ListTile(
                leading: Icon(Icons.auto_stories),
                title: Text('查看解读'),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.pushNamed(
                    context,
                    '/interpretation',
                    arguments: chart,
                  );
                },
              ),
              ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('删除命盘', style: TextStyle(color: Colors.red)),
                onTap: () {
                  Navigator.pop(context);
                  _confirmDelete(context, chart, storageService);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _confirmDelete(
    BuildContext context,
    ChartData chart,
    StorageService storageService,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('删除命盘'),
          content: Text('确定要删除这个命盘吗？此操作不可撤销。'),
          actions: [
            TextButton(
              child: Text('取消'),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
            TextButton(
              child: Text('删除', style: TextStyle(color: Colors.red)),
              onPressed: () async {
                Navigator.pop(context);
                await storageService.deleteChart(chart.id);
                setState(() {
                  _loadCharts();
                });
              },
            ),
          ],
        );
      },
    );
  }
}