// lib/data/ziwei_star_database.dart
// 紫微斗数星曜数据库

import '../models/ziwei_star.dart';

/// 紫微斗数星曜数据库
class ZiweiStarDatabase {
  
  /// 十四主星数据
  static const List<ZiweiStar> mainStars = [
    // 紫微星
    ZiweiStar(
      name: '紫微',
      type: StarType.main,
      element: StarElement.earth,
      polarity: StarPolarity.yang,
      description: '紫微星为北斗主星，帝王之星，主贵',
      characteristics: ['帝王', '尊贵', '领导', '权威', '自尊'],
      meaning: '主管权威、地位、领导能力，为命盘中最尊贵之星',
    ),
    
    // 天机星
    ZiweiStar(
      name: '天机',
      type: StarType.main,
      element: StarElement.wood,
      polarity: StarPolarity.yang,
      description: '天机星主智慧、机变、思考',
      characteristics: ['智慧', '机变', '思考', '计划', '分析'],
      meaning: '主管智慧、策划、思维能力，善于分析和计划',
    ),
    
    // 太阳星
    ZiweiStar(
      name: '太阳',
      type: StarType.main,
      element: StarElement.fire,
      polarity: StarPolarity.yang,
      description: '太阳星主光明、博爱、奉献',
      characteristics: ['光明', '博爱', '奉献', '正直', '热情'],
      meaning: '主管光明正大、博爱精神、奉献精神',
    ),
    
    // 武曲星
    ZiweiStar(
      name: '武曲',
      type: StarType.main,
      element: StarElement.metal,
      polarity: StarPolarity.yang,
      description: '武曲星主财富、决断、行动',
      characteristics: ['财富', '决断', '行动', '勇敢', '直率'],
      meaning: '主管财富、决断力、行动力，为财星之首',
    ),
    
    // 天同星
    ZiweiStar(
      name: '天同',
      type: StarType.main,
      element: StarElement.water,
      polarity: StarPolarity.yang,
      description: '天同星主福德、享受、和谐',
      characteristics: ['福德', '享受', '和谐', '温和', '知足'],
      meaning: '主管福德、享受、和谐，性格温和知足',
    ),
    
    // 廉贞星
    ZiweiStar(
      name: '廉贞',
      type: StarType.main,
      element: StarElement.fire,
      polarity: StarPolarity.yin,
      description: '廉贞星主感情、艺术、变化',
      characteristics: ['感情', '艺术', '变化', '热情', '冲动'],
      meaning: '主管感情、艺术才能、变化，性格热情易冲动',
    ),
    
    // 天府星
    ZiweiStar(
      name: '天府',
      type: StarType.main,
      element: StarElement.earth,
      polarity: StarPolarity.yang,
      description: '天府星主保守、稳重、储蓄',
      characteristics: ['保守', '稳重', '储蓄', '谨慎', '持重'],
      meaning: '主管保守稳重、储蓄理财，性格谨慎持重',
    ),
    
    // 太阴星
    ZiweiStar(
      name: '太阴',
      type: StarType.main,
      element: StarElement.water,
      polarity: StarPolarity.yin,
      description: '太阴星主柔和、内敛、母性',
      characteristics: ['柔和', '内敛', '母性', '细腻', '包容'],
      meaning: '主管柔和内敛、母性光辉，性格细腻包容',
    ),
    
    // 贪狼星
    ZiweiStar(
      name: '贪狼',
      type: StarType.main,
      element: StarElement.wood,
      polarity: StarPolarity.yang,
      description: '贪狼星主欲望、才艺、交际',
      characteristics: ['欲望', '才艺', '交际', '多才', '善变'],
      meaning: '主管欲望、才艺、交际能力，多才多艺善变',
    ),
    
    // 巨门星
    ZiweiStar(
      name: '巨门',
      type: StarType.main,
      element: StarElement.water,
      polarity: StarPolarity.yin,
      description: '巨门星主口才、是非、暗曜',
      characteristics: ['口才', '是非', '暗曜', '疑虑', '深沉'],
      meaning: '主管口才、是非、暗曜，性格疑虑深沉',
    ),
    
    // 天相星
    ZiweiStar(
      name: '天相',
      type: StarType.main,
      element: StarElement.water,
      polarity: StarPolarity.yang,
      description: '天相星主辅助、服务、印绶',
      characteristics: ['辅助', '服务', '印绶', '忠诚', '可靠'],
      meaning: '主管辅助、服务、印绶，性格忠诚可靠',
    ),
    
    // 天梁星
    ZiweiStar(
      name: '天梁',
      type: StarType.main,
      element: StarElement.earth,
      polarity: StarPolarity.yang,
      description: '天梁星主长者、化解、荫庇',
      characteristics: ['长者', '化解', '荫庇', '慈祥', '智慧'],
      meaning: '主管长者风范、化解灾厄、荫庇他人',
    ),
    
    // 七杀星
    ZiweiStar(
      name: '七杀',
      type: StarType.main,
      element: StarElement.metal,
      polarity: StarPolarity.yang,
      description: '七杀星主冲动、开创、孤独',
      characteristics: ['冲动', '开创', '孤独', '勇猛', '独立'],
      meaning: '主管冲动开创、孤独独立，性格勇猛果断',
    ),
    
    // 破军星
    ZiweiStar(
      name: '破军',
      type: StarType.main,
      element: StarElement.water,
      polarity: StarPolarity.yin,
      description: '破军星主破坏、创新、变动',
      characteristics: ['破坏', '创新', '变动', '冲破', '改革'],
      meaning: '主管破坏创新、变动改革，善于冲破现状',
    ),
  ];
  
  /// 辅星数据
  static const List<ZiweiStar> auxiliaryStars = [
    // 左辅星
    ZiweiStar(
      name: '左辅',
      type: StarType.auxiliary,
      element: StarElement.earth,
      polarity: StarPolarity.yang,
      description: '左辅星主辅助、贵人、合作',
      characteristics: ['辅助', '贵人', '合作', '协调', '支持'],
      meaning: '主管辅助贵人、合作协调，能得他人支持',
    ),
    
    // 右弼星
    ZiweiStar(
      name: '右弼',
      type: StarType.auxiliary,
      element: StarElement.earth,
      polarity: StarPolarity.yin,
      description: '右弼星主辅助、贵人、协调',
      characteristics: ['辅助', '贵人', '协调', '平衡', '调和'],
      meaning: '主管辅助协调、平衡调和，善于处理关系',
    ),
    
    // 天魁星
    ZiweiStar(
      name: '天魁',
      type: StarType.auxiliary,
      element: StarElement.fire,
      polarity: StarPolarity.yang,
      description: '天魁星主贵人、提携、机遇',
      characteristics: ['贵人', '提携', '机遇', '显贵', '正统'],
      meaning: '主管贵人提携、机遇显贵，多得正统贵人相助',
    ),
    
    // 天钺星
    ZiweiStar(
      name: '天钺',
      type: StarType.auxiliary,
      element: StarElement.fire,
      polarity: StarPolarity.yin,
      description: '天钺星主贵人、暗助、机缘',
      characteristics: ['贵人', '暗助', '机缘', '隐贵', '暗助'],
      meaning: '主管贵人暗助、机缘隐贵，多得暗中贵人相助',
    ),
    
    // 禄存星
    ZiweiStar(
      name: '禄存',
      type: StarType.auxiliary,
      element: StarElement.earth,
      polarity: StarPolarity.yang,
      description: '禄存星主财禄、储蓄、保守',
      characteristics: ['财禄', '储蓄', '保守', '稳定', '积累'],
      meaning: '主管财禄储蓄、保守稳定，善于积累财富',
    ),
    
    // 天马星
    ZiweiStar(
      name: '天马',
      type: StarType.auxiliary,
      element: StarElement.fire,
      polarity: StarPolarity.yang,
      description: '天马星主动态、变迁、奔波',
      characteristics: ['动态', '变迁', '奔波', '活跃', '变动'],
      meaning: '主管动态变迁、奔波活跃，喜欢变动和活动',
    ),
    
    // 擎羊星
    ZiweiStar(
      name: '擎羊',
      type: StarType.auxiliary,
      element: StarElement.metal,
      polarity: StarPolarity.yang,
      description: '擎羊星主刑克、冲动、竞争',
      characteristics: ['刑克', '冲动', '竞争', '锐利', '争斗'],
      meaning: '主管刑克冲动、竞争争斗，性格锐利好胜',
    ),
    
    // 陀罗星
    ZiweiStar(
      name: '陀罗',
      type: StarType.auxiliary,
      element: StarElement.metal,
      polarity: StarPolarity.yin,
      description: '陀罗星主阻碍、延迟、纠缠',
      characteristics: ['阻碍', '延迟', '纠缠', '拖延', '困扰'],
      meaning: '主管阻碍延迟、纠缠困扰，容易遇到拖延',
    ),
    
    // 火星
    ZiweiStar(
      name: '火星',
      type: StarType.auxiliary,
      element: StarElement.fire,
      polarity: StarPolarity.yang,
      description: '火星主急躁、冲动、暴烈',
      characteristics: ['急躁', '冲动', '暴烈', '快速', '激烈'],
      meaning: '主管急躁冲动、暴烈激烈，性格急躁易怒',
    ),
    
    // 铃星
    ZiweiStar(
      name: '铃星',
      type: StarType.auxiliary,
      element: StarElement.fire,
      polarity: StarPolarity.yin,
      description: '铃星主暗火、内燃、隐忍',
      characteristics: ['暗火', '内燃', '隐忍', '内敛', '压抑'],
      meaning: '主管暗火内燃、隐忍压抑，内心容易焦虑',
    ),
    
    // 地空星
    ZiweiStar(
      name: '地空',
      type: StarType.auxiliary,
      element: StarElement.fire,
      polarity: StarPolarity.yang,
      description: '地空星主空虚、理想、精神',
      characteristics: ['空虚', '理想', '精神', '虚幻', '超脱'],
      meaning: '主管空虚理想、精神超脱，容易有虚幻想法',
    ),
    
    // 地劫星
    ZiweiStar(
      name: '地劫',
      type: StarType.auxiliary,
      element: StarElement.fire,
      polarity: StarPolarity.yin,
      description: '地劫星主破耗、损失、劫难',
      characteristics: ['破耗', '损失', '劫难', '消耗', '破财'],
      meaning: '主管破耗损失、劫难破财，容易有意外损失',
    ),
  ];
  
  /// 杂曜数据
  static const List<ZiweiStar> miscellaneousStars = [
    // 文昌星
    ZiweiStar(
      name: '文昌',
      type: StarType.miscellaneous,
      element: StarElement.metal,
      polarity: StarPolarity.yang,
      description: '文昌星主文学、考试、功名',
      characteristics: ['文学', '考试', '功名', '才华', '学问'],
      meaning: '主管文学才华、考试功名，利于学业和文职',
    ),
    
    // 文曲星
    ZiweiStar(
      name: '文曲',
      type: StarType.miscellaneous,
      element: StarElement.water,
      polarity: StarPolarity.yin,
      description: '文曲星主艺术、口才、技艺',
      characteristics: ['艺术', '口才', '技艺', '灵巧', '变通'],
      meaning: '主管艺术技艺、口才变通，多才多艺灵巧',
    ),
    
    // 天姚星
    ZiweiStar(
      name: '天姚',
      type: StarType.miscellaneous,
      element: StarElement.water,
      polarity: StarPolarity.yin,
      description: '天姚星主桃花、魅力、异性缘',
      characteristics: ['桃花', '魅力', '异性缘', '感情', '诱惑'],
      meaning: '主管桃花魅力、异性缘分，容易有感情纠葛',
    ),
    
    // 红鸾星
    ZiweiStar(
      name: '红鸾',
      type: StarType.miscellaneous,
      element: StarElement.water,
      polarity: StarPolarity.yin,
      description: '红鸾星主婚姻、喜庆、桃花',
      characteristics: ['婚姻', '喜庆', '桃花', '结合', '美满'],
      meaning: '主管婚姻喜庆、桃花结合，利于婚姻感情',
    ),
    
    // 天喜星
    ZiweiStar(
      name: '天喜',
      type: StarType.miscellaneous,
      element: StarElement.water,
      polarity: StarPolarity.yang,
      description: '天喜星主喜庆、婚嫁、吉庆',
      characteristics: ['喜庆', '婚嫁', '吉庆', '欢乐', '庆祝'],
      meaning: '主管喜庆婚嫁、吉庆欢乐，多有喜事临门',
    ),
    
    // 孤辰星
    ZiweiStar(
      name: '孤辰',
      type: StarType.miscellaneous,
      element: StarElement.fire,
      polarity: StarPolarity.yang,
      description: '孤辰星主孤独、独立、清高',
      characteristics: ['孤独', '独立', '清高', '孤僻', '超然'],
      meaning: '主管孤独独立、清高超然，性格孤僻不群',
    ),
    
    // 寡宿星
    ZiweiStar(
      name: '寡宿',
      type: StarType.miscellaneous,
      element: StarElement.fire,
      polarity: StarPolarity.yin,
      description: '寡宿星主孤寡、独处、清静',
      characteristics: ['孤寡', '独处', '清静', '寂寞', '独身'],
      meaning: '主管孤寡独处、清静寂寞，容易独身或寡居',
    ),
  ];
  
  /// 获取所有星曜
  static List<ZiweiStar> getAllStars() {
    return [...mainStars, ...auxiliaryStars, ...miscellaneousStars];
  }
  
  /// 根据名称查找星曜
  static ZiweiStar? findStarByName(String name) {
    try {
      return getAllStars().firstWhere((star) => star.name == name);
    } catch (e) {
      return null;
    }
  }
  
  /// 获取指定类型的星曜
  static List<ZiweiStar> getStarsByType(StarType type) {
    return getAllStars().where((star) => star.type == type).toList();
  }
  
  /// 获取指定五行的星曜
  static List<ZiweiStar> getStarsByElement(StarElement element) {
    return getAllStars().where((star) => star.element == element).toList();
  }

  /// 星曜亮度表数据
  static const Map<String, List<StarBrightness>> starBrightnessTable = {
    '紫微': [
      StarBrightness.temple,     // 寅宫
      StarBrightness.prosperous, // 卯宫
      StarBrightness.favorable,  // 辰宫
      StarBrightness.favorable,  // 巳宫
      StarBrightness.temple,     // 午宫
      StarBrightness.prosperous, // 未宫
      StarBrightness.favorable,  // 申宫
      StarBrightness.favorable,  // 酉宫
      StarBrightness.temple,     // 戌宫
      StarBrightness.prosperous, // 亥宫
      StarBrightness.favorable,  // 子宫
      StarBrightness.favorable,  // 丑宫
    ],
    '天机': [
      StarBrightness.favorable,  // 寅宫
      StarBrightness.temple,     // 卯宫
      StarBrightness.prosperous, // 辰宫
      StarBrightness.trapped,    // 巳宫
      StarBrightness.fallen,     // 午宫
      StarBrightness.trapped,    // 未宫
      StarBrightness.favorable,  // 申宫
      StarBrightness.temple,     // 酉宫
      StarBrightness.prosperous, // 戌宫
      StarBrightness.favorable,  // 亥宫
      StarBrightness.temple,     // 子宫
      StarBrightness.prosperous, // 丑宫
    ],
    '太阳': [
      StarBrightness.favorable,  // 寅宫
      StarBrightness.temple,     // 卯宫
      StarBrightness.temple,     // 辰宫
      StarBrightness.temple,     // 巳宫
      StarBrightness.temple,     // 午宫
      StarBrightness.prosperous, // 未宫
      StarBrightness.favorable,  // 申宫
      StarBrightness.trapped,    // 酉宫
      StarBrightness.fallen,     // 戌宫
      StarBrightness.fallen,     // 亥宫
      StarBrightness.fallen,     // 子宫
      StarBrightness.trapped,    // 丑宫
    ],
    // 其他星曜的亮度表可以继续添加...
  };

  /// 获取星曜在指定宫位的亮度
  static StarBrightness getStarBrightness(String starName, int palaceIndex) {
    var brightnessData = starBrightnessTable[starName];
    if (brightnessData != null && palaceIndex >= 0 && palaceIndex < 12) {
      return brightnessData[palaceIndex];
    }
    return StarBrightness.neutral; // 默认亮度
  }
}
