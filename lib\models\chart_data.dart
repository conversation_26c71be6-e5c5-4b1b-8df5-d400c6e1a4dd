// lib/models/chart_data.dart
import 'birth_data.dart';
import 'palace.dart';

class ChartData {
  final String id;
  final String name;
  final String gender;
  final BirthData birthData;
  final int ascendant; // 命宫地支索引
  final int bodyPalace; // 身宫地支索引
  final List<Palace> palaces; // 十二宫位
  final DateTime createdAt;
  final Map<String, dynamic>? paipanData; // 排盘数据

  ChartData({
    required this.id,
    required this.name,
    required this.gender,
    required this.birthData,
    required this.ascendant,
    required this.bodyPalace,
    required this.palaces,
    required this.createdAt,
    this.paipanData,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'gender': gender,
      'birthData': birthData.toJson(),
      'ascendant': ascendant,
      'bodyPalace': bodyPalace,
      'palaces': palaces.map((p) => p.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'paipanData': paipanData,
    };
  }

  factory ChartData.fromJson(Map<String, dynamic> json) {
    return ChartData(
      id: json['id'] as String,
      name: json['name'] as String,
      gender: json['gender'] as String,
      birthData: BirthData.fromJson(json['birthData'] as Map<String, dynamic>),
      ascendant: json['ascendant'] as int,
      bodyPalace: json['bodyPalace'] as int,
      palaces: (json['palaces'] as List)
          .map((p) => Palace.fromJson(p as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      paipanData: json['paipanData'] as Map<String, dynamic>?,
    );
  }
}


