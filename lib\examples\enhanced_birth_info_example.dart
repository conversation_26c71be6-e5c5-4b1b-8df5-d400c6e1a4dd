// lib/examples/enhanced_birth_info_example.dart
// 增强的出生信息界面使用示例

import 'package:flutter/material.dart';
import '../screens/input/birth_info_screen.dart';

/// 增强的出生信息界面示例
/// 展示了集成真假太阳时转换和天干地支计算的功能
class EnhancedBirthInfoExample {
  
  /// 功能特点说明
  static const List<String> features = [
    '实时计算四柱干支（年月日时）',
    '真假太阳时对比显示',
    '时辰详细信息（五行、方位、养生）',
    '阳历阴历自动转换',
    '地理位置影响的时差计算',
    '时辰边界的精确判断',
  ];
  
  /// 界面组件说明
  static const Map<String, String> components = {
    '基本信息卡片': '姓名和性别选择',
    '出生日期卡片': '支持公历/农历切换，实时显示对应历法',
    '出生时辰卡片': '时间选择，显示当前时辰信息',
    '出生地点卡片': '地点输入，影响真太阳时计算',
    '天干地支信息切换': '控制是否显示详细计算信息',
    '四柱干支卡片': '实时显示年月日时干支和历法信息',
    '真假太阳时对比卡片': '显示平太阳时、真太阳时和时差',
    '时辰详情卡片': '显示时辰的五行、方位、描述等详细信息',
  };
  
  /// 计算逻辑说明
  static const Map<String, String> calculations = {
    '四柱干支计算': '基于精确的天文算法，考虑真太阳时影响',
    '真假太阳时转换': '根据地理经度和时差方程进行精确计算',
    '时辰划分': '使用真太阳时进行时辰边界判断',
    '阳历阴历转换': '支持1900-2100年的精确历法转换',
    '地理位置修正': '根据经纬度计算地方时差',
    '实时更新': '任何输入变化都会触发重新计算',
  };
  
  /// 使用场景
  static const List<String> useCases = [
    '紫微斗数排盘：精确的时辰计算确保排盘准确性',
    '八字命理：完整的四柱干支信息',
    '择日择时：基于真太阳时的时辰选择',
    '传统历法研究：阳历阴历对比',
    '天文计算：真假太阳时差异分析',
    '中医养生：时辰对应的脏腑和养生建议',
  ];
  
  /// 技术优势
  static const List<String> advantages = [
    '精确性：基于天文算法，考虑地球轨道偏心率',
    '实时性：输入变化立即更新计算结果',
    '完整性：涵盖时间计算的各个方面',
    '易用性：直观的界面设计，信息层次清晰',
    '扩展性：模块化设计，易于添加新功能',
    '兼容性：保持与原有功能的完全兼容',
  ];
  
  /// 界面使用指南
  static void showUsageGuide(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('增强功能使用指南'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildSection('功能特点', features),
              SizedBox(height: 16),
              _buildSection('使用步骤', [
                '1. 填写基本信息（姓名、性别）',
                '2. 选择出生日期（公历或农历）',
                '3. 设置出生时间（精确到分钟）',
                '4. 输入出生地点（影响真太阳时）',
                '5. 开启"天干地支信息"查看详细计算',
                '6. 查看实时更新的四柱干支信息',
                '7. 对比真假太阳时差异',
                '8. 了解时辰的详细属性信息',
              ]),
              SizedBox(height: 16),
              _buildSection('注意事项', [
                '• 时间精度：建议精确到分钟以获得准确结果',
                '• 地理位置：提供准确经纬度可提高计算精度',
                '• 时辰边界：在时辰交界时间要特别注意',
                '• 农历闰月：注意正确选择是否为闰月',
                '• 年份范围：农历转换支持1900-2100年',
              ]),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('了解了'),
          ),
        ],
      ),
    );
  }
  
  static Widget _buildSection(String title, List<String> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8),
        ...items.map((item) => Padding(
          padding: EdgeInsets.only(left: 8, bottom: 4),
          child: Text(
            item,
            style: TextStyle(fontSize: 14),
          ),
        )),
      ],
    );
  }
  
  /// 创建增强的出生信息界面
  static Widget createEnhancedBirthInfoScreen() {
    return BirthInfoScreen();
  }
  
  /// 功能演示说明
  static void showFeatureDemo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('功能演示'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '实时计算演示：',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('1. 修改出生时间，观察时辰变化'),
              Text('2. 切换公历/农历，查看历法转换'),
              Text('3. 调整地点，观察真太阳时差异'),
              Text('4. 开启高级信息，查看详细计算'),
              SizedBox(height: 16),
              Text(
                '计算精度验证：',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• 四柱干支符合传统算法'),
              Text('• 真假太阳时差异准确'),
              Text('• 时辰划分精确到分钟'),
              Text('• 农历转换经过验证'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('开始体验'),
          ),
        ],
      ),
    );
  }
  
  /// 获取功能说明文档
  static String getDocumentation() {
    return '''
# 增强的出生信息界面

## 概述
集成了真假太阳时转换和天干地支计算功能的出生信息输入界面，为紫微斗数排盘提供精确的时间和干支计算基础。

## 主要功能
${features.map((f) => '- $f').join('\n')}

## 界面组件
${components.entries.map((e) => '- ${e.key}：${e.value}').join('\n')}

## 计算逻辑
${calculations.entries.map((e) => '- ${e.key}：${e.value}').join('\n')}

## 应用场景
${useCases.map((u) => '- $u').join('\n')}

## 技术优势
${advantages.map((a) => '- $a').join('\n')}

## 使用建议
1. 确保输入信息的准确性，特别是时间和地点
2. 在时辰边界时间要特别注意真假太阳时的差异
3. 利用实时计算功能验证输入的正确性
4. 参考时辰详细信息了解更多背景知识

这个增强的界面为传统命理应用提供了现代化的技术支持，确保了计算的准确性和用户体验的友好性。
''';
  }
}
