// lib/utils/hour_calculator.dart
import '../services/paipan_service.dart';
import 'solar_time_converter.dart';

/// 时辰计算工具类
/// 支持真太阳时的精确时辰划分
/// 重构后与 PaipanService 保持一致
class HourCalculator {
  static final PaipanService _paipanService = PaipanService();
  
  // 十二时辰名称
  static const List<String> hourNames = [
    '子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'
  ];
  
  // 时辰对应的现代时间范围
  static const List<String> hourRanges = [
    '23:00-01:00', '01:00-03:00', '03:00-05:00', '05:00-07:00',
    '07:00-09:00', '09:00-11:00', '11:00-13:00', '13:00-15:00',
    '15:00-17:00', '17:00-19:00', '19:00-21:00', '21:00-23:00'
  ];
  
  // 时辰的详细信息
  static const List<Map<String, dynamic>> hourDetails = [
    {
      'name': '子时',
      'range': '23:00-01:00',
      'element': '水',
      'direction': '北',
      'season': '冬',
      'description': '夜半，又名子夜、中夜',
      'organs': ['胆'],
      'activity': '深度睡眠时间'
    },
    {
      'name': '丑时',
      'range': '01:00-03:00',
      'element': '土',
      'direction': '东北',
      'season': '冬',
      'description': '鸡鸣，又名荒鸡',
      'organs': ['肝'],
      'activity': '肝脏排毒时间'
    },
    {
      'name': '寅时',
      'range': '03:00-05:00',
      'element': '木',
      'direction': '东北',
      'season': '春',
      'description': '平旦，又名黎明、早晨、日旦',
      'organs': ['肺'],
      'activity': '肺部调养时间'
    },
    {
      'name': '卯时',
      'range': '05:00-07:00',
      'element': '木',
      'direction': '东',
      'season': '春',
      'description': '日出，又名日始、破晓、旭日',
      'organs': ['大肠'],
      'activity': '大肠排毒时间'
    },
    {
      'name': '辰时',
      'range': '07:00-09:00',
      'element': '土',
      'direction': '东南',
      'season': '春',
      'description': '食时，又名早食',
      'organs': ['胃'],
      'activity': '胃部消化时间'
    },
    {
      'name': '巳时',
      'range': '09:00-11:00',
      'element': '火',
      'direction': '东南',
      'season': '夏',
      'description': '隅中，又名日禺',
      'organs': ['脾'],
      'activity': '脾脏运化时间'
    },
    {
      'name': '午时',
      'range': '11:00-13:00',
      'element': '火',
      'direction': '南',
      'season': '夏',
      'description': '日中，又名日正、中午',
      'organs': ['心'],
      'activity': '心脏活跃时间'
    },
    {
      'name': '未时',
      'range': '13:00-15:00',
      'element': '土',
      'direction': '西南',
      'season': '夏',
      'description': '日昳，又名日跌、日央',
      'organs': ['小肠'],
      'activity': '小肠吸收时间'
    },
    {
      'name': '申时',
      'range': '15:00-17:00',
      'element': '金',
      'direction': '西南',
      'season': '秋',
      'description': '晡时，又名日铺、夕食',
      'organs': ['膀胱'],
      'activity': '膀胱储尿时间'
    },
    {
      'name': '酉时',
      'range': '17:00-19:00',
      'element': '金',
      'direction': '西',
      'season': '秋',
      'description': '日入，又名日落、日沉、傍晚',
      'organs': ['肾'],
      'activity': '肾脏调养时间'
    },
    {
      'name': '戌时',
      'range': '19:00-21:00',
      'element': '土',
      'direction': '西北',
      'season': '秋',
      'description': '黄昏，又名日夕、日暮、日晚',
      'organs': ['心包'],
      'activity': '心包经活跃时间'
    },
    {
      'name': '亥时',
      'range': '21:00-23:00',
      'element': '水',
      'direction': '西北',
      'season': '冬',
      'description': '人定，又名定昏',
      'organs': ['三焦'],
      'activity': '三焦通百脉时间'
    },
  ];
  
  /// 根据平太阳时计算时辰（不考虑地理位置）
  /// [dateTime] 平太阳时
  /// 返回时辰索引 (0-11)
  static int getHourIndexByMeanTime(DateTime dateTime) {
    int hour = dateTime.hour;
    int minute = dateTime.minute;
    
    // 将时间转换为分钟数
    int totalMinutes = hour * 60 + minute;
    
    // 子时从23:00开始，到次日1:00结束
    if (totalMinutes >= 23 * 60) {
      return 0; // 子时
    } else {
      // 1:00-23:00 按正常计算
      return ((totalMinutes + 60) ~/ 120) % 12;
    }
  }
  
  /// 根据真太阳时计算时辰（考虑地理位置）
  /// [dateTime] 平太阳时
  /// [longitude] 经度（东经为正，西经为负）
  /// 返回时辰索引 (0-11)
  static int getHourIndexByApparentTime(DateTime dateTime, double longitude) {
    // 转换为真太阳时
    DateTime apparentTime = SolarTimeConverter.meanToApparentSolarTime(dateTime, longitude);
    
    return SolarTimeConverter.getHourIndex(apparentTime);
  }
  
  /// 获取时辰名称
  /// [hourIndex] 时辰索引 (0-11)
  static String getHourName(int hourIndex) {
    return hourNames[hourIndex % 12];
  }
  
  /// 获取时辰的现代时间范围
  /// [hourIndex] 时辰索引 (0-11)
  static String getHourRange(int hourIndex) {
    return hourRanges[hourIndex % 12];
  }
  
  /// 获取时辰的详细信息
  /// [hourIndex] 时辰索引 (0-11)
  static Map<String, dynamic> getHourDetails(int hourIndex) {
    return Map<String, dynamic>.from(hourDetails[hourIndex % 12]);
  }
  
  /// 获取所有时辰信息
  static List<Map<String, dynamic>> getAllHourDetails() {
    return hourDetails.map((hour) => Map<String, dynamic>.from(hour)).toList();
  }
  
  /// 判断指定时间是否在某个时辰内
  /// [dateTime] 要判断的时间
  /// [hourIndex] 时辰索引
  /// [longitude] 经度（可选，用于真太阳时计算）
  static bool isInHour(DateTime dateTime, int hourIndex, {double? longitude}) {
    int actualHourIndex;
    
    if (longitude != null) {
      actualHourIndex = getHourIndexByApparentTime(dateTime, longitude);
    } else {
      actualHourIndex = getHourIndexByMeanTime(dateTime);
    }
    
    return actualHourIndex == hourIndex;
  }
  
  /// 获取指定时辰的开始时间
  /// [date] 日期
  /// [hourIndex] 时辰索引
  /// [longitude] 经度（可选，用于真太阳时计算）
  static DateTime getHourStartTime(DateTime date, int hourIndex, {double? longitude}) {
    // 计算时辰开始的小时数
    int startHour;
    if (hourIndex == 0) {
      startHour = 23; // 子时从前一天23:00开始
      date = date.subtract(Duration(days: 1));
    } else {
      startHour = hourIndex * 2 - 1;
    }
    
    DateTime startTime = DateTime(date.year, date.month, date.day, startHour, 0);
    
    // 如果需要考虑真太阳时，进行转换
    if (longitude != null) {
      startTime = SolarTimeConverter.apparentToMeanSolarTime(startTime, longitude);
    }
    
    return startTime;
  }
  
  /// 获取指定时辰的结束时间
  /// [date] 日期
  /// [hourIndex] 时辰索引
  /// [longitude] 经度（可选，用于真太阳时计算）
  static DateTime getHourEndTime(DateTime date, int hourIndex, {double? longitude}) {
    // 计算时辰结束的小时数
    int endHour;
    if (hourIndex == 0) {
      endHour = 1; // 子时到次日1:00结束
      date = date.add(Duration(days: 1));
    } else {
      endHour = hourIndex * 2 + 1;
    }
    
    DateTime endTime = DateTime(date.year, date.month, date.day, endHour, 0);
    
    // 如果需要考虑真太阳时，进行转换
    if (longitude != null) {
      endTime = SolarTimeConverter.apparentToMeanSolarTime(endTime, longitude);
    }
    
    return endTime;
  }
  
  /// 计算两个时辰之间的差值
  /// [fromHour] 起始时辰索引
  /// [toHour] 结束时辰索引
  /// 返回时辰差值（可能为负数）
  static int getHourDifference(int fromHour, int toHour) {
    int diff = toHour - fromHour;

    // 处理跨日的情况
    if (diff > 6) {
      diff -= 12;
    } else if (diff < -6) {
      diff += 12;
    }

    return diff;
  }

  /// 使用 PaipanService 获取时辰详细信息
  /// [hour] 小时 (0-23)
  /// [minute] 分钟 (0-59)
  /// 返回时辰信息
  static Map<String, dynamic> getHourInfoFromPaipan(int hour, int minute) {
    return _paipanService.getCTPart(hour, minute);
  }

  /// 获取地支名称（与 PaipanService 保持一致）
  /// [hourIndex] 时辰索引 (0-11)
  static String getEarthlyBranch(int hourIndex) {
    return _paipanService.cdz[hourIndex % 12];
  }
}
