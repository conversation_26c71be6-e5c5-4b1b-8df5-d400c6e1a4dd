// lib/utils/calendar_converter.dart
import '../services/paipan_service.dart';
import 'solar_time_converter.dart';
import 'hour_calculator.dart';

/// 历法转换工具类
/// 重构后统一使用 PaipanService 进行所有计算
class CalendarConverter {
  static final PaipanService _paipanService = PaipanService();

  // 使用统一的排盘服务进行农历转公历
  static DateTime lunarToSolar(int year, int month, int day, bool isLeapMonth) {
    List<int>? result = _paipanService.lunar2Solar(year, month, day, isLeapMonth);
    if (result != null && result.length >= 3) {
      return DateTime(result[0], result[1], result[2]);
    }
    // 如果转换失败，返回默认值
    return DateTime(year, month, day);
  }

  // 使用统一的排盘服务进行公历转农历
  static Map<String, dynamic> solarToLunar(DateTime solarDate) {
    List<int>? result = _paipanService.solar2Lunar(solarDate.year, solarDate.month, solarDate.day);
    if (result != null && result.length >= 4) {
      return {
        'year': result[0],
        'month': result[1],
        'day': result[2],
        'isLeapMonth': result[3] == 1,
      };
    }
    // 如果转换失败，返回默认值
    return {
      'year': solarDate.year,
      'month': solarDate.month,
      'day': solarDate.day,
      'isLeapMonth': false,
    };
  }


  // 获取天干（使用统一的排盘服务）
  static String getHeavenlyStem(int year) {
    Map<String, dynamic> ganZhiResult = _paipanService.getGanZhi(year, 1, 1, 12, 0, 0);
    if (ganZhiResult.isNotEmpty) {
      List<int> tg = ganZhiResult['tg'];
      return _paipanService.ctg[tg[0]];
    }
    return '';
  }

  // 获取地支（使用统一的排盘服务）
  static String getEarthlyBranch(int year) {
    Map<String, dynamic> ganZhiResult = _paipanService.getGanZhi(year, 1, 1, 12, 0, 0);
    if (ganZhiResult.isNotEmpty) {
      List<int> dz = ganZhiResult['dz'];
      return _paipanService.cdz[dz[0]];
    }
    return '';
  }

  // 获取干支纪年（使用统一的排盘服务）
  static String getStemBranchYear(int year) {
    return getHeavenlyStem(year) + getEarthlyBranch(year);
  }

  // 获取时辰名称（使用新的计算器）
  static String getHourName(int hour) {
    return HourCalculator.getHourName(hour);
  }

  // 获取现代时间范围（使用新的计算器）
  static String getModernHourRange(int hourIndex) {
    return HourCalculator.getHourRange(hourIndex);
  }

  // 获取中文年份
  static String getChineseYear(int year) {
    return getStemBranchYear(year);
  }

  // === 新增功能：真假太阳时转换 ===

  /// 将平太阳时转换为真太阳时
  /// [dateTime] 平太阳时
  /// [longitude] 经度（东经为正，西经为负）
  static DateTime meanToApparentSolarTime(DateTime dateTime, double longitude) {
    return SolarTimeConverter.meanToApparentSolarTime(dateTime, longitude);
  }

  /// 将真太阳时转换为平太阳时
  /// [dateTime] 真太阳时
  /// [longitude] 经度（东经为正，西经为负）
  static DateTime apparentToMeanSolarTime(DateTime dateTime, double longitude) {
    return SolarTimeConverter.apparentToMeanSolarTime(dateTime, longitude);
  }

  /// 根据真太阳时计算时辰
  /// [dateTime] 平太阳时
  /// [longitude] 经度
  static int getHourIndexByApparentTime(DateTime dateTime, double longitude) {
    return HourCalculator.getHourIndexByApparentTime(dateTime, longitude);
  }

  /// 根据平太阳时计算时辰
  /// [dateTime] 平太阳时
  static int getHourIndexByMeanTime(DateTime dateTime) {
    return HourCalculator.getHourIndexByMeanTime(dateTime);
  }

  // === 新增功能：完整的天干地支计算 ===

  /// 获取完整的四柱干支（年月日时）
  /// [solarDate] 公历日期时间
  /// [longitude] 经度（可选，用于真太阳时计算）
  static Map<String, String> getFourPillars(DateTime solarDate, {double? longitude}) {
    // 使用统一的排盘服务计算四柱
    Map<String, dynamic> ganZhiResult = _paipanService.getGanZhi(
      solarDate.year,
      solarDate.month,
      solarDate.day,
      solarDate.hour,
      solarDate.minute,
      solarDate.second
    );

    if (ganZhiResult.isNotEmpty) {
      List<int> tg = ganZhiResult['tg'];
      List<int> dz = ganZhiResult['dz'];

      return {
        'year': _paipanService.ctg[tg[0]] + _paipanService.cdz[dz[0]],
        'month': _paipanService.ctg[tg[1]] + _paipanService.cdz[dz[1]],
        'day': _paipanService.ctg[tg[2]] + _paipanService.cdz[dz[2]],
        'hour': _paipanService.ctg[tg[3]] + _paipanService.cdz[dz[3]],
      };
    }

    return {};
  }

  /// 获取年干支
  static String getYearStemBranch(int year) {
    return getStemBranchYear(year);
  }

  /// 获取月干支（使用统一的排盘服务）
  /// [year] 公历年份
  /// [lunarMonth] 农历月份
  static String getMonthStemBranch(int year, int lunarMonth) {
    Map<String, dynamic> ganZhiResult = _paipanService.getGanZhi(year, lunarMonth, 15, 12, 0, 0);
    if (ganZhiResult.isNotEmpty) {
      List<int> tg = ganZhiResult['tg'];
      List<int> dz = ganZhiResult['dz'];
      return _paipanService.ctg[tg[1]] + _paipanService.cdz[dz[1]];
    }
    return '';
  }

  /// 获取日干支（使用统一的排盘服务）
  /// [date] 日期
  static String getDayStemBranch(DateTime date) {
    Map<String, dynamic> ganZhiResult = _paipanService.getGanZhi(date.year, date.month, date.day, 12, 0, 0);
    if (ganZhiResult.isNotEmpty) {
      List<int> tg = ganZhiResult['tg'];
      List<int> dz = ganZhiResult['dz'];
      return _paipanService.ctg[tg[2]] + _paipanService.cdz[dz[2]];
    }
    return '';
  }

  /// 获取时干支（使用统一的排盘服务）
  /// [date] 包含时间的日期
  /// [hourIndex] 时辰索引
  static String getHourStemBranch(DateTime date, int hourIndex) {
    Map<String, dynamic> ganZhiResult = _paipanService.getGanZhi(date.year, date.month, date.day, date.hour, date.minute, 0);
    if (ganZhiResult.isNotEmpty) {
      List<int> tg = ganZhiResult['tg'];
      List<int> dz = ganZhiResult['dz'];
      return _paipanService.ctg[tg[3]] + _paipanService.cdz[dz[3]];
    }
    return '';
  }

  // === 新增功能：农历相关 ===

  /// 获取农历月份的中文名称
  static String getLunarMonthName(int month, bool isLeapMonth) {
    const monthNames = [
      '', '正月', '二月', '三月', '四月', '五月', '六月',
      '七月', '八月', '九月', '十月', '冬月', '腊月'
    ];

    if (month < 1 || month > 12) return '未知月';

    String name = monthNames[month];
    return isLeapMonth ? '闰$name' : name;
  }

  /// 获取农历日期的中文名称
  static String getLunarDayName(int day) {
    const dayNames = [
      '', '初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
      '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
      '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'
    ];

    if (day < 1 || day > 30) return '未知日';
    return dayNames[day];
  }

  // === 新增功能：时辰详细信息 ===

  /// 获取时辰的详细信息
  /// [hourIndex] 时辰索引
  static Map<String, dynamic> getHourDetails(int hourIndex) {
    return HourCalculator.getHourDetails(hourIndex);
  }

  /// 获取所有时辰信息
  static List<Map<String, dynamic>> getAllHourDetails() {
    return HourCalculator.getAllHourDetails();
  }
}


