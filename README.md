# 紫微命盘应用

这是一个基于 Flutter 开发的紫微斗数命盘应用，提供命盘计算、展示和解读功能。

## 功能特性

- 📊 **命盘计算**：支持公历/农历日期输入，自动计算紫微命盘
- 🎯 **命盘展示**：直观的十二宫位展示，包含主星、辅星、化星信息
- 📖 **智能解读**：提供基础模板解读和AI生成解读
- 👤 **用户系统**：支持用户注册、登录，命盘数据云端同步
- 💾 **本地存储**：命盘数据本地保存，支持离线查看

## 安装要求

- Flutter SDK 3.0+
- Dart 2.17+
- Android Studio / VS Code
- Android 设备或模拟器 / iOS 设备或模拟器

## 安装步骤

### 1. 安装 Flutter SDK

如果您还没有安装 Flutter，请按照以下步骤：

1. 访问 [Flutter 官网](https://flutter.dev/docs/get-started/install)
2. 下载适合您操作系统的 Flutter SDK
3. 解压到合适的目录（如 `C:\flutter`）
4. 将 Flutter bin 目录添加到系统 PATH 环境变量
5. 运行 `flutter doctor` 检查安装状态

### 2. 克隆项目

```bash
git clone <repository-url>
cd ziwei_reader
```

### 3. 安装依赖

```bash
flutter pub get
```

### 4. 运行应用

```bash
# 在 Android 设备/模拟器上运行
flutter run

# 或者指定平台
flutter run -d android
flutter run -d ios
```

## 项目结构

```
lib/
├── main.dart                 # 应用入口
├── models/                   # 数据模型
│   ├── birth_data.dart      # 出生信息模型
│   ├── chart_data.dart      # 命盘数据模型
│   ├── location.dart        # 位置信息模型
│   ├── palace.dart          # 宫位模型
│   ├── interpretation.dart  # 解读模型
│   └── user.dart           # 用户模型
├── screens/                 # 界面
│   ├── auth/               # 认证界面
│   ├── home/               # 主页
│   ├── input/              # 信息输入
│   ├── chart/              # 命盘展示
│   └── interpretation/     # 解读界面
├── services/               # 服务层
│   ├── auth_service.dart   # 认证服务
│   ├── chart_service.dart  # 命盘服务
│   ├── storage_service.dart # 存储服务
│   ├── ai_service.dart     # AI服务
│   └── api_service.dart    # API服务
├── widgets/                # 自定义组件
└── utils/                  # 工具函数
```

## 使用说明

1. **注册/登录**：首次使用需要注册账号
2. **创建命盘**：输入出生信息（姓名、性别、日期、时间、地点）
3. **查看命盘**：浏览十二宫位的星曜分布
4. **获取解读**：查看命盘的详细解读分析

## 开发说明

### 主要依赖

- `provider`: 状态管理
- `http`: 网络请求
- `shared_preferences`: 本地存储
- `geolocator`: 位置服务
- `geocoding`: 地理编码

### 开发环境设置

1. 确保 Flutter 环境正确配置
2. 使用 VS Code 或 Android Studio 开发
3. 安装 Flutter 和 Dart 插件

### 调试

```bash
# 运行调试模式
flutter run --debug

# 查看日志
flutter logs

# 热重载
按 'r' 键进行热重载
按 'R' 键进行热重启
```

## 注意事项

- 本应用目前为演示版本，AI解读功能需要配置相应的API服务
- 位置服务需要相应的权限配置
- 生产环境部署前需要配置正确的API端点

## 许可证

本项目仅供学习和演示使用。
