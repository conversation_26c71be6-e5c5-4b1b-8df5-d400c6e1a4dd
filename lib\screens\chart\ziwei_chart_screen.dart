// lib/screens/chart/ziwei_chart_screen.dart
// 紫微斗数命盘显示界面

import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../models/ziwei_chart.dart';
import '../../models/ziwei_palace.dart';
import '../../models/ziwei_star.dart';
import '../../models/ziwei_star.dart';

class ZiweiChartScreen extends StatefulWidget {
  final ZiweiChart chart;

  const ZiweiChartScreen({Key? key, required this.chart}) : super(key: key);

  @override
  _ZiweiChartScreenState createState() => _ZiweiChartScreenState();
}

class _ZiweiChartScreenState extends State<ZiweiChartScreen> {
  int _selectedPalaceIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.chart.name} - 紫微斗数命盘'),
        backgroundColor: Colors.purple[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: Icon(Icons.info_outline),
            onPressed: _showChartInfo,
          ),
        ],
      ),
      body: Column(
        children: [
          // 命盘基本信息
          _buildChartHeader(),
          
          // 十二宫位圆盘
          Expanded(
            flex: 3,
            child: _buildPalaceCircle(),
          ),
          
          // 选中宫位详情
          Expanded(
            flex: 2,
            child: _buildPalaceDetails(),
          ),
        ],
      ),
    );
  }

  /// 构建命盘头部信息
  Widget _buildChartHeader() {
    // 计算当前年龄和大运
    int currentAge = DateTime.now().year - widget.chart.birthData.year;
    MajorPeriod? currentMajorPeriod = widget.chart.getCurrentMajorPeriod(currentAge);

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple[50],
        border: Border(bottom: BorderSide(color: Colors.purple[200]!)),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildInfoItem('姓名', widget.chart.name),
              _buildInfoItem('性别', widget.chart.gender == 'male' ? '男' : '女'),
              _buildInfoItem('命宫', widget.chart.lifePalace.name),
              _buildInfoItem('身宫', widget.chart.bodyPalace.name),
            ],
          ),
          SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildInfoItem('年柱', widget.chart.fourPillars['year'] ?? ''),
              _buildInfoItem('月柱', widget.chart.fourPillars['month'] ?? ''),
              _buildInfoItem('日柱', widget.chart.fourPillars['day'] ?? ''),
              _buildInfoItem('时柱', widget.chart.fourPillars['hour'] ?? ''),
            ],
          ),
          if (currentMajorPeriod != null) ...[
            SizedBox(height: 8),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.orange[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange[300]!),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildInfoItem('当前年龄', '$currentAge岁', isSmall: true),
                  _buildInfoItem('大运', '${currentMajorPeriod.palace.name}(${currentMajorPeriod.stemBranch})', isSmall: true),
                  _buildInfoItem('大运期间', '${currentMajorPeriod.startAge}-${currentMajorPeriod.endAge}岁', isSmall: true),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建信息项
  Widget _buildInfoItem(String label, String value, {bool isSmall = false}) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isSmall ? 10 : 12,
            color: Colors.grey[600],
          ),
        ),
        SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            fontSize: isSmall ? 12 : 14,
            fontWeight: FontWeight.bold,
            color: isSmall ? Colors.orange[700] : Colors.purple[700],
          ),
        ),
      ],
    );
  }

  /// 构建十二宫位圆盘
  Widget _buildPalaceCircle() {
    return Container(
      padding: EdgeInsets.all(16),
      child: AspectRatio(
        aspectRatio: 1.0,
        child: Stack(
          children: [
            // 圆形背景
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.purple[300]!, width: 2),
                color: Colors.white,
              ),
            ),
            
            // 十二宫位
            ...List.generate(12, (index) => _buildPalaceWidget(index)),
            
            // 中心信息
            Center(
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.purple[100],
                  border: Border.all(color: Colors.purple[400]!, width: 2),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '紫微斗数',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple[700],
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      widget.chart.name,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.purple[600],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建单个宫位组件
  Widget _buildPalaceWidget(int index) {
    ZiweiPalace palace = widget.chart.palaces[index];
    bool isSelected = index == _selectedPalaceIndex;
    bool isLifePalace = index == widget.chart.lifePalaceIndex;
    bool isBodyPalace = index == widget.chart.bodyPalaceIndex;
    
    // 计算宫位在圆形中的位置
    double angle = (index * 30 - 90) * 3.14159 / 180; // 从12点开始，顺时针
    double radius = 140;
    double x = radius * (1 + 0.7 * math.cos(angle));
    double y = radius * (1 + 0.7 * math.sin(angle));

    return Positioned(
      left: x - 40,
      top: y - 30,
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedPalaceIndex = index;
          });
        },
        child: Container(
          width: 80,
          height: 60,
          decoration: BoxDecoration(
            color: isSelected ? Colors.purple[200] : Colors.white,
            border: Border.all(
              color: isLifePalace ? Colors.red : 
                     isBodyPalace ? Colors.blue : Colors.purple[300]!,
              width: isLifePalace || isBodyPalace ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                palace.name,
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: isSelected ? Colors.purple[800] : Colors.purple[600],
                ),
              ),
              if (palace.stemBranch.isNotEmpty)
                Text(
                  palace.stemBranch,
                  style: TextStyle(
                    fontSize: 8,
                    color: Colors.grey[600],
                  ),
                ),
              if (palace.mainStars.isNotEmpty)
                Text(
                  palace.mainStars.first.name,
                  style: TextStyle(
                    fontSize: 8,
                    color: Colors.red[600],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              // 显示化曜标记
              if (palace.transformStars.isNotEmpty)
                Container(
                  margin: EdgeInsets.only(top: 1),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: palace.transformStars.take(2).map((star) => Container(
                      width: 8,
                      height: 8,
                      margin: EdgeInsets.symmetric(horizontal: 1),
                      decoration: BoxDecoration(
                        color: _getTransformColor(star.name),
                        shape: BoxShape.circle,
                      ),
                    )).toList(),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建宫位详情
  Widget _buildPalaceDetails() {
    ZiweiPalace selectedPalace = widget.chart.palaces[_selectedPalaceIndex];
    
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 宫位标题
          Row(
            children: [
              Text(
                selectedPalace.name,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.purple[700],
                ),
              ),
              SizedBox(width: 8),
              if (selectedPalace.stemBranch.isNotEmpty)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.purple[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    selectedPalace.stemBranch,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.purple[700],
                    ),
                  ),
                ),
              Spacer(),
              if (_selectedPalaceIndex == widget.chart.lifePalaceIndex)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.red[100],
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text('命宫', style: TextStyle(fontSize: 10, color: Colors.red[700])),
                ),
              if (_selectedPalaceIndex == widget.chart.bodyPalaceIndex)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.blue[100],
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text('身宫', style: TextStyle(fontSize: 10, color: Colors.blue[700])),
                ),
            ],
          ),
          
          SizedBox(height: 12),
          
          // 星曜列表
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (selectedPalace.mainStars.isNotEmpty) ...[
                    _buildStarSection('主星', selectedPalace.mainStars, Colors.red),
                    SizedBox(height: 8),
                  ],
                  if (selectedPalace.auxiliaryStars.isNotEmpty) ...[
                    _buildStarSection('辅星', selectedPalace.auxiliaryStars, Colors.blue),
                    SizedBox(height: 8),
                  ],
                  if (selectedPalace.miscellaneousStars.isNotEmpty) ...[
                    _buildStarSection('杂曜', selectedPalace.miscellaneousStars, Colors.green),
                    SizedBox(height: 8),
                  ],
                  if (selectedPalace.transformStars.isNotEmpty) ...[
                    _buildTransformStarSection('化曜', selectedPalace.transformStars),
                    SizedBox(height: 8),
                  ],
                  
                  // 宫位描述
                  Text(
                    '主管事项：',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[700],
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    selectedPalace.domains.join('、'),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),

                  SizedBox(height: 12),

                  // 宫位强度分析
                  Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '宫位分析：',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[700],
                          ),
                        ),
                        SizedBox(height: 4),
                        Row(
                          children: [
                            Text('宫位强度：', style: TextStyle(fontSize: 11)),
                            Container(
                              width: 60,
                              height: 8,
                              decoration: BoxDecoration(
                                color: Colors.grey[300],
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: FractionallySizedBox(
                                widthFactor: selectedPalace.strength / 10.0,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: _getStrengthColor(selectedPalace.strength),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 4),
                            Text(
                              '${selectedPalace.strength.toStringAsFixed(1)}',
                              style: TextStyle(fontSize: 11, fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                        SizedBox(height: 4),
                        Text(
                          '五行属性：${_getElementText(selectedPalace.element)}',
                          style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                        ),
                        if (selectedPalace.stars.isNotEmpty) ...[
                          SizedBox(height: 4),
                          Text(
                            '星曜总数：${selectedPalace.stars.length}颗',
                            style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建星曜分组
  Widget _buildStarSection(String title, List<ZiweiStar> stars, MaterialColor color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color[700],
          ),
        ),
        SizedBox(height: 4),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: stars.map((star) => _buildStarChip(star, color)).toList(),
        ),
      ],
    );
  }

  /// 构建星曜芯片（包含亮度信息）
  Widget _buildStarChip(ZiweiStar star, MaterialColor color) {
    String brightnessText = _getBrightnessText(star.brightness);
    Color brightnessColor = _getBrightnessColor(star.brightness);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color[50],
        border: Border.all(color: color[200]!),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            star.name,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color[700],
            ),
          ),
          if (star.brightness != null) ...[
            SizedBox(height: 2),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 4, vertical: 1),
              decoration: BoxDecoration(
                color: brightnessColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                brightnessText,
                style: TextStyle(
                  fontSize: 8,
                  color: brightnessColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 获取亮度文本
  String _getBrightnessText(StarBrightness? brightness) {
    switch (brightness) {
      case StarBrightness.temple:
        return '庙';
      case StarBrightness.prosperous:
        return '旺';
      case StarBrightness.favorable:
        return '得地';
      case StarBrightness.neutral:
        return '平';
      case StarBrightness.trapped:
        return '陷';
      case StarBrightness.fallen:
        return '落';
      default:
        return '';
    }
  }

  /// 获取亮度颜色
  Color _getBrightnessColor(StarBrightness? brightness) {
    switch (brightness) {
      case StarBrightness.temple:
        return Colors.red[700]!;
      case StarBrightness.prosperous:
        return Colors.orange[700]!;
      case StarBrightness.favorable:
        return Colors.green[700]!;
      case StarBrightness.neutral:
        return Colors.grey[600]!;
      case StarBrightness.trapped:
        return Colors.blue[700]!;
      case StarBrightness.fallen:
        return Colors.purple[700]!;
      default:
        return Colors.grey[500]!;
    }
  }

  /// 构建化曜星分组（特殊显示）
  Widget _buildTransformStarSection(String title, List<ZiweiStar> transformStars) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.orange[700],
          ),
        ),
        SizedBox(height: 4),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: transformStars.map((star) => _buildTransformStarChip(star)).toList(),
        ),
      ],
    );
  }

  /// 构建化曜星芯片
  Widget _buildTransformStarChip(ZiweiStar star) {
    Color transformColor = _getTransformColor(star.name);
    String transformSymbol = _getTransformSymbol(star.name);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: transformColor.withOpacity(0.1),
        border: Border.all(color: transformColor, width: 1.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: transformColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                transformSymbol,
                style: TextStyle(
                  fontSize: 8,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          SizedBox(width: 4),
          Text(
            star.name,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: transformColor,
            ),
          ),
        ],
      ),
    );
  }

  /// 获取化曜颜色
  Color _getTransformColor(String transformName) {
    switch (transformName) {
      case '化禄':
        return Colors.red[600]!;
      case '化权':
        return Colors.orange[600]!;
      case '化科':
        return Colors.green[600]!;
      case '化忌':
        return Colors.grey[700]!;
      default:
        return Colors.orange[600]!;
    }
  }

  /// 获取化曜符号
  String _getTransformSymbol(String transformName) {
    switch (transformName) {
      case '化禄':
        return '禄';
      case '化权':
        return '权';
      case '化科':
        return '科';
      case '化忌':
        return '忌';
      default:
        return '化';
    }
  }

  /// 获取强度颜色
  Color _getStrengthColor(double strength) {
    if (strength >= 8.0) return Colors.red[600]!;
    if (strength >= 6.0) return Colors.orange[600]!;
    if (strength >= 4.0) return Colors.green[600]!;
    if (strength >= 2.0) return Colors.blue[600]!;
    return Colors.grey[600]!;
  }

  /// 获取五行文本
  String _getElementText(StarElement element) {
    switch (element) {
      case StarElement.wood:
        return '木';
      case StarElement.fire:
        return '火';
      case StarElement.earth:
        return '土';
      case StarElement.metal:
        return '金';
      case StarElement.water:
        return '水';
    }
  }

  /// 显示命盘信息
  void _showChartInfo() {
    int currentAge = DateTime.now().year - widget.chart.birthData.year;
    MajorPeriod? currentMajorPeriod = widget.chart.getCurrentMajorPeriod(currentAge);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('命盘信息'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('姓名：${widget.chart.name}'),
              Text('性别：${widget.chart.gender == 'male' ? '男' : '女'}'),
              Text('出生时间：${widget.chart.birthData.year}年${widget.chart.birthData.month}月${widget.chart.birthData.day}日'),
              Text('命宫：${widget.chart.lifePalace.name}'),
              Text('身宫：${widget.chart.bodyPalace.name}'),
              SizedBox(height: 8),
              Text('四柱干支：'),
              Text('年柱：${widget.chart.fourPillars['year']}'),
              Text('月柱：${widget.chart.fourPillars['month']}'),
              Text('日柱：${widget.chart.fourPillars['day']}'),
              Text('时柱：${widget.chart.fourPillars['hour']}'),
              SizedBox(height: 8),
              Text('大运信息：', style: TextStyle(fontWeight: FontWeight.bold)),
              Text('当前年龄：$currentAge岁'),
              if (currentMajorPeriod != null) ...[
                Text('当前大运：${currentMajorPeriod.palace.name}(${currentMajorPeriod.stemBranch})'),
                Text('大运期间：${currentMajorPeriod.startAge}-${currentMajorPeriod.endAge}岁'),
                Text('大运描述：${currentMajorPeriod.description}'),
              ] else ...[
                Text('当前大运：未计算'),
              ],
              SizedBox(height: 8),
              Text('大运列表：', style: TextStyle(fontWeight: FontWeight.bold)),
              ...widget.chart.majorPeriods.take(5).map((period) => Padding(
                padding: EdgeInsets.only(left: 8, top: 2),
                child: Text(
                  '${period.startAge}-${period.endAge}岁：${period.palace.name}(${period.stemBranch})',
                  style: TextStyle(fontSize: 12),
                ),
              )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('关闭'),
          ),
        ],
      ),
    );
  }
}
