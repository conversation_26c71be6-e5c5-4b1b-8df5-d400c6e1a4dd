// lib/models/palace.dart
class Palace {
  final int index; // 0-11
  final String name; // 宫位名称：命宫、财帛宫等
  final String earthBranch; // 地支：子、丑、寅等
  final List<String> mainStars; // 主星列表
  final List<String> minorStars; // 辅星列表
  final List<String> transformStars; // 化星列表
  
  Palace({
    required this.index,
    required this.name,
    required this.earthBranch,
    required this.mainStars,
    required this.minorStars,
    required this.transformStars,
  });
  
  Map<String, dynamic> toJson() {
    return {
      'index': index,
      'name': name,
      'earthBranch': earthBranch,
      'mainStars': mainStars,
      'minorStars': minorStars,
      'transformStars': transformStars,
    };
  }
  
  factory Palace.fromJson(Map<String, dynamic> json) {
    return Palace(
      index: json['index'] as int,
      name: json['name'] as String,
      earthBranch: json['earthBranch'] as String,
      mainStars: List<String>.from(json['mainStars']),
      minorStars: List<String>.from(json['minorStars']),
      transformStars: List<String>.from(json['transformStars']),
    );
  }
}