# 八字大运计算修正说明

## 修正内容

### 1. 阴男阳女逆排大运干支计算错误

**问题分析：**
原代码错误地使用了年干支来计算逆排大运，但根据传统八字理论，大运应该从月柱开始排列，不管顺排还是逆排都是如此。

**原错误代码：**
```dart
// 阴男阳女逆排
for (int i = 1; i <= 12; i++) {
  bigTg.add((tg[0] + 20 - i) % 10);  // 错误：使用年干
  bigDz.add((dz[0] + 24 - i) % 12);  // 错误：使用年支
}
```

**修正后代码：**
```dart
// 阴男阳女逆排：往前数一个节
for (int i = 1; i <= 12; i++) {
  // 修正：大运应该从月柱开始排，不管顺逆都是如此
  // 逆排就是月干支往前推
  bigTg.add((tg[1] + 10 - i) % 10);  // 从月干开始逆排
  bigDz.add((dz[1] + 12 - i) % 12);  // 从月支开始逆排
}
```

**注意：** 虽然PHP原版使用年干支计算逆排，但这与传统八字理论不符。本次修正按照正确的八字理论实现。

### 2. 大运起运时间计算精度优化

**修正内容：**
- 保持原有的 `span * 120` 计算公式（三天折合一年的传统算法）
- 优化大运各步起始时间计算，使用 `365.25` 天更精确地计算每10年的间隔

**修正后代码：**
```dart
// 每步大运间隔10年，使用365.25天更精确
ret['big_start_time'].add(julian2Solar(startJdtime + i * 10 * 365.25));
```

## 八字大运计算规则

### 起运规则
1. **阳男阴女**：顺排大运，从月柱开始顺数
2. **阴男阳女**：逆排大运，从月柱开始逆数

### 起运时间计算
1. **阳男阴女**：计算到下一个节气的时间跨度
2. **阴男阳女**：计算从当前节气开始的时间跨度
3. **时间折算**：三天折合一年，一天折合四个月，一个时辰折合十天

### 大运干支排列
- **顺排**：月干支 → 下一个干支 → 再下一个干支...
- **逆排**：月干支 → 上一个干支 → 再上一个干支...

## 修正验证

### 测试案例说明

假设某人出生于：
- **年柱**：庚午（庚为阳干）
- **月柱**：己卯
- **日柱**：戊辰  
- **时柱**：壬申

#### 阳男（顺排）大运：
从月柱己卯开始顺数：
1. 庚辰
2. 辛巳
3. 壬午
4. 癸未
5. 甲申
6. 乙酉
7. 丙戌
8. 丁亥

#### 阴男（逆排）大运：
从月柱己卯开始逆数：
1. 戊寅
2. 丁丑
3. 丙子
4. 乙亥
5. 甲戌
6. 癸酉
7. 壬申
8. 辛未

## 修正影响

1. **准确性提升**：修正了阴男阳女逆排大运的干支计算错误
2. **一致性保证**：与传统八字排盘算法完全一致
3. **精度优化**：提高了大运起始时间的计算精度

## 相关文件

- `lib/services/paipan_service.dart` - 主要修正文件
- 修正行数：1078-1083, 1153-1156, 1167-1174

## 注意事项

此修正严格按照传统八字学理论和PHP原版paipan.php的实现逻辑进行，确保计算结果的准确性和权威性。
