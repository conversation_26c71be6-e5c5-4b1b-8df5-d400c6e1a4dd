import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../utils/calendar_converter.dart';
import '../utils/hour_calculator.dart';

class CustomTimeSelector extends StatefulWidget {
  final DateTime initialDate;
  final TimeOfDay initialTime;
  final bool isLunar;
  final bool isLeapMonth;

  const CustomTimeSelector({
    Key? key,
    required this.initialDate,
    required this.initialTime,
    required this.isLunar,
    required this.isLeapMonth,
  }) : super(key: key);

  @override
  _CustomTimeSelectorState createState() => _CustomTimeSelectorState();
}

class _CustomTimeSelectorState extends State<CustomTimeSelector> {
  late DateTime _selectedDate;
  late TimeOfDay _selectedTime;
  late bool _isLunar;
  late bool _isLeapMonth;
  String _selectedTab = '公历'; // 公历, 农历, 四柱
  
  // 滚轮控制器
  late FixedExtentScrollController _yearController;
  late FixedExtentScrollController _monthController;
  late FixedExtentScrollController _dayController;
  late FixedExtentScrollController _hourController;
  late FixedExtentScrollController _minuteController;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate;
    _selectedTime = widget.initialTime;
    _isLunar = widget.isLunar;
    _isLeapMonth = widget.isLeapMonth;

    // 根据是否为农历设置初始标签
    _selectedTab = _isLunar ? '农历' : '公历';

    // 初始化滚轮控制器
    _yearController = FixedExtentScrollController(initialItem: _selectedDate.year - 1900);
    _monthController = FixedExtentScrollController(initialItem: _selectedDate.month - 1);
    _dayController = FixedExtentScrollController(initialItem: _selectedDate.day - 1);
    _hourController = FixedExtentScrollController(initialItem: _selectedTime.hour);
    _minuteController = FixedExtentScrollController(initialItem: _selectedTime.minute);
  }

  @override
  void dispose() {
    _yearController.dispose();
    _monthController.dispose();
    _dayController.dispose();
    _hourController.dispose();
    _minuteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            // 顶部标签
            _buildTabBar(),
            SizedBox(height: 16),
            
            // 主要内容区域
            Expanded(
              child: Row(
                children: [
                  // 左侧数字选择器
                  Expanded(
                    flex: 3,
                    child: _buildNumberSelectors(),
                  ),
                  
                  // 分隔线
                  Container(
                    width: 1,
                    color: Colors.grey[300],
                    margin: EdgeInsets.symmetric(horizontal: 16),
                  ),
                  
                  // 右侧时辰显示
                  Expanded(
                    flex: 2,
                    child: _buildHourDisplay(),
                  ),
                ],
              ),
            ),
            
            // 底部按钮
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text('取消'),
                ),
                ElevatedButton(
                  onPressed: _confirmSelection,
                  child: Text('确定'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: ['公历', '农历', '四柱'].map((tab) {
          final isSelected = _selectedTab == tab;
          return Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedTab = tab;
                  if (tab == '农历') {
                    _isLunar = true;
                  } else if (tab == '公历') {
                    _isLunar = false;
                  }
                });
              },
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.blue : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  tab,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.black,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildNumberSelectors() {
    return Row(
      children: [
        // 年
        Expanded(child: _buildYearSelector()),
        // 月
        Expanded(child: _buildMonthSelector()),
        // 日
        Expanded(child: _buildDaySelector()),
        // 时
        Expanded(child: _buildHourSelector()),
        // 分
        Expanded(child: _buildMinuteSelector()),
      ],
    );
  }

  Widget _buildYearSelector() {
    return Column(
      children: [
        Text('年', style: TextStyle(fontSize: 12, color: Colors.grey)),
        Expanded(
          child: CupertinoPicker(
            scrollController: _yearController,
            itemExtent: 40,
            onSelectedItemChanged: (index) {
              final newYear = 1900 + index;
              setState(() {
                _selectedDate = DateTime(newYear, _selectedDate.month, _selectedDate.day);
              });
            },
            children: List.generate(201, (index) {
              final year = 1900 + index;
              return Center(child: Text('$year'));
            }),
          ),
        ),
      ],
    );
  }

  Widget _buildMonthSelector() {
    return Column(
      children: [
        Text('月', style: TextStyle(fontSize: 12, color: Colors.grey)),
        Expanded(
          child: CupertinoPicker(
            scrollController: _monthController,
            itemExtent: 40,
            onSelectedItemChanged: (index) {
              final newMonth = index + 1;
              // 确保日期有效
              final daysInNewMonth = DateTime(_selectedDate.year, newMonth + 1, 0).day;
              final newDay = _selectedDate.day > daysInNewMonth ? daysInNewMonth : _selectedDate.day;
              setState(() {
                _selectedDate = DateTime(_selectedDate.year, newMonth, newDay);
                // 更新日期选择器
                if (newDay != _selectedDate.day) {
                  _dayController.animateToItem(newDay - 1, duration: Duration(milliseconds: 200), curve: Curves.easeInOut);
                }
              });
            },
            children: List.generate(12, (index) {
              return Center(child: Text('${index + 1}'));
            }),
          ),
        ),
      ],
    );
  }

  Widget _buildDaySelector() {
    final daysInMonth = DateTime(_selectedDate.year, _selectedDate.month + 1, 0).day;
    return Column(
      children: [
        Text('日', style: TextStyle(fontSize: 12, color: Colors.grey)),
        Expanded(
          child: CupertinoPicker(
            scrollController: _dayController,
            itemExtent: 40,
            onSelectedItemChanged: (index) {
              final newDay = index + 1;
              setState(() {
                _selectedDate = DateTime(_selectedDate.year, _selectedDate.month, newDay);
              });
            },
            children: List.generate(daysInMonth, (index) {
              return Center(child: Text('${index + 1}'));
            }),
          ),
        ),
      ],
    );
  }

  Widget _buildHourSelector() {
    return Column(
      children: [
        Text('时', style: TextStyle(fontSize: 12, color: Colors.grey)),
        Expanded(
          child: CupertinoPicker(
            scrollController: _hourController,
            itemExtent: 40,
            onSelectedItemChanged: (index) {
              setState(() {
                _selectedTime = TimeOfDay(hour: index, minute: _selectedTime.minute);
              });
            },
            children: List.generate(24, (index) {
              return Center(child: Text('$index'));
            }),
          ),
        ),
      ],
    );
  }

  Widget _buildMinuteSelector() {
    return Column(
      children: [
        Text('分', style: TextStyle(fontSize: 12, color: Colors.grey)),
        Expanded(
          child: CupertinoPicker(
            scrollController: _minuteController,
            itemExtent: 40,
            onSelectedItemChanged: (index) {
              setState(() {
                _selectedTime = TimeOfDay(hour: _selectedTime.hour, minute: index);
              });
            },
            children: List.generate(60, (index) {
              return Center(child: Text('$index'));
            }),
          ),
        ),
      ],
    );
  }

  Widget _buildHourDisplay() {
    // 先计算时辰索引
    final currentTime = DateTime(2024, 1, 1, _selectedTime.hour, _selectedTime.minute);
    final hourIndex = HourCalculator.getHourIndexByMeanTime(currentTime);
    final hourName = HourCalculator.getHourName(hourIndex);
    final hourBranch = HourCalculator.getEarthlyBranch(hourIndex);

    // 时辰对应表
    final hourRanges = [
      {'name': '子时', 'range': '23:00-01:00', 'desc': '夜半'},
      {'name': '丑时', 'range': '01:00-03:00', 'desc': '鸡鸣'},
      {'name': '寅时', 'range': '03:00-05:00', 'desc': '平旦'},
      {'name': '卯时', 'range': '05:00-07:00', 'desc': '日出'},
      {'name': '辰时', 'range': '07:00-09:00', 'desc': '食时'},
      {'name': '巳时', 'range': '09:00-11:00', 'desc': '隅中'},
      {'name': '午时', 'range': '11:00-13:00', 'desc': '日中'},
      {'name': '未时', 'range': '13:00-15:00', 'desc': '日昳'},
      {'name': '申时', 'range': '15:00-17:00', 'desc': '哺时'},
      {'name': '酉时', 'range': '17:00-19:00', 'desc': '日入'},
      {'name': '戌时', 'range': '19:00-21:00', 'desc': '黄昏'},
      {'name': '亥时', 'range': '21:00-23:00', 'desc': '人定'},
    ];

    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 当前时间显示
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${_selectedTime.hour.toString().padLeft(2, '0')}时${_selectedTime.minute.toString().padLeft(2, '0')}分',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 4),
                Text(
                  '$hourName时 ($hourBranch)',
                  style: TextStyle(fontSize: 16, color: Colors.blue[700]),
                ),
              ],
            ),
          ),

          SizedBox(height: 16),

          // 时辰对照表
          Text('时辰对照', style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
          SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              itemCount: hourRanges.length,
              itemBuilder: (context, index) {
                final hour = hourRanges[index];
                final isCurrentHour = hour['name'] == '${hourName}时';
                return Container(
                  margin: EdgeInsets.only(bottom: 4),
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isCurrentHour ? Colors.orange[100] : null,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: [
                      Text(
                        hour['name']!,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: isCurrentHour ? FontWeight.bold : FontWeight.normal,
                          color: isCurrentHour ? Colors.orange[800] : Colors.black87,
                        ),
                      ),
                      SizedBox(width: 8),
                      Text(
                        hour['range']!,
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _confirmSelection() {
    Navigator.of(context).pop({
      'date': _selectedDate,
      'time': _selectedTime,
      'isLunar': _isLunar,
      'isLeapMonth': _isLeapMonth,
    });
  }
}
